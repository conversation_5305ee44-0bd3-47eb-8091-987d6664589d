ul.contact-history {
	margin: 0;
}

#poststuff #submitdiv .inside {
	margin: 0;
	padding: 0;
}

#poststuff table.form-table tr.contact-prop th {
	width: 25%;
}

table.message-main-fields th, table.message-main-fields td {
	font-size: 15px;
	text-align: left;
	padding: 8px 4px;
}

table.message-main-fields th {
	width: 20%;
	color: #555;
}

table.message-fields td {
	padding: 4px 14px 2px 7px;
}

table.message-fields td.field-title {
	font-weight: bold;
	width: 24%;
}

table.message-fields td.field-value p {
	overflow-wrap: anywhere;
}

table.message-fields td.field-value ul {
	margin: 0;
}

table.message-fields td.field-value li {
	list-style: disc;
	margin-left: 1em;
}

.tagsdiv {
	margin-top: inherit;
}

.tablenav .actions input.button {
	margin: 0 8px 0 0;
}

#misc-publishing-actions .dashicons-before::before {
	position: relative;
	top: -1px;
	margin-left: -1px;
	padding-right: 3px;
	vertical-align: top;
	color: #82878c;
}
