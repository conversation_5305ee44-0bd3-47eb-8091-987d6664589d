(()=>{"use strict";var e={n:t=>{var r=t&&t.__esModule?()=>t.default:()=>t;return e.d(r,{a:r}),r},d:(t,r)=>{for(var s in r)e.o(r,s)&&!e.o(t,s)&&Object.defineProperty(t,s,{enumerable:!0,get:r[s]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{setAllowTweetImage:()=>P,setAutoshareEnabled:()=>j,setErrorMessage:()=>v,setLoaded:()=>A,setOverriding:()=>S,setSaving:()=>y,setTweetAccounts:()=>L,setTweetText:()=>E});var r={};e.r(r),e.d(r,{getAllowTweetImage:()=>D,getAutoshareEnabled:()=>k,getErrorMessage:()=>N,getOverriding:()=>C,getSaving:()=>I,getTweetAccounts:()=>O,getTweetText:()=>M});const s=window.wp.element,a=window.wp.plugins,o=window.wp.editPost,i=window.wp.data,n=window.wp.components,l=window.wp.i18n,c="SET_AUTOSHARE_FOR_TWITTER_ENABLED",u="SET_ERROR_MESSAGE",d="SET_LOADED",h="SET_OVERRIDING",w="SET_SAVING",g="SET_TWEET_TEXT",m="SET_ALLOW_TWEET_IMAGE",p="SET_TWEET_ACCOUNTS",{enabled:_,allowTweetImage:f,tweetAccounts:x,customTweetBody:T}=adminAutoshareForTwitter,b={autoshareEnabled:!!_&&"0"!==_,errorMessage:"",loaded:!1,overriding:!!T,overrideLength:0,tweetText:T||"",allowTweetImage:!!f,tweetAccounts:x||[]};const j=e=>({type:c,autoshareEnabled:e}),v=e=>({type:u,errorMessage:e}),A=()=>({type:d}),S=e=>({type:h,overriding:e}),y=e=>({type:w,saving:e}),E=e=>({type:g,tweetText:e}),P=e=>({type:m,allowTweetImage:e}),L=e=>({type:p,tweetAccounts:e}),k=e=>e.autoshareEnabled,N=e=>e.errorMessage,C=e=>e.overriding,I=e=>e.saving,M=e=>e.tweetText,D=e=>e.allowTweetImage,O=e=>e.tweetAccounts||[],F="10up/autoshare",X=window.wp.primitives,R=window.ReactJSXRuntime,z=e=>(0,R.jsx)(X.SVG,{version:"1.1",xmlSpace:"preserve",width:256,height:256,viewBox:"0 0 256 256",children:(0,R.jsxs)("g",{style:{stroke:"none",strokeWidth:0,strokeDasharray:"none",strokeLinecap:"butt",strokeLinejoin:"miter",strokeMiterlimit:10,fill:"none",fillRule:"nonzero",opacity:1},children:[(0,R.jsx)("path",{d:"M45 90C20.147 90 0 69.853 0 45S20.147 0 45 0s45 20.147 45 45-20.147 45-45 45z",style:{stroke:"none",strokeWidth:1,strokeDasharray:"none",strokeLinecap:"butt",strokeLinejoin:"miter",strokeMiterlimit:10,fill:e,fillRule:"nonzero",opacity:1},transform:"matrix(2.33 0 0 2.33 22.612 22.612)"}),(0,R.jsx)("path",{d:"M17.884 19.496 38.925 47.63 17.751 70.504h4.765l18.538-20.027 14.978 20.027h16.217L50.024 40.788l19.708-21.291h-4.765L47.895 37.94 34.101 19.496H17.884zm7.008 3.51h7.45L65.24 66.993h-7.45L24.892 23.006z",style:{stroke:"none",strokeWidth:1,strokeDasharray:"none",strokeLinecap:"butt",strokeLinejoin:"miter",strokeMiterlimit:10,fill:"#fff",fillRule:"nonzero",opacity:1},transform:"matrix(2.33 0 0 2.33 22.612 22.612)"})]})}),B=z("#1B1C20"),U=z("#1DA1F2"),W=z("#787E88"),$=z("#D0494A"),G=z("#7FD051"),H=e=>{let t=B;return e&&(t="published"===e?G:"error"===e?$:B),(0,R.jsx)(n.Icon,{className:"autoshare-for-twitter-icon",icon:t,size:48})},K=window.wp.apiFetch;var q=e.n(K);const V=window.lodash,{enableAutoshareKey:J,errorText:Y,restUrl:Q,tweetBodyKey:Z,allowTweetImageKey:ee,tweetAccountsKey:te}=adminAutoshareForTwitter;function re(){const{tweetText:e}=(0,i.useSelect)((e=>({tweetText:e(F).getTweetText()}))),{setTweetText:t}=(0,i.useDispatch)(F);return[e,t]}function se(){const{autoshareEnabled:e}=(0,i.useSelect)((e=>({autoshareEnabled:e(F).getAutoshareEnabled()}))),{setAutoshareEnabled:t}=(0,i.useDispatch)(F);return[e,t]}function ae(){const{allowTweetImage:e}=(0,i.useSelect)((e=>({allowTweetImage:e(F).getAllowTweetImage()}))),{setAllowTweetImage:t}=(0,i.useDispatch)(F);return[e,t]}function oe(){const{tweetAccounts:e}=(0,i.useSelect)((e=>({tweetAccounts:e(F).getTweetAccounts()}))),{setTweetAccounts:t}=(0,i.useDispatch)(F);return[e,t]}function ie(){const{errorMessage:e}=(0,i.useSelect)((e=>({errorMessage:e(F).getErrorMessage()}))),{setErrorMessage:t}=(0,i.useDispatch)(F);return[e,t]}function ne(){const{imageId:e}=(0,i.useSelect)((e=>({imageId:e("core/editor").getEditedPostAttribute("featured_media")})));return e>0}function le(){const[e]=se(),[t]=ae(),[r]=oe(),[a]=re(),[,o]=ie(),[,n]=function(){const{saving:e}=(0,i.useSelect)((e=>({saving:e(F).getSaving()})));return[e,function(e){(0,i.dispatch)(F).setSaving(e),e?(0,i.dispatch)("core/editor").lockPostSaving():(0,i.dispatch)("core/editor").unlockPostSaving()}]}(),{hasFeaturedImage:c}=(0,i.useSelect)((e=>({hasFeaturedImage:e("core/editor").getEditedPostAttribute("featured_media")>0}))),u=(0,s.useCallback)((0,V.debounce)((async function(e,t,r,s){const a={};a[J]=e,a[Z]=t,a[ee]=r,a[te]=s||[];try{n(!0);const e=await q()({url:Q,data:a,method:"POST",parse:!1});if(!e.ok)throw e;await e.json(),o(""),n(!1)}catch(e){o(e.statusText?`${Y} ${e.status}: ${e.statusText}`:(0,l.__)("An error occurred.","autoshare-for-twitter")),n(!1)}}),250),[]);(0,s.useEffect)((()=>{u(e,a,t,r)}),[e,a,c,t,r,u])}const{siteUrl:ce,isLocalSite:ue,twitterURLLength:de}=adminAutoshareForTwitter;function he(){const e=e=>{if(!ue&&!isNaN(de))return Number(de);const t=e("core/editor").getPermalink();if(t)return t.length;const r=e("core/editor").getEditedPostAttribute("title");return r&&"rendered"in r?(ce+r.rendered).length:ce.length},{permalinkLength:t,maxLength:r}=(0,i.useSelect)((t=>({permalinkLength:e(t),maxLength:275-e(t)}))),[a,o]=re(),{tweetLength:c,overrideLengthClass:u}=(()=>{const e=t+a.length+5;return 280<=e?{tweetLength:(0,l.sprintf)(/* translators: %d is tweet message character count */ /* translators: %d is tweet message character count */
(0,l.__)("%d - Too Long!","autoshare-for-twitter"),e),overrideLengthClass:"over-limit"}:240<=e?{tweetLength:(0,l.sprintf)(/* translators: %d is tweet message character count */ /* translators: %d is tweet message character count */
(0,l.__)("%d - Getting Long!","autoshare-for-twitter"),e),overrideLengthClass:"near-limit"}:{tweetLength:`${e}`,overrideLengthClass:""}})(),d=(0,i.useSelect)((e=>e("core/editor").getEditedPostAttribute("status"))),[h,w]=(0,s.useState)("publish"===d);(0,s.useEffect)((()=>{"publish"!==d||h||(o(""),w(!0))}),[d,h]);const g=()=>(0,R.jsx)(n.Tooltip,{text:(0,l.__)("Count is inclusive of the post permalink which will be included in the final tweet.","autoshare-for-twitter"),children:(0,R.jsx)("div",{children:c})});return(0,R.jsx)(n.TextareaControl,{value:a,onChange:e=>{o(e)},className:"autoshare-for-twitter-tweet-text",maxLength:r,label:(0,R.jsxs)("span",{style:{marginTop:"0.5rem",display:"block"},className:"autoshare-for-twitter-prepublish__message-label",children:[(0,R.jsxs)("span",{children:[(0,l.__)("Custom message:","autoshare-for-twitter")," "]}),(0,R.jsx)("span",{id:"autoshare-for-twitter-counter-wrap",className:`alignright ${u}`,children:(0,R.jsx)(g,{})})]})})}const we=window.wp.date,{connectedAccounts:ge,connectAccountUrl:me}=adminAutoshareForTwitter,pe=(0,we.getSettings)();function _e(){const e=ge?Object.values(ge):[],[t]=e,[r]=oe();return(0,R.jsxs)("div",{className:"autoshare-for-twitter-accounts-wrapper",children:[e.map((e=>(0,R.jsx)(fe,{...e},e.id))),t&&r?.length>0&&(0,R.jsx)(Te,{...t}),r?.length>0&&(0,R.jsx)("div",{className:"autoshare-for-twitter-rate-monitor__disclaimer",children:(0,R.jsxs)("p",{children:[(0,R.jsx)("strong",{children:(0,l.__)("Note:","autoshare-for-twitter")})," ",(0,l.__)("The displayed API rate limits are updated only when a tweet is posted. Since there is no dedicated endpoint for real-time usage data, the information provided may not fully reflect the current API usage, especially if other tweets are made through the same app.","autoshare-for-twitter")]})}),(0,R.jsx)("span",{className:"connect-account-link",children:(0,R.jsx)(n.ExternalLink,{href:me,children:(0,l.__)("Connect an account","autoshare-for-twitter")})})]})}function fe(e){const[t,r]=oe(),{id:s,name:a,username:o,profile_image_url:i}=e;return(0,R.jsxs)(R.Fragment,{children:[(0,R.jsxs)("div",{className:"twitter-account-wrapper",children:[(0,R.jsx)("img",{src:i,alt:a,className:"twitter-account-profile-image"}),(0,R.jsxs)("span",{className:"account-details",children:[(0,R.jsxs)("strong",{children:["@",o]}),(0,R.jsx)("br",{}),a]}),(0,R.jsx)(n.ToggleControl,{checked:t&&t.includes(s),onChange:e=>{r(e?[...t,s]:t.filter((e=>e!==s)))},className:"autoshare-for-twitter-account-toggle"})]}),t&&t.includes(s)&&(0,R.jsx)(xe,{...e})]})}function xe({rate_limits:e}){return!e||!e.user_limit_24hour_limit||e?.user_limit_24hour_reset<Math.floor(Date.now()/1e3)?(0,R.jsx)("p",{children:(0,l.__)("No X/Twitter rate limit available yet. Make a post to X/Twitter first.","autoshare-for-twitter")}):(0,R.jsx)("div",{className:"autoshare-for-twitter-rate-monitor__user",children:(0,R.jsx)(be,{title:(0,l.__)("User 24-Hour Limit:","autoshare-for-twitter"),remaining:e?.user_limit_24hour_remaining,limit:e?.user_limit_24hour_limit,reset:e?.user_limit_24hour_reset,tooltip:(0,l.__)("The maximum number of requests a single user can make across all API endpoints within a 24-hour period.","autoshare-for-twitter")})})}function Te({rate_limits:e}){return!e||!e.app_limit_24hour_limit||e?.app_limit_24hour_reset<Math.floor(Date.now()/1e3)?null:(0,R.jsx)("div",{className:"autoshare-for-twitter-rate-monitor__app",children:(0,R.jsx)(be,{title:(0,l.__)("App 24-Hour Limit:","autoshare-for-twitter"),remaining:e?.app_limit_24hour_remaining,limit:e?.app_limit_24hour_limit,reset:e?.app_limit_24hour_reset,tooltip:(0,l.__)("The total number of API calls your app can make across all users within a 24-hour period.","autoshare-for-twitter")})})}function be({title:e,remaining:t,limit:r,reset:s,tooltip:a}){let o=(0,l.__)("N/A","autoshare-for-twitter");return s&&pe?.formats?.datetime&&(o=(0,we.dateI18n)(pe.formats.datetime,1e3*s),o=(0,l.sprintf)("Resets on %1$s",o)),(0,R.jsxs)("div",{className:"autoshare-for-twitter-rate-monitor__rate",children:[(0,R.jsxs)("p",{className:"autoshare-for-twitter-rate-monitor__rate-limit",children:[(0,R.jsx)(n.Tooltip,{text:a,children:(0,R.jsx)("strong",{children:e})})," ",(0,l.sprintf)(/* translators: %1$s: Remaining, %2$s: Limit */ /* translators: %1$s: Remaining, %2$s: Limit */
(0,l.__)("%1$s of %2$s requests remaining","autoshare-for-twitter"),null!=t?t:(0,l.__)("N/A","autoshare-for-twitter"),null!=r?r:(0,l.__)("N/A","autoshare-for-twitter"))]}),(0,R.jsx)("p",{className:"autoshare-for-twitter-rate-monitor__rate-reset",children:o})]})}const je=({errorMessage:e})=>(0,R.jsxs)("span",{children:[e," ",e?.includes("When authenticating requests to the Twitter API v2 endpoints, you must use keys and tokens from a Twitter developer App that is attached to a Project. You can create a project via the developer portal.")&&(0,R.jsx)(n.ExternalLink,{href:"https://developer.twitter.com/en/docs/twitter-api/migrate/ready-to-migrate",children:(0,l.__)("Learn more here.","autoshare-for-twitter")})]});function ve({messages:e}){return e&&e.message.length?(0,R.jsxs)("div",{className:"autoshare-for-twitter-post-status",children:[e.message.map(((e,t)=>{const r=H(e.status);return(0,R.jsxs)("div",{className:"autoshare-for-twitter-log",children:[r,(0,R.jsxs)("span",{children:[e.url?(0,R.jsx)(n.ExternalLink,{href:e.url,children:e.message}):(0,R.jsx)(je,{errorMessage:e.message}),!!e.handle&&(0,R.jsx)("strong",{children:" - @"+e.handle})]})]},t)})),(0,R.jsx)(n.CardDivider,{})]}):null}function Ae(){const[e,t]=se(),[r,s]=function(){const{overriding:e}=(0,i.useSelect)((e=>({overriding:e(F).getOverriding()}))),{setOverriding:t}=(0,i.useDispatch)(F);return[e,t]}(),[a,o]=ae(),[c]=ie(),u=ne(),d=(0,i.select)("core/editor").getCurrentPostAttribute("autoshare_for_twitter_status");return le(),(0,R.jsxs)(R.Fragment,{children:[(0,R.jsx)(ve,{messages:d}),(0,R.jsx)(n.ToggleControl,{label:e?(0,l.__)("Post to X/Twitter when published","autoshare-for-twitter"):(0,l.__)("Don't post to X/Twitter","autoshare-for-twitter"),checked:e,onChange:e=>{t(e)},className:"autoshare-for-twitter-toggle-control"}),e&&u&&(0,R.jsx)(n.ToggleControl,{label:(0,l.__)("Use featured image in Post to X/Twitter","autoshare-for-twitter"),checked:a,onChange:()=>{o(!a)},className:"autoshare-for-twitter-toggle-control"}),e&&(0,R.jsx)(_e,{}),e&&(0,R.jsxs)("div",{className:"autoshare-for-twitter-prepublish__override-row",children:[r&&(0,R.jsx)(he,{}),(0,R.jsx)(n.Button,{isLink:!0,onClick:()=>{s(!r)},children:r?(0,l.__)("Hide","autoshare-for-twitter"):(0,l.__)("Edit","autoshare-for-twitter")})]}),(0,R.jsx)("div",{children:c})]})}const Se=(0,window.wp.compose.compose)((0,i.withSelect)((e=>({statusMessage:e("core/editor").getCurrentPostAttribute("autoshare_for_twitter_status")}))))((function(){const e=ne(),[t,r]=ae(),[,a]=re(),[o,c]=(0,s.useState)(!1),[u,d]=(0,s.useState)(!1),{messages:h}=(0,i.useSelect)((e=>({messages:e("core/editor").getCurrentPostAttribute("autoshare_for_twitter_status")}))),[w,g]=(0,s.useState)(h);if(le(),w&&!w.message.length)return null;const m=(0,R.jsx)(n.Icon,{icon:(0,R.jsx)("svg",{viewBox:"0 0 28 28",xmlns:"http://www.w3.org/2000/svg",width:"28",height:"28","aria-hidden":"true",focusable:"false",children:(0,R.jsx)("path",{d:"M6.5 12.4L12 8l5.5 4.4-.9 1.2L12 10l-4.5 3.6-1-1.2z"})})}),p=(0,R.jsx)(n.Icon,{icon:(0,R.jsx)("svg",{viewBox:"0 0 28 28",xmlns:"http://www.w3.org/2000/svg",width:"28",height:"28","aria-hidden":"true",focusable:"false",children:(0,R.jsx)("path",{d:"M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"})})});return(0,R.jsxs)(R.Fragment,{children:[(0,R.jsx)(ve,{messages:w}),(0,R.jsx)(n.Button,{className:"autoshare-for-twitter-tweet-now",variant:"link",text:(0,l.__)("Post to X/Twitter now","autoshare-for-twitter"),onClick:()=>d(!u),iconPosition:"right",icon:u?m:p}),u&&(0,R.jsxs)(R.Fragment,{children:[e&&(0,R.jsx)(n.ToggleControl,{label:(0,l.__)("Use featured image in Post to X/Twitter","autoshare-for-twitter"),checked:t,onChange:()=>{r(!t)},className:"autoshare-for-twitter-toggle-control"}),(0,R.jsx)(_e,{}),(0,R.jsx)(he,{}),(0,R.jsx)(n.Button,{variant:"primary",className:"autoshare-for-twitter-re-tweet",text:o?(0,l.__)("Posting to X/Twitter…","autoshare-for-twitter"):(0,l.__)("Post to X/Twitter","autoshare-for-twitter"),onClick:()=>{(async()=>{c(!0);const e=await(0,i.select)("core/editor").getCurrentPostId(),t=new FormData;t.append("action",adminAutoshareForTwitter.retweetAction),t.append("nonce",adminAutoshareForTwitter.nonce),t.append("post_id",e);const r=await fetch(ajaxurl,{method:"POST",body:t}),{data:s}=await r.json();s.is_retweeted&&a(""),g(s),c(!1)})()}})]})]})}));(0,i.registerStore)(F,{reducer:function(e=b,t){switch(t.type){case c:return{...e,autoshareEnabled:t.autoshareEnabled};case u:return{...e,errorMessage:t.errorMessage};case d:return{...e,loaded:!0};case h:return{...e,overriding:t.overriding};case w:return{...e,saving:t.saving};case g:return{...e,tweetText:t.tweetText};case m:return{...e,allowTweetImage:t.allowTweetImage};case p:return{...e,tweetAccounts:t.tweetAccounts}}},actions:t,selectors:r});class ye extends s.Component{constructor(e){super(e),this.state={enabledText:""},this.maybeSetEnabledText=this.maybeSetEnabledText.bind(this)}componentDidMount(){(0,i.dispatch)(F).setLoaded(),(0,i.subscribe)(this.maybeSetEnabledText)}maybeSetEnabledText(){try{const e=(0,i.select)(F).getAutoshareEnabled(),t=e?(0,l.__)("This post will be posted to X/Twitter","autoshare-for-twitter"):(0,l.__)("This post will not be posted to X/Twitter","autoshare-for-twitter");t!==this.state.enabledText&&this.setState({enabled:e,enabledText:t})}catch(e){}}render(){const{enabled:e,enabledText:t}=this.state,r=e?U:W,s=(0,R.jsx)(n.Icon,{className:"autoshare-for-twitter-icon components-panel__icon",icon:r,size:24});return(0,R.jsx)(o.PluginPrePublishPanel,{title:t,icon:s,className:"autoshare-for-twitter-pre-publish-panel",children:(0,R.jsx)(Ae,{})})}}class Ee extends s.Component{constructor(e){super(e),this.state={enabledText:""},this.maybeSetEnabledText=this.maybeSetEnabledText.bind(this)}componentDidMount(){(0,i.dispatch)(F).setLoaded(),(0,i.subscribe)(this.maybeSetEnabledText)}maybeSetEnabledText(){try{const e=(0,i.select)(F).getAutoshareEnabled(),t=e?(0,l.__)("Autopost to X/Twitter enabled","autoshare-for-twitter"):(0,l.__)("Autopost to X/Twitter disabled","autoshare-for-twitter");t!==this.state.enabledText&&this.setState({enabledText:t,enabled:e})}catch(e){}}render(){if("publish"===(0,i.select)("core/editor").getCurrentPostAttribute("status")){const e=(0,i.select)("core/editor").getCurrentPostAttribute("autoshare_for_twitter_status");let t="";return e&&e.message&&e.message.length&&(t=e.message[e.message.length-1].status||""),(0,R.jsx)(o.PluginDocumentSettingPanel,{title:(0,l.__)("Autopost to X/Twitter","autoshare-for-twitter"),icon:H(t),className:"autoshare-for-twitter-editor-panel",children:(0,R.jsx)(Se,{})})}const{enabled:e,enabledText:t}=this.state,r=e?U:W,s=(0,R.jsx)(n.Icon,{className:"autoshare-for-twitter-icon components-panel__icon",icon:r,size:24});return(0,R.jsx)(o.PluginDocumentSettingPanel,{title:t,icon:s,className:"autoshare-for-twitter-editor-panel",children:(0,R.jsx)(Ae,{})})}}(0,a.registerPlugin)("autoshare-for-twitter-editor-panel",{render:Ee}),(0,a.registerPlugin)("autoshare-for-twitter-pre-publish-panel",{render:ye}),(0,a.registerPlugin)("autoshare-for-twitter-post-publish-panel",{render:()=>(0,R.jsx)(o.PluginPostPublishPanel,{className:"my-plugin-post-status-info",children:(0,R.jsx)(Se,{})})})})();