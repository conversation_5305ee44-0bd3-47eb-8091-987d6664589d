# Advanced Custom Fields Translations are a combination of translate.wordpress.org contributions,
# combined with user contributed strings for the PRO version.
# Translations from translate.wordpress.org take priority over translations in this file.
# translate.wordpress.org contributions are synced at the time of each release.
#
# If you would like to contribute translations, please visit
# https://translate.wordpress.org/projects/wp-plugins/advanced-custom-fields/stable/
#
# For additional ACF PRO strings, please submit a pull request over on the ACF GitHub repo at
# http://github.com/advancedcustomfields/acf using the .pot (and any existing .po) files in /lang/pro/
#
# This file is distributed under the same license as Advanced Custom Fields.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-08-12T11:23:47+00:00\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"Language: en_GB\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: gettext\n"
"Project-Id-Version: Advanced Custom Fields\n"

#: includes/fields/class-acf-field-date_picker.php:235
#: includes/fields/class-acf-field-date_time_picker.php:222
msgid "Use the current date as the default value for this field."
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:234
#: includes/fields/class-acf-field-date_time_picker.php:221
msgid "Default to the current date"
msgstr ""

#: includes/assets.php:363
msgid "Toggle panel"
msgstr ""

#. translators: %1$s - Plugin name, %2$s URL to documentation
#: includes/admin/admin.php:302
msgid ""
"%1$s We have detected that this website is configured to use v3 of the "
"Select2 jQuery library, which has been deprecated in favor of v4 and will be "
"removed in a future version of ACF. <a href=\"%2$s\" target=\"_blank\">Learn "
"more</a>."
msgstr ""

#: includes/fields/class-acf-field-select.php:471
msgid ""
"Save created options back to the \"Choices\" setting in the field definition."
msgstr ""

#: includes/fields/class-acf-field-select.php:470
msgid "Save Options"
msgstr ""

#: includes/fields/class-acf-field-select.php:448
msgid ""
"Allow content editors to create new options by typing in the Select input. "
"Multiple options can be created from a comma separated string."
msgstr ""

#: includes/fields/class-acf-field-select.php:447
msgid "Create Options"
msgstr ""

#: includes/admin/views/global/navigation.php:179
#: includes/admin/views/global/navigation.php:183
msgid "Edit ACF Field Groups"
msgstr ""

#: includes/admin/views/global/navigation.php:100
msgid "Get 4 months free on any WP Engine plan"
msgstr ""

#: src/Site_Health/Site_Health.php:528
msgid "Number of Field Groups with Blocks and Other Locations"
msgstr ""

#: src/Site_Health/Site_Health.php:523
msgid "Number of Field Groups with Multiple Block Locations"
msgstr ""

#: src/Site_Health/Site_Health.php:518
msgid "Number of Field Groups with a Single Block Location"
msgstr ""

#: src/Site_Health/Site_Health.php:487
msgid "All Location Rules"
msgstr ""

#: includes/validation.php:145
msgid "Learn more"
msgstr "Learn more"

#: includes/validation.php:134
msgid ""
"ACF was unable to perform validation because the provided nonce failed "
"verification."
msgstr ""
"ACF was unable to perform validation because the provided nonce failed "
"verification."

#: includes/validation.php:132
msgid ""
"ACF was unable to perform validation because no nonce was received by the "
"server."
msgstr ""
"ACF was unable to perform validation because no nonce was received by the "
"server."

#. translators: This text is prepended by a link to ACF's website, and appended
#. by a link to WP Engine's website.
#: includes/admin/admin.php:348
msgid "are developed and maintained by"
msgstr "are developed and maintained by"

#: src/Site_Health/Site_Health.php:295
msgid "Update Source"
msgstr "Update Source"

#: includes/admin/views/acf-post-type/advanced-settings.php:850
#: includes/admin/views/acf-taxonomy/advanced-settings.php:810
msgid "By default only admin users can edit this setting."
msgstr "By default, only admin users can edit this setting."

#: includes/admin/views/acf-post-type/advanced-settings.php:848
#: includes/admin/views/acf-taxonomy/advanced-settings.php:808
msgid "By default only super admin users can edit this setting."
msgstr "By default, only super admin users can edit this setting."

#: includes/admin/views/acf-field-group/field.php:322
msgid "Close and Add Field"
msgstr "Close and Add Field"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:804
msgid ""
"A PHP function name to be called to handle the content of a meta box on your "
"taxonomy. For security, this callback will be executed in a special context "
"without access to any superglobals like $_POST or $_GET."
msgstr ""
"A PHP function name to be called to handle the content of a meta box on your "
"taxonomy. For security, this callback will be executed in a special context "
"without access to any superglobals like $_POST or $_GET."

#: includes/admin/views/acf-post-type/advanced-settings.php:842
msgid ""
"A PHP function name to be called when setting up the meta boxes for the edit "
"screen. For security, this callback will be executed in a special context "
"without access to any superglobals like $_POST or $_GET."
msgstr ""
"A PHP function name to be called when setting up the meta boxes for the edit "
"screen. For security, this callback will be executed in a special context "
"without access to any superglobals like $_POST or $_GET."

#: src/Site_Health/Site_Health.php:296
msgid "wordpress.org"
msgstr "wordpress.org"

#: includes/fields/class-acf-field.php:364
msgid "Allow Access to Value in Editor UI"
msgstr "Allow Access to Value in Editor UI"

#: includes/fields/class-acf-field.php:346
msgid "Learn more."
msgstr "Learn more."

#. translators: %s A "Learn More" link to documentation explaining the setting
#. further.
#: includes/fields/class-acf-field.php:345
msgid ""
"Allow content editors to access and display the field value in the editor UI "
"using Block Bindings or the ACF Shortcode. %s"
msgstr ""
"Allow content editors to access and display the field value in the editor UI "
"using Block Bindings or the ACF Shortcode. %s"

#: src/Blocks/Bindings.php:67
msgid ""
"The requested ACF field type does not support output in Block Bindings or "
"the ACF shortcode."
msgstr ""
"The requested ACF field type does not support output in Block Bindings or "
"the ACF shortcode."

#: includes/api/api-template.php:1085 src/Blocks/Bindings.php:75
msgid ""
"The requested ACF field is not allowed to be output in bindings or the ACF "
"Shortcode."
msgstr ""
"The requested ACF field is not allowed to be output in bindings or the ACF "
"Shortcode."

#: includes/api/api-template.php:1077
msgid ""
"The requested ACF field type does not support output in bindings or the ACF "
"Shortcode."
msgstr ""
"The requested ACF field type does not support output in bindings or the ACF "
"Shortcode."

#: includes/api/api-template.php:1054
msgid "[The ACF shortcode cannot display fields from non-public posts]"
msgstr "[The ACF shortcode cannot display fields from non-public posts]"

#: includes/api/api-template.php:1011
msgid "[The ACF shortcode is disabled on this site]"
msgstr "[The ACF shortcode is disabled on this site]"

#: includes/fields/class-acf-field-icon_picker.php:483
msgid "Businessman Icon"
msgstr "Businessman Icon"

#: includes/fields/class-acf-field-icon_picker.php:475
msgid "Forums Icon"
msgstr "Forums Icon"

#: includes/fields/class-acf-field-icon_picker.php:754
msgid "YouTube Icon"
msgstr "YouTube Icon"

#: includes/fields/class-acf-field-icon_picker.php:753
msgid "Yes (alt) Icon"
msgstr "Yes (alt) Icon"

#: includes/fields/class-acf-field-icon_picker.php:751
msgid "Xing Icon"
msgstr "Xing Icon"

#: includes/fields/class-acf-field-icon_picker.php:750
msgid "WordPress (alt) Icon"
msgstr "WordPress (alt) Icon"

#: includes/fields/class-acf-field-icon_picker.php:748
msgid "WhatsApp Icon"
msgstr "WhatsApp Icon"

#: includes/fields/class-acf-field-icon_picker.php:747
msgid "Write Blog Icon"
msgstr "Write Blog Icon"

#: includes/fields/class-acf-field-icon_picker.php:746
msgid "Widgets Menus Icon"
msgstr "Widgets Menus Icon"

#: includes/fields/class-acf-field-icon_picker.php:745
msgid "View Site Icon"
msgstr "View Site Icon"

#: includes/fields/class-acf-field-icon_picker.php:744
msgid "Learn More Icon"
msgstr "Learn More Icon"

#: includes/fields/class-acf-field-icon_picker.php:742
msgid "Add Page Icon"
msgstr "Add Page Icon"

#: includes/fields/class-acf-field-icon_picker.php:739
msgid "Video (alt3) Icon"
msgstr "Video (alt3) Icon"

#: includes/fields/class-acf-field-icon_picker.php:738
msgid "Video (alt2) Icon"
msgstr "Video (alt2) Icon"

#: includes/fields/class-acf-field-icon_picker.php:737
msgid "Video (alt) Icon"
msgstr "Video (alt) Icon"

#: includes/fields/class-acf-field-icon_picker.php:734
msgid "Update (alt) Icon"
msgstr "Update (alt) Icon"

#: includes/fields/class-acf-field-icon_picker.php:731
msgid "Universal Access (alt) Icon"
msgstr "Universal Access (alt) Icon"

#: includes/fields/class-acf-field-icon_picker.php:728
msgid "Twitter (alt) Icon"
msgstr "Twitter (alt) Icon"

#: includes/fields/class-acf-field-icon_picker.php:726
msgid "Twitch Icon"
msgstr "Twitch Icon"

#: includes/fields/class-acf-field-icon_picker.php:723
msgid "Tide Icon"
msgstr "Tide Icon"

#: includes/fields/class-acf-field-icon_picker.php:722
msgid "Tickets (alt) Icon"
msgstr "Tickets (alt) Icon"

#: includes/fields/class-acf-field-icon_picker.php:718
msgid "Text Page Icon"
msgstr "Text Page Icon"

#: includes/fields/class-acf-field-icon_picker.php:712
msgid "Table Row Delete Icon"
msgstr "Table Row Delete Icon"

#: includes/fields/class-acf-field-icon_picker.php:711
msgid "Table Row Before Icon"
msgstr "Table Row Before Icon"

#: includes/fields/class-acf-field-icon_picker.php:710
msgid "Table Row After Icon"
msgstr "Table Row After Icon"

#: includes/fields/class-acf-field-icon_picker.php:709
msgid "Table Col Delete Icon"
msgstr "Table Col Delete Icon"

#: includes/fields/class-acf-field-icon_picker.php:708
msgid "Table Col Before Icon"
msgstr "Table Col Before Icon"

#: includes/fields/class-acf-field-icon_picker.php:707
msgid "Table Col After Icon"
msgstr "Table Col After Icon"

#: includes/fields/class-acf-field-icon_picker.php:706
msgid "Superhero (alt) Icon"
msgstr "Superhero (alt) Icon"

#: includes/fields/class-acf-field-icon_picker.php:705
msgid "Superhero Icon"
msgstr "Superhero Icon"

#: includes/fields/class-acf-field-icon_picker.php:699
msgid "Spotify Icon"
msgstr "Spotify Icon"

#: includes/fields/class-acf-field-icon_picker.php:693
msgid "Shortcode Icon"
msgstr "Shortcode Icon"

#: includes/fields/class-acf-field-icon_picker.php:692
msgid "Shield (alt) Icon"
msgstr "Shield (alt) Icon"

#: includes/fields/class-acf-field-icon_picker.php:690
msgid "Share (alt2) Icon"
msgstr "Share (alt2) Icon"

#: includes/fields/class-acf-field-icon_picker.php:689
msgid "Share (alt) Icon"
msgstr "Share (alt) Icon"

#: includes/fields/class-acf-field-icon_picker.php:684
msgid "Saved Icon"
msgstr "Saved Icon"

#: includes/fields/class-acf-field-icon_picker.php:683
msgid "RSS Icon"
msgstr "RSS Icon"

#: includes/fields/class-acf-field-icon_picker.php:682
msgid "REST API Icon"
msgstr "REST API Icon"

#: includes/fields/class-acf-field-icon_picker.php:681
msgid "Remove Icon"
msgstr "Remove Icon"

#: includes/fields/class-acf-field-icon_picker.php:679
msgid "Reddit Icon"
msgstr "Reddit Icon"

#: includes/fields/class-acf-field-icon_picker.php:676
msgid "Privacy Icon"
msgstr "Privacy Icon"

#: includes/fields/class-acf-field-icon_picker.php:675
msgid "Printer Icon"
msgstr "Printer Icon"

#: includes/fields/class-acf-field-icon_picker.php:671
msgid "Podio Icon"
msgstr "Podio Icon"

#: includes/fields/class-acf-field-icon_picker.php:670
msgid "Plus (alt2) Icon"
msgstr "Plus (alt2) Icon"

#: includes/fields/class-acf-field-icon_picker.php:669
msgid "Plus (alt) Icon"
msgstr "Plus (alt) Icon"

#: includes/fields/class-acf-field-icon_picker.php:667
msgid "Plugins Checked Icon"
msgstr "Plugins Checked Icon"

#: includes/fields/class-acf-field-icon_picker.php:664
msgid "Pinterest Icon"
msgstr "Pinterest Icon"

#: includes/fields/class-acf-field-icon_picker.php:662
msgid "Pets Icon"
msgstr "Pets Icon"

#: includes/fields/class-acf-field-icon_picker.php:660
msgid "PDF Icon"
msgstr "PDF Icon"

#: includes/fields/class-acf-field-icon_picker.php:658
msgid "Palm Tree Icon"
msgstr "Palm Tree Icon"

#: includes/fields/class-acf-field-icon_picker.php:657
msgid "Open Folder Icon"
msgstr "Open Folder Icon"

#: includes/fields/class-acf-field-icon_picker.php:656
msgid "No (alt) Icon"
msgstr "No (alt) Icon"

#: includes/fields/class-acf-field-icon_picker.php:651
msgid "Money (alt) Icon"
msgstr "Money (alt) Icon"

#: includes/fields/class-acf-field-icon_picker.php:646
msgid "Menu (alt3) Icon"
msgstr "Menu (alt3) Icon"

#: includes/fields/class-acf-field-icon_picker.php:645
msgid "Menu (alt2) Icon"
msgstr "Menu (alt2) Icon"

#: includes/fields/class-acf-field-icon_picker.php:644
msgid "Menu (alt) Icon"
msgstr "Menu (alt) Icon"

#: includes/fields/class-acf-field-icon_picker.php:639
msgid "Spreadsheet Icon"
msgstr "Spreadsheet Icon"

#: includes/fields/class-acf-field-icon_picker.php:638
msgid "Interactive Icon"
msgstr "Interactive Icon"

#: includes/fields/class-acf-field-icon_picker.php:637
msgid "Document Icon"
msgstr "Document Icon"

#: includes/fields/class-acf-field-icon_picker.php:636
msgid "Default Icon"
msgstr "Default Icon"

#: includes/fields/class-acf-field-icon_picker.php:630
msgid "Location (alt) Icon"
msgstr "Location (alt) Icon"

#: includes/fields/class-acf-field-icon_picker.php:627
msgid "LinkedIn Icon"
msgstr "LinkedIn Icon"

#: includes/fields/class-acf-field-icon_picker.php:622
msgid "Instagram Icon"
msgstr "Instagram Icon"

#: includes/fields/class-acf-field-icon_picker.php:621
msgid "Insert Before Icon"
msgstr "Insert Before Icon"

#: includes/fields/class-acf-field-icon_picker.php:620
msgid "Insert After Icon"
msgstr "Insert After Icon"

#: includes/fields/class-acf-field-icon_picker.php:619
msgid "Insert Icon"
msgstr "Insert Icon"

#: includes/fields/class-acf-field-icon_picker.php:618
msgid "Info Outline Icon"
msgstr "Info Outline Icon"

#: includes/fields/class-acf-field-icon_picker.php:615
msgid "Images (alt2) Icon"
msgstr "Images (alt2) Icon"

#: includes/fields/class-acf-field-icon_picker.php:614
msgid "Images (alt) Icon"
msgstr "Images (alt) Icon"

#: includes/fields/class-acf-field-icon_picker.php:613
msgid "Rotate Right Icon"
msgstr "Rotate Right Icon"

#: includes/fields/class-acf-field-icon_picker.php:612
msgid "Rotate Left Icon"
msgstr "Rotate Left Icon"

#: includes/fields/class-acf-field-icon_picker.php:611
msgid "Rotate Icon"
msgstr "Rotate Icon"

#: includes/fields/class-acf-field-icon_picker.php:610
msgid "Flip Vertical Icon"
msgstr "Flip Vertical Icon"

#: includes/fields/class-acf-field-icon_picker.php:609
msgid "Flip Horizontal Icon"
msgstr "Flip Horizontal Icon"

#: includes/fields/class-acf-field-icon_picker.php:607
msgid "Crop Icon"
msgstr "Crop Icon"

#: includes/fields/class-acf-field-icon_picker.php:606
msgid "ID (alt) Icon"
msgstr "ID (alt) icon"

#: includes/fields/class-acf-field-icon_picker.php:604
msgid "HTML Icon"
msgstr "HTML Icon"

#: includes/fields/class-acf-field-icon_picker.php:603
msgid "Hourglass Icon"
msgstr "Hourglass Icon"

#: includes/fields/class-acf-field-icon_picker.php:600
msgid "Heading Icon"
msgstr "Heading Icon"

#: includes/fields/class-acf-field-icon_picker.php:596
msgid "Google Icon"
msgstr "Google Icon"

#: includes/fields/class-acf-field-icon_picker.php:595
msgid "Games Icon"
msgstr "Games Icon"

#: includes/fields/class-acf-field-icon_picker.php:594
msgid "Fullscreen Exit (alt) Icon"
msgstr "Fullscreen Exit (alt) Icon"

#: includes/fields/class-acf-field-icon_picker.php:593
msgid "Fullscreen (alt) Icon"
msgstr "Fullscreen (alt) Icon"

#: includes/fields/class-acf-field-icon_picker.php:590
msgid "Status Icon"
msgstr "Status Icon"

#: includes/fields/class-acf-field-icon_picker.php:588
msgid "Image Icon"
msgstr "Image Icon"

#: includes/fields/class-acf-field-icon_picker.php:587
msgid "Gallery Icon"
msgstr "Gallery Icon"

#: includes/fields/class-acf-field-icon_picker.php:586
msgid "Chat Icon"
msgstr "Chat Icon"

#: includes/fields/class-acf-field-icon_picker.php:585
#: includes/fields/class-acf-field-icon_picker.php:634
msgid "Audio Icon"
msgstr "Audio Icon"

#: includes/fields/class-acf-field-icon_picker.php:584
msgid "Aside Icon"
msgstr "Aside Icon"

#: includes/fields/class-acf-field-icon_picker.php:583
msgid "Food Icon"
msgstr "Food Icon"

#: includes/fields/class-acf-field-icon_picker.php:576
msgid "Exit Icon"
msgstr "Exit Icon"

#: includes/fields/class-acf-field-icon_picker.php:575
msgid "Excerpt View Icon"
msgstr "Excerpt View Icon"

#: includes/fields/class-acf-field-icon_picker.php:574
msgid "Embed Video Icon"
msgstr "Embed Video Icon"

#: includes/fields/class-acf-field-icon_picker.php:573
msgid "Embed Post Icon"
msgstr "Embed Post Icon"

#: includes/fields/class-acf-field-icon_picker.php:572
msgid "Embed Photo Icon"
msgstr "Embed Photo Icon"

#: includes/fields/class-acf-field-icon_picker.php:571
msgid "Embed Generic Icon"
msgstr "Embed Generic Icon"

#: includes/fields/class-acf-field-icon_picker.php:570
msgid "Embed Audio Icon"
msgstr "Embed Audio Icon"

#: includes/fields/class-acf-field-icon_picker.php:569
msgid "Email (alt2) Icon"
msgstr "Email (alt2) Icon"

#: includes/fields/class-acf-field-icon_picker.php:566
msgid "Ellipsis Icon"
msgstr "Ellipsis Icon"

#: includes/fields/class-acf-field-icon_picker.php:562
msgid "Unordered List Icon"
msgstr "Unordered List Icon"

#: includes/fields/class-acf-field-icon_picker.php:557
msgid "RTL Icon"
msgstr "RTL Icon"

#: includes/fields/class-acf-field-icon_picker.php:550
msgid "Ordered List RTL Icon"
msgstr "Ordered List RTL Icon"

#: includes/fields/class-acf-field-icon_picker.php:549
msgid "Ordered List Icon"
msgstr "Ordered List Icon"

#: includes/fields/class-acf-field-icon_picker.php:548
msgid "LTR Icon"
msgstr "LTR Icon"

#: includes/fields/class-acf-field-icon_picker.php:540
msgid "Custom Character Icon"
msgstr "Custom Character Icon"

#: includes/fields/class-acf-field-icon_picker.php:532
msgid "Edit Page Icon"
msgstr "Edit Page Icon"

#: includes/fields/class-acf-field-icon_picker.php:531
msgid "Edit Large Icon"
msgstr "Edit Large Icon"

#: includes/fields/class-acf-field-icon_picker.php:529
msgid "Drumstick Icon"
msgstr "Drumstick Icon"

#: includes/fields/class-acf-field-icon_picker.php:525
msgid "Database View Icon"
msgstr "Database View Icon"

#: includes/fields/class-acf-field-icon_picker.php:524
msgid "Database Remove Icon"
msgstr "Database Remove Icon"

#: includes/fields/class-acf-field-icon_picker.php:523
msgid "Database Import Icon"
msgstr "Database Import Icon"

#: includes/fields/class-acf-field-icon_picker.php:522
msgid "Database Export Icon"
msgstr "Database Export Icon"

#: includes/fields/class-acf-field-icon_picker.php:521
msgid "Database Add Icon"
msgstr "Database Add Icon"

#: includes/fields/class-acf-field-icon_picker.php:520
msgid "Database Icon"
msgstr "Database Icon"

#: includes/fields/class-acf-field-icon_picker.php:518
msgid "Cover Image Icon"
msgstr "Cover Image Icon"

#: includes/fields/class-acf-field-icon_picker.php:517
msgid "Volume On Icon"
msgstr "Volume On Icon"

#: includes/fields/class-acf-field-icon_picker.php:516
msgid "Volume Off Icon"
msgstr "Volume Off Icon"

#: includes/fields/class-acf-field-icon_picker.php:515
msgid "Skip Forward Icon"
msgstr "Skip Forward Icon"

#: includes/fields/class-acf-field-icon_picker.php:514
msgid "Skip Back Icon"
msgstr "Skip Back Icon"

#: includes/fields/class-acf-field-icon_picker.php:513
msgid "Repeat Icon"
msgstr "Repeat Icon"

#: includes/fields/class-acf-field-icon_picker.php:512
msgid "Play Icon"
msgstr "Play Icon"

#: includes/fields/class-acf-field-icon_picker.php:511
msgid "Pause Icon"
msgstr "Pause Icon"

#: includes/fields/class-acf-field-icon_picker.php:510
msgid "Forward Icon"
msgstr "Forward Icon"

#: includes/fields/class-acf-field-icon_picker.php:509
msgid "Back Icon"
msgstr "Back Icon"

#: includes/fields/class-acf-field-icon_picker.php:508
msgid "Columns Icon"
msgstr "Columns Icon"

#: includes/fields/class-acf-field-icon_picker.php:507
msgid "Color Picker Icon"
msgstr "Colour Picker Icon"

#: includes/fields/class-acf-field-icon_picker.php:506
msgid "Coffee Icon"
msgstr "Coffee Icon"

#: includes/fields/class-acf-field-icon_picker.php:505
msgid "Code Standards Icon"
msgstr "Code Standards Icon"

#: includes/fields/class-acf-field-icon_picker.php:504
msgid "Cloud Upload Icon"
msgstr "Cloud Upload Icon"

#: includes/fields/class-acf-field-icon_picker.php:503
msgid "Cloud Saved Icon"
msgstr "Cloud Saved Icon"

#: includes/fields/class-acf-field-icon_picker.php:492
msgid "Car Icon"
msgstr "Car Icon"

#: includes/fields/class-acf-field-icon_picker.php:491
msgid "Camera (alt) Icon"
msgstr "Camera (alt) Icon"

#: includes/fields/class-acf-field-icon_picker.php:487
msgid "Calculator Icon"
msgstr "Calculator Icon"

#: includes/fields/class-acf-field-icon_picker.php:486
msgid "Button Icon"
msgstr "Button Icon"

#: includes/fields/class-acf-field-icon_picker.php:484
msgid "Businessperson Icon"
msgstr "Businessperson Icon"

#: includes/fields/class-acf-field-icon_picker.php:481
msgid "Tracking Icon"
msgstr "Tracking Icon"

#: includes/fields/class-acf-field-icon_picker.php:480
msgid "Topics Icon"
msgstr "Topics Icon"

#: includes/fields/class-acf-field-icon_picker.php:479
msgid "Replies Icon"
msgstr "Replies Icon"

#: includes/fields/class-acf-field-icon_picker.php:478
msgid "PM Icon"
msgstr "PM icon"

#: includes/fields/class-acf-field-icon_picker.php:476
msgid "Friends Icon"
msgstr "Friends Icon"

#: includes/fields/class-acf-field-icon_picker.php:474
msgid "Community Icon"
msgstr "Community Icon"

#: includes/fields/class-acf-field-icon_picker.php:473
msgid "BuddyPress Icon"
msgstr "BuddyPress Icon"

#: includes/fields/class-acf-field-icon_picker.php:472
msgid "bbPress Icon"
msgstr "bbPress icon"

#: includes/fields/class-acf-field-icon_picker.php:471
msgid "Activity Icon"
msgstr "Activity Icon"

#: includes/fields/class-acf-field-icon_picker.php:470
msgid "Book (alt) Icon"
msgstr "Book (alt) Icon"

#: includes/fields/class-acf-field-icon_picker.php:468
msgid "Block Default Icon"
msgstr "Block Default Icon"

#: includes/fields/class-acf-field-icon_picker.php:467
msgid "Bell Icon"
msgstr "Bell Icon"

#: includes/fields/class-acf-field-icon_picker.php:466
msgid "Beer Icon"
msgstr "Beer Icon"

#: includes/fields/class-acf-field-icon_picker.php:465
msgid "Bank Icon"
msgstr "Bank Icon"

#: includes/fields/class-acf-field-icon_picker.php:461
msgid "Arrow Up (alt2) Icon"
msgstr "Arrow Up (alt2) Icon"

#: includes/fields/class-acf-field-icon_picker.php:460
msgid "Arrow Up (alt) Icon"
msgstr "Arrow Up (alt) Icon"

#: includes/fields/class-acf-field-icon_picker.php:458
msgid "Arrow Right (alt2) Icon"
msgstr "Arrow Right (alt2) Icon"

#: includes/fields/class-acf-field-icon_picker.php:457
msgid "Arrow Right (alt) Icon"
msgstr "Arrow Right (alt) Icon"

#: includes/fields/class-acf-field-icon_picker.php:455
msgid "Arrow Left (alt2) Icon"
msgstr "Arrow Left (alt2) Icon"

#: includes/fields/class-acf-field-icon_picker.php:454
msgid "Arrow Left (alt) Icon"
msgstr "Arrow Left (alt) Icon"

#: includes/fields/class-acf-field-icon_picker.php:452
msgid "Arrow Down (alt2) Icon"
msgstr "Arrow Down (alt2) Icon"

#: includes/fields/class-acf-field-icon_picker.php:451
msgid "Arrow Down (alt) Icon"
msgstr "Arrow Down (alt) Icon"

#: includes/fields/class-acf-field-icon_picker.php:447
msgid "Amazon Icon"
msgstr "Amazon Icon"

#: includes/fields/class-acf-field-icon_picker.php:446
msgid "Align Wide Icon"
msgstr "Align Wide Icon"

#: includes/fields/class-acf-field-icon_picker.php:444
msgid "Align Pull Right Icon"
msgstr "Align Pull Right Icon"

#: includes/fields/class-acf-field-icon_picker.php:443
msgid "Align Pull Left Icon"
msgstr "Align Pull Left Icon"

#: includes/fields/class-acf-field-icon_picker.php:440
msgid "Align Full Width Icon"
msgstr "Align Full Width Icon"

#: includes/fields/class-acf-field-icon_picker.php:437
msgid "Airplane Icon"
msgstr "Aeroplane Icon"

#: includes/fields/class-acf-field-icon_picker.php:434
msgid "Site (alt3) Icon"
msgstr "Site (alt3) Icon"

#: includes/fields/class-acf-field-icon_picker.php:433
msgid "Site (alt2) Icon"
msgstr "Site (alt2) Icon"

#: includes/fields/class-acf-field-icon_picker.php:432
msgid "Site (alt) Icon"
msgstr "Site (alt) Icon"

#: includes/admin/views/options-page-preview.php:26
msgid "Upgrade to ACF PRO to create options pages in just a few clicks"
msgstr "Upgrade to ACF PRO to create options pages in just a few clicks"

#: includes/ajax/class-acf-ajax-query-users.php:24
msgid "Invalid request args."
msgstr "Invalid request args."

#: includes/ajax/class-acf-ajax-check-screen.php:37
#: includes/ajax/class-acf-ajax-local-json-diff.php:37
#: includes/ajax/class-acf-ajax-query-users.php:33
#: includes/ajax/class-acf-ajax-upgrade.php:24
#: includes/ajax/class-acf-ajax-user-setting.php:38
msgid "Sorry, you do not have permission to do that."
msgstr "Sorry, you do not have permission to do that."

#: src/Site_Health/Site_Health.php:726
msgid "Blocks Using Post Meta"
msgstr "Blocks Using Post Meta"

#: includes/admin/views/acf-field-group/pro-features.php:25
#: includes/admin/views/acf-field-group/pro-features.php:27
#: includes/admin/views/global/header.php:27
msgid "ACF PRO logo"
msgstr "ACF PRO logo"

#: includes/admin/views/acf-field-group/field.php:37
msgid "ACF PRO Logo"
msgstr "ACF PRO Logo"

#. translators: %s - field/param name
#: includes/fields/class-acf-field-icon_picker.php:820
msgid "%s requires a valid attachment ID when type is set to media_library."
msgstr "%s requires a valid attachment ID when type is set to media_library."

#. translators: %s - field name
#: includes/fields/class-acf-field-icon_picker.php:804
msgid "%s is a required property of acf."
msgstr "%s is a required property of acf."

#: includes/fields/class-acf-field-icon_picker.php:780
msgid "The value of icon to save."
msgstr "The value of icon to save."

#: includes/fields/class-acf-field-icon_picker.php:774
msgid "The type of icon to save."
msgstr "The type of icon to save."

#: includes/fields/class-acf-field-icon_picker.php:752
msgid "Yes Icon"
msgstr "Yes icon"

#: includes/fields/class-acf-field-icon_picker.php:749
msgid "WordPress Icon"
msgstr "WordPress icon"

#: includes/fields/class-acf-field-icon_picker.php:741
msgid "Warning Icon"
msgstr "Warning icon"

#: includes/fields/class-acf-field-icon_picker.php:740
msgid "Visibility Icon"
msgstr "Visibility icon"

#: includes/fields/class-acf-field-icon_picker.php:736
msgid "Vault Icon"
msgstr "Vault icon"

#: includes/fields/class-acf-field-icon_picker.php:735
msgid "Upload Icon"
msgstr "Upload icon"

#: includes/fields/class-acf-field-icon_picker.php:733
msgid "Update Icon"
msgstr "Update icon"

#: includes/fields/class-acf-field-icon_picker.php:732
msgid "Unlock Icon"
msgstr "Unlock icon"

#: includes/fields/class-acf-field-icon_picker.php:730
msgid "Universal Access Icon"
msgstr "Universal access icon"

#: includes/fields/class-acf-field-icon_picker.php:729
msgid "Undo Icon"
msgstr "Undo icon"

#: includes/fields/class-acf-field-icon_picker.php:727
msgid "Twitter Icon"
msgstr "X icon"

#: includes/fields/class-acf-field-icon_picker.php:725
msgid "Trash Icon"
msgstr "Bin icon"

#: includes/fields/class-acf-field-icon_picker.php:724
msgid "Translation Icon"
msgstr "Translation icon"

#: includes/fields/class-acf-field-icon_picker.php:721
msgid "Tickets Icon"
msgstr "Tickets icon"

#: includes/fields/class-acf-field-icon_picker.php:720
msgid "Thumbs Up Icon"
msgstr "Thumbs up icon"

#: includes/fields/class-acf-field-icon_picker.php:719
msgid "Thumbs Down Icon"
msgstr "Thumbs down icon"

#: includes/fields/class-acf-field-icon_picker.php:640
#: includes/fields/class-acf-field-icon_picker.php:717
msgid "Text Icon"
msgstr "Text icon"

#: includes/fields/class-acf-field-icon_picker.php:716
msgid "Testimonial Icon"
msgstr "Testimonial icon"

#: includes/fields/class-acf-field-icon_picker.php:715
msgid "Tagcloud Icon"
msgstr "Tag cloud icon"

#: includes/fields/class-acf-field-icon_picker.php:714
msgid "Tag Icon"
msgstr "Tag icon"

#: includes/fields/class-acf-field-icon_picker.php:713
msgid "Tablet Icon"
msgstr "Tablet icon"

#: includes/fields/class-acf-field-icon_picker.php:704
msgid "Store Icon"
msgstr "Store icon"

#: includes/fields/class-acf-field-icon_picker.php:703
msgid "Sticky Icon"
msgstr "Sticky icon"

#: includes/fields/class-acf-field-icon_picker.php:702
msgid "Star Half Icon"
msgstr "Star half icon"

#: includes/fields/class-acf-field-icon_picker.php:701
msgid "Star Filled Icon"
msgstr "Star filled icon"

#: includes/fields/class-acf-field-icon_picker.php:700
msgid "Star Empty Icon"
msgstr "Star empty icon"

#: includes/fields/class-acf-field-icon_picker.php:698
msgid "Sos Icon"
msgstr "SOS icon"

#: includes/fields/class-acf-field-icon_picker.php:697
msgid "Sort Icon"
msgstr "Sort icon"

#: includes/fields/class-acf-field-icon_picker.php:696
msgid "Smiley Icon"
msgstr "Smiley icon"

#: includes/fields/class-acf-field-icon_picker.php:695
msgid "Smartphone Icon"
msgstr "Smartphone icon"

#: includes/fields/class-acf-field-icon_picker.php:694
msgid "Slides Icon"
msgstr "Slides icon"

#: includes/fields/class-acf-field-icon_picker.php:691
msgid "Shield Icon"
msgstr "Shield icon"

#: includes/fields/class-acf-field-icon_picker.php:688
msgid "Share Icon"
msgstr "Share icon"

#: includes/fields/class-acf-field-icon_picker.php:687
msgid "Search Icon"
msgstr "Search icon"

#: includes/fields/class-acf-field-icon_picker.php:686
msgid "Screen Options Icon"
msgstr "Screen options icon"

#: includes/fields/class-acf-field-icon_picker.php:685
msgid "Schedule Icon"
msgstr "Schedule icon"

#: includes/fields/class-acf-field-icon_picker.php:680
msgid "Redo Icon"
msgstr "Redo icon"

#: includes/fields/class-acf-field-icon_picker.php:678
msgid "Randomize Icon"
msgstr "Randomise icon"

#: includes/fields/class-acf-field-icon_picker.php:677
msgid "Products Icon"
msgstr "Products icon"

#: includes/fields/class-acf-field-icon_picker.php:674
msgid "Pressthis Icon"
msgstr "Pressthis icon"

#: includes/fields/class-acf-field-icon_picker.php:673
msgid "Post Status Icon"
msgstr "Post status icon"

#: includes/fields/class-acf-field-icon_picker.php:672
msgid "Portfolio Icon"
msgstr "Portfolio icon"

#: includes/fields/class-acf-field-icon_picker.php:668
msgid "Plus Icon"
msgstr "Plus icon"

#: includes/fields/class-acf-field-icon_picker.php:666
msgid "Playlist Video Icon"
msgstr "Playlist video icon"

#: includes/fields/class-acf-field-icon_picker.php:665
msgid "Playlist Audio Icon"
msgstr "Playlist audio icon"

#: includes/fields/class-acf-field-icon_picker.php:663
msgid "Phone Icon"
msgstr "Phone icon"

#: includes/fields/class-acf-field-icon_picker.php:661
msgid "Performance Icon"
msgstr "Performance icon"

#: includes/fields/class-acf-field-icon_picker.php:659
msgid "Paperclip Icon"
msgstr "Paper clip icon"

#: includes/fields/class-acf-field-icon_picker.php:655
msgid "No Icon"
msgstr "No icon"

#: includes/fields/class-acf-field-icon_picker.php:654
msgid "Networking Icon"
msgstr "Networking icon"

#: includes/fields/class-acf-field-icon_picker.php:653
msgid "Nametag Icon"
msgstr "Name tag icon"

#: includes/fields/class-acf-field-icon_picker.php:652
msgid "Move Icon"
msgstr "Move icon"

#: includes/fields/class-acf-field-icon_picker.php:650
msgid "Money Icon"
msgstr "Money icon"

#: includes/fields/class-acf-field-icon_picker.php:649
msgid "Minus Icon"
msgstr "Minus icon"

#: includes/fields/class-acf-field-icon_picker.php:648
msgid "Migrate Icon"
msgstr "Migrate icon"

#: includes/fields/class-acf-field-icon_picker.php:647
msgid "Microphone Icon"
msgstr "Microphone icon"

#: includes/fields/class-acf-field-icon_picker.php:642
msgid "Megaphone Icon"
msgstr "Megaphone icon"

#: includes/fields/class-acf-field-icon_picker.php:632
msgid "Marker Icon"
msgstr "Marker icon"

#: includes/fields/class-acf-field-icon_picker.php:631
msgid "Lock Icon"
msgstr "Lock icon"

#: includes/fields/class-acf-field-icon_picker.php:629
msgid "Location Icon"
msgstr "Location icon"

#: includes/fields/class-acf-field-icon_picker.php:628
msgid "List View Icon"
msgstr "List view icon"

#: includes/fields/class-acf-field-icon_picker.php:626
msgid "Lightbulb Icon"
msgstr "Lightbulb icon"

#: includes/fields/class-acf-field-icon_picker.php:625
msgid "Left Right Icon"
msgstr "Left right icon"

#: includes/fields/class-acf-field-icon_picker.php:624
msgid "Layout Icon"
msgstr "Layout icon"

#: includes/fields/class-acf-field-icon_picker.php:623
msgid "Laptop Icon"
msgstr "Laptop icon"

#: includes/fields/class-acf-field-icon_picker.php:617
msgid "Info Icon"
msgstr "Info icon"

#: includes/fields/class-acf-field-icon_picker.php:616
msgid "Index Card Icon"
msgstr "Index card icon"

#: includes/fields/class-acf-field-icon_picker.php:605
msgid "ID Icon"
msgstr "ID icon"

#: includes/fields/class-acf-field-icon_picker.php:602
msgid "Hidden Icon"
msgstr "Hidden icon"

#: includes/fields/class-acf-field-icon_picker.php:601
msgid "Heart Icon"
msgstr "Heart icon"

#: includes/fields/class-acf-field-icon_picker.php:599
msgid "Hammer Icon"
msgstr "Hammer icon"

#: includes/fields/class-acf-field-icon_picker.php:477
#: includes/fields/class-acf-field-icon_picker.php:598
msgid "Groups Icon"
msgstr "Groups icon"

#: includes/fields/class-acf-field-icon_picker.php:597
msgid "Grid View Icon"
msgstr "Grid view icon"

#: includes/fields/class-acf-field-icon_picker.php:592
msgid "Forms Icon"
msgstr "Forms icon"

#: includes/fields/class-acf-field-icon_picker.php:582
msgid "Flag Icon"
msgstr "Flag icon"

#: includes/fields/class-acf-field-icon_picker.php:581
#: includes/fields/class-acf-field-icon_picker.php:608
msgid "Filter Icon"
msgstr "Filter icon"

#: includes/fields/class-acf-field-icon_picker.php:580
msgid "Feedback Icon"
msgstr "Feedback icon"

#: includes/fields/class-acf-field-icon_picker.php:579
msgid "Facebook (alt) Icon"
msgstr "Facebook (alt) icon"

#: includes/fields/class-acf-field-icon_picker.php:578
msgid "Facebook Icon"
msgstr "Facebook icon"

#: includes/fields/class-acf-field-icon_picker.php:577
msgid "External Icon"
msgstr "External icon"

#: includes/fields/class-acf-field-icon_picker.php:568
msgid "Email (alt) Icon"
msgstr "Email (alt) icon"

#: includes/fields/class-acf-field-icon_picker.php:567
msgid "Email Icon"
msgstr "Email icon"

#: includes/fields/class-acf-field-icon_picker.php:565
#: includes/fields/class-acf-field-icon_picker.php:591
#: includes/fields/class-acf-field-icon_picker.php:641
msgid "Video Icon"
msgstr "Video icon"

#: includes/fields/class-acf-field-icon_picker.php:564
msgid "Unlink Icon"
msgstr "Unlink icon"

#: includes/fields/class-acf-field-icon_picker.php:563
msgid "Underline Icon"
msgstr "Underline icon"

#: includes/fields/class-acf-field-icon_picker.php:561
msgid "Text Color Icon"
msgstr "Text colour icon"

#: includes/fields/class-acf-field-icon_picker.php:560
msgid "Table Icon"
msgstr "Table icon"

#: includes/fields/class-acf-field-icon_picker.php:559
msgid "Strikethrough Icon"
msgstr "Strikethrough icon"

#: includes/fields/class-acf-field-icon_picker.php:558
msgid "Spellcheck Icon"
msgstr "Spellcheck icon"

#: includes/fields/class-acf-field-icon_picker.php:556
msgid "Remove Formatting Icon"
msgstr "Remove formatting icon"

#: includes/fields/class-acf-field-icon_picker.php:555
#: includes/fields/class-acf-field-icon_picker.php:589
msgid "Quote Icon"
msgstr "Quote icon"

#: includes/fields/class-acf-field-icon_picker.php:554
msgid "Paste Word Icon"
msgstr "Paste word icon"

#: includes/fields/class-acf-field-icon_picker.php:553
msgid "Paste Text Icon"
msgstr "Paste text icon"

#: includes/fields/class-acf-field-icon_picker.php:552
msgid "Paragraph Icon"
msgstr "Paragraph icon"

#: includes/fields/class-acf-field-icon_picker.php:551
msgid "Outdent Icon"
msgstr "Outdent icon"

#: includes/fields/class-acf-field-icon_picker.php:547
msgid "Kitchen Sink Icon"
msgstr "Kitchen sink icon"

#: includes/fields/class-acf-field-icon_picker.php:546
msgid "Justify Icon"
msgstr "Justify icon"

#: includes/fields/class-acf-field-icon_picker.php:545
msgid "Italic Icon"
msgstr "Italic icon"

#: includes/fields/class-acf-field-icon_picker.php:544
msgid "Insert More Icon"
msgstr "Insert more icon"

#: includes/fields/class-acf-field-icon_picker.php:543
msgid "Indent Icon"
msgstr "Indent icon"

#: includes/fields/class-acf-field-icon_picker.php:542
msgid "Help Icon"
msgstr "Help icon"

#: includes/fields/class-acf-field-icon_picker.php:541
msgid "Expand Icon"
msgstr "Expand icon"

#: includes/fields/class-acf-field-icon_picker.php:539
msgid "Contract Icon"
msgstr "Contract icon"

#: includes/fields/class-acf-field-icon_picker.php:538
#: includes/fields/class-acf-field-icon_picker.php:635
msgid "Code Icon"
msgstr "Code icon"

#: includes/fields/class-acf-field-icon_picker.php:537
msgid "Break Icon"
msgstr "Break icon"

#: includes/fields/class-acf-field-icon_picker.php:536
msgid "Bold Icon"
msgstr "Bold icon"

#: includes/fields/class-acf-field-icon_picker.php:530
msgid "Edit Icon"
msgstr "Edit icon"

#: includes/fields/class-acf-field-icon_picker.php:528
msgid "Download Icon"
msgstr "Download icon"

#: includes/fields/class-acf-field-icon_picker.php:527
msgid "Dismiss Icon"
msgstr "Dismiss icon"

#: includes/fields/class-acf-field-icon_picker.php:526
msgid "Desktop Icon"
msgstr "Desktop icon"

#: includes/fields/class-acf-field-icon_picker.php:519
msgid "Dashboard Icon"
msgstr "Dashboard icon"

#: includes/fields/class-acf-field-icon_picker.php:502
msgid "Cloud Icon"
msgstr "Cloud icon"

#: includes/fields/class-acf-field-icon_picker.php:501
msgid "Clock Icon"
msgstr "Clock icon"

#: includes/fields/class-acf-field-icon_picker.php:500
msgid "Clipboard Icon"
msgstr "Clipboard icon"

#: includes/fields/class-acf-field-icon_picker.php:499
msgid "Chart Pie Icon"
msgstr "Chart pie icon"

#: includes/fields/class-acf-field-icon_picker.php:498
msgid "Chart Line Icon"
msgstr "Chart line icon"

#: includes/fields/class-acf-field-icon_picker.php:497
msgid "Chart Bar Icon"
msgstr "Chart bar icon"

#: includes/fields/class-acf-field-icon_picker.php:496
msgid "Chart Area Icon"
msgstr "Chart area icon"

#: includes/fields/class-acf-field-icon_picker.php:495
msgid "Category Icon"
msgstr "Category icon"

#: includes/fields/class-acf-field-icon_picker.php:494
msgid "Cart Icon"
msgstr "Basket icon"

#: includes/fields/class-acf-field-icon_picker.php:493
msgid "Carrot Icon"
msgstr "Carrot icon"

#: includes/fields/class-acf-field-icon_picker.php:490
msgid "Camera Icon"
msgstr "Camera icon"

#: includes/fields/class-acf-field-icon_picker.php:489
msgid "Calendar (alt) Icon"
msgstr "Calendar (alt) icon"

#: includes/fields/class-acf-field-icon_picker.php:488
msgid "Calendar Icon"
msgstr "Calendar icon"

#: includes/fields/class-acf-field-icon_picker.php:485
msgid "Businesswoman Icon"
msgstr "Businesswoman icon"

#: includes/fields/class-acf-field-icon_picker.php:482
msgid "Building Icon"
msgstr "Building icon"

#: includes/fields/class-acf-field-icon_picker.php:469
msgid "Book Icon"
msgstr "Book icon"

#: includes/fields/class-acf-field-icon_picker.php:464
msgid "Backup Icon"
msgstr "Backup icon"

#: includes/fields/class-acf-field-icon_picker.php:463
msgid "Awards Icon"
msgstr "Awards icon"

#: includes/fields/class-acf-field-icon_picker.php:462
msgid "Art Icon"
msgstr "Art icon"

#: includes/fields/class-acf-field-icon_picker.php:459
msgid "Arrow Up Icon"
msgstr "Arrow up icon"

#: includes/fields/class-acf-field-icon_picker.php:456
msgid "Arrow Right Icon"
msgstr "Arrow right icon"

#: includes/fields/class-acf-field-icon_picker.php:453
msgid "Arrow Left Icon"
msgstr "Arrow left icon"

#: includes/fields/class-acf-field-icon_picker.php:450
msgid "Arrow Down Icon"
msgstr "Arrow down icon"

#: includes/fields/class-acf-field-icon_picker.php:449
#: includes/fields/class-acf-field-icon_picker.php:633
msgid "Archive Icon"
msgstr "Archive icon"

#: includes/fields/class-acf-field-icon_picker.php:448
msgid "Analytics Icon"
msgstr "Analytics icon"

#: includes/fields/class-acf-field-icon_picker.php:445
#: includes/fields/class-acf-field-icon_picker.php:535
msgid "Align Right Icon"
msgstr "Align right icon"

#: includes/fields/class-acf-field-icon_picker.php:442
msgid "Align None Icon"
msgstr "Align none icon"

#: includes/fields/class-acf-field-icon_picker.php:441
#: includes/fields/class-acf-field-icon_picker.php:534
msgid "Align Left Icon"
msgstr "Align left icon"

#: includes/fields/class-acf-field-icon_picker.php:439
#: includes/fields/class-acf-field-icon_picker.php:533
msgid "Align Center Icon"
msgstr "Align centre icon"

#: includes/fields/class-acf-field-icon_picker.php:438
msgid "Album Icon"
msgstr "Album icon"

#: includes/fields/class-acf-field-icon_picker.php:436
msgid "Users Icon"
msgstr "Users icon"

#: includes/fields/class-acf-field-icon_picker.php:435
msgid "Tools Icon"
msgstr "Tools icon"

#: includes/fields/class-acf-field-icon_picker.php:431
msgid "Site Icon"
msgstr "Site icon"

#: includes/fields/class-acf-field-icon_picker.php:430
msgid "Settings Icon"
msgstr "Settings icon"

#: includes/fields/class-acf-field-icon_picker.php:429
msgid "Post Icon"
msgstr "Post icon"

#: includes/fields/class-acf-field-icon_picker.php:428
msgid "Plugins Icon"
msgstr "Plugins icon"

#: includes/fields/class-acf-field-icon_picker.php:427
msgid "Page Icon"
msgstr "Page icon"

#: includes/fields/class-acf-field-icon_picker.php:426
msgid "Network Icon"
msgstr "Network icon"

#: includes/fields/class-acf-field-icon_picker.php:425
msgid "Multisite Icon"
msgstr "Multisite icon"

#: includes/fields/class-acf-field-icon_picker.php:424
msgid "Media Icon"
msgstr "Media icon"

#: includes/fields/class-acf-field-icon_picker.php:423
msgid "Links Icon"
msgstr "Links icon"

#: includes/fields/class-acf-field-icon_picker.php:422
msgid "Home Icon"
msgstr "Home icon"

#: includes/fields/class-acf-field-icon_picker.php:420
msgid "Customizer Icon"
msgstr "Customiser icon"

#: includes/fields/class-acf-field-icon_picker.php:419
#: includes/fields/class-acf-field-icon_picker.php:743
msgid "Comments Icon"
msgstr "Comments icon"

#: includes/fields/class-acf-field-icon_picker.php:418
msgid "Collapse Icon"
msgstr "Collapse icon"

#: includes/fields/class-acf-field-icon_picker.php:417
msgid "Appearance Icon"
msgstr "Appearance icon"

#: includes/fields/class-acf-field-icon_picker.php:421
msgid "Generic Icon"
msgstr "Generic icon"

#: includes/fields/class-acf-field-icon_picker.php:353
msgid "Icon picker requires a value."
msgstr "Icon picker requires a value."

#: includes/fields/class-acf-field-icon_picker.php:348
msgid "Icon picker requires an icon type."
msgstr "Icon picker requires an icon type."

#: includes/fields/class-acf-field-icon_picker.php:317
msgid ""
"The available icons matching your search query have been updated in the icon "
"picker below."
msgstr ""
"The available icons matching your search query have been updated in the icon "
"picker below."

#: includes/fields/class-acf-field-icon_picker.php:316
msgid "No results found for that search term"
msgstr "No results found for that search term"

#: includes/fields/class-acf-field-icon_picker.php:298
msgid "Array"
msgstr "Array"

#: includes/fields/class-acf-field-icon_picker.php:297
msgid "String"
msgstr "String"

#. translators: %s - link to documentation
#: includes/fields/class-acf-field-icon_picker.php:285
msgid "Specify the return format for the icon. %s"
msgstr "Specify the return format for the icon. %s"

#: includes/fields/class-acf-field-icon_picker.php:270
msgid "Select where content editors can choose the icon from."
msgstr "Select where content editors can choose the icon from."

#: includes/fields/class-acf-field-icon_picker.php:242
msgid "The URL to the icon you'd like to use, or svg as Data URI"
msgstr "The URL to the icon you'd like to use, or svg as Data URI"

#: includes/fields/class-acf-field-icon_picker.php:225
msgid "Browse Media Library"
msgstr "Browse Media Library"

#: includes/fields/class-acf-field-icon_picker.php:216
msgid "The currently selected image preview"
msgstr "The currently selected image preview"

#: includes/fields/class-acf-field-icon_picker.php:207
msgid "Click to change the icon in the Media Library"
msgstr "Click to change the icon in the Media Library"

#: includes/fields/class-acf-field-icon_picker.php:95
msgid "Search icons..."
msgstr "Search icons..."

#: includes/fields/class-acf-field-icon_picker.php:53
msgid "Media Library"
msgstr "Media Library"

#: includes/fields/class-acf-field-icon_picker.php:49
msgid "Dashicons"
msgstr "Dashicons"

#: includes/fields/class-acf-field-icon_picker.php:26
msgid ""
"An interactive UI for selecting an icon. Select from Dashicons, the media "
"library, or a standalone URL input."
msgstr ""
"An interactive UI for selecting an icon. Select from Dashicons, the media "
"library, or a standalone URL input."

#: includes/fields/class-acf-field-icon_picker.php:23
msgid "Icon Picker"
msgstr "Icon Picker"

#: src/Site_Health/Site_Health.php:787
msgid "JSON Load Paths"
msgstr "JSON Load Paths"

#: src/Site_Health/Site_Health.php:781
msgid "JSON Save Paths"
msgstr "JSON Save Paths"

#: src/Site_Health/Site_Health.php:772
msgid "Registered ACF Forms"
msgstr "Registered ACF Forms"

#: src/Site_Health/Site_Health.php:766
msgid "Shortcode Enabled"
msgstr "Shortcode Enabled"

#: src/Site_Health/Site_Health.php:758
msgid "Field Settings Tabs Enabled"
msgstr "Field Settings Tabs Enabled"

#: src/Site_Health/Site_Health.php:750
msgid "Field Type Modal Enabled"
msgstr "Field Type Modal Enabled"

#: src/Site_Health/Site_Health.php:742
msgid "Admin UI Enabled"
msgstr "Admin UI Enabled"

#: src/Site_Health/Site_Health.php:733
msgid "Block Preloading Enabled"
msgstr "Block Preloading Enabled"

#: src/Site_Health/Site_Health.php:721
msgid "Blocks Per ACF Block Version"
msgstr "Blocks Per ACF Block Version"

#: src/Site_Health/Site_Health.php:716
msgid "Blocks Per API Version"
msgstr "Blocks Per API Version"

#: src/Site_Health/Site_Health.php:689
msgid "Registered ACF Blocks"
msgstr "Registered ACF Blocks"

#: src/Site_Health/Site_Health.php:683
msgid "Light"
msgstr "Light"

#: src/Site_Health/Site_Health.php:683
msgid "Standard"
msgstr "Standard"

#: src/Site_Health/Site_Health.php:682
msgid "REST API Format"
msgstr "REST API Format"

#: src/Site_Health/Site_Health.php:674
msgid "Registered Options Pages (PHP)"
msgstr "Registered Options Pages (PHP)"

#: src/Site_Health/Site_Health.php:660
msgid "Registered Options Pages (JSON)"
msgstr "Registered Options Pages (JSON)"

#: src/Site_Health/Site_Health.php:655
msgid "Registered Options Pages (UI)"
msgstr "Registered Options Pages (UI)"

#: src/Site_Health/Site_Health.php:625
msgid "Options Pages UI Enabled"
msgstr "Options Pages UI Enabled"

#: src/Site_Health/Site_Health.php:617
msgid "Registered Taxonomies (JSON)"
msgstr "Registered Taxonomies (JSON)"

#: src/Site_Health/Site_Health.php:605
msgid "Registered Taxonomies (UI)"
msgstr "Registered Taxonomies (UI)"

#: src/Site_Health/Site_Health.php:593
msgid "Registered Post Types (JSON)"
msgstr "Registered Post Types (JSON)"

#: src/Site_Health/Site_Health.php:581
msgid "Registered Post Types (UI)"
msgstr "Registered Post Types (UI)"

#: src/Site_Health/Site_Health.php:568
msgid "Post Types and Taxonomies Enabled"
msgstr "Post Types and Taxonomies Enabled"

#: src/Site_Health/Site_Health.php:561
msgid "Number of Third Party Fields by Field Type"
msgstr "Number of Third Party Fields by Field Type"

#: src/Site_Health/Site_Health.php:556
msgid "Number of Fields by Field Type"
msgstr "Number of Fields by Field Type"

#: src/Site_Health/Site_Health.php:449
msgid "Field Groups Enabled for GraphQL"
msgstr "Field Groups Enabled for GraphQL"

#: src/Site_Health/Site_Health.php:436
msgid "Field Groups Enabled for REST API"
msgstr "Field Groups Enabled for REST API"

#: src/Site_Health/Site_Health.php:424
msgid "Registered Field Groups (JSON)"
msgstr "Registered Field Groups (JSON)"

#: src/Site_Health/Site_Health.php:412
msgid "Registered Field Groups (PHP)"
msgstr "Registered Field Groups (PHP)"

#: src/Site_Health/Site_Health.php:400
msgid "Registered Field Groups (UI)"
msgstr "Registered Field Groups (UI)"

#: src/Site_Health/Site_Health.php:388
msgid "Active Plugins"
msgstr "Active Plugins"

#: src/Site_Health/Site_Health.php:362
msgid "Parent Theme"
msgstr "Parent Theme"

#: src/Site_Health/Site_Health.php:351
msgid "Active Theme"
msgstr "Active Theme"

#: src/Site_Health/Site_Health.php:342
msgid "Is Multisite"
msgstr "Is Multisite"

#: src/Site_Health/Site_Health.php:337
msgid "MySQL Version"
msgstr "MySQL Version"

#: src/Site_Health/Site_Health.php:332
msgid "WordPress Version"
msgstr "WordPress Version"

#: src/Site_Health/Site_Health.php:325
msgid "Subscription Expiry Date"
msgstr "Subscription Expiry Date"

#: src/Site_Health/Site_Health.php:317
msgid "License Status"
msgstr "Licence Status"

#: src/Site_Health/Site_Health.php:312
msgid "License Type"
msgstr "Licence Type"

#: src/Site_Health/Site_Health.php:307
msgid "Licensed URL"
msgstr "Licensed URL"

#: src/Site_Health/Site_Health.php:301
msgid "License Activated"
msgstr "Licence Activated"

#: src/Site_Health/Site_Health.php:290
msgid "Free"
msgstr "Free"

#: src/Site_Health/Site_Health.php:289
msgid "Plugin Type"
msgstr "Plugin Type"

#: src/Site_Health/Site_Health.php:284
msgid "Plugin Version"
msgstr "Plugin Version"

#: src/Site_Health/Site_Health.php:255
msgid ""
"This section contains debug information about your ACF configuration which "
"can be useful to provide to support."
msgstr ""
"This section contains debug information about your ACF configuration which "
"can be useful to provide to support."

#: includes/assets.php:373
msgid "An ACF Block on this page requires attention before you can save."
msgstr "An ACF Block on this page requires attention before you can save."

#. translators: %s - The clear log button opening HTML tag. %s - The closing
#. HTML tag.
#: includes/admin/views/escaped-html-notice.php:63
msgid ""
"This data is logged as we detect values that have been changed during "
"output. %1$sClear log and dismiss%2$s after escaping the values in your "
"code. The notice will reappear if we detect changed values again."
msgstr ""
"This data is logged as we detect values that have been changed during "
"output. %1$sClear log and dismiss%2$s after escaping the values in your "
"code. The notice will reappear if we detect changed values again."

#: includes/admin/views/escaped-html-notice.php:25
msgid "Dismiss permanently"
msgstr "Dismiss permanently"

#: includes/admin/views/acf-field-group/field.php:220
msgid "Instructions for content editors. Shown when submitting data."
msgstr "Instructions for content editors. Shown when submitting data."

#: includes/admin/post-types/admin-field-group.php:142
msgid "Has no term selected"
msgstr "Has no term selected"

#: includes/admin/post-types/admin-field-group.php:141
msgid "Has any term selected"
msgstr "Has any term selected"

#: includes/admin/post-types/admin-field-group.php:140
msgid "Terms do not contain"
msgstr "Terms do not contain"

#: includes/admin/post-types/admin-field-group.php:139
msgid "Terms contain"
msgstr "Terms contain"

#: includes/admin/post-types/admin-field-group.php:138
msgid "Term is not equal to"
msgstr "Term is not equal to"

#: includes/admin/post-types/admin-field-group.php:137
msgid "Term is equal to"
msgstr "Term is equal to"

#: includes/admin/post-types/admin-field-group.php:136
msgid "Has no user selected"
msgstr "Has no user selected"

#: includes/admin/post-types/admin-field-group.php:135
msgid "Has any user selected"
msgstr "Has any user selected"

#: includes/admin/post-types/admin-field-group.php:134
msgid "Users do not contain"
msgstr "Users do not contain"

#: includes/admin/post-types/admin-field-group.php:133
msgid "Users contain"
msgstr "Users contain"

#: includes/admin/post-types/admin-field-group.php:132
msgid "User is not equal to"
msgstr "User is not equal to"

#: includes/admin/post-types/admin-field-group.php:131
msgid "User is equal to"
msgstr "User is equal to"

#: includes/admin/post-types/admin-field-group.php:130
msgid "Has no page selected"
msgstr "Has no page selected"

#: includes/admin/post-types/admin-field-group.php:129
msgid "Has any page selected"
msgstr "Has any page selected"

#: includes/admin/post-types/admin-field-group.php:128
msgid "Pages do not contain"
msgstr "Pages do not contain"

#: includes/admin/post-types/admin-field-group.php:127
msgid "Pages contain"
msgstr "Pages contain"

#: includes/admin/post-types/admin-field-group.php:126
msgid "Page is not equal to"
msgstr "Page is not equal to"

#: includes/admin/post-types/admin-field-group.php:125
msgid "Page is equal to"
msgstr "Page is equal to"

#: includes/admin/post-types/admin-field-group.php:124
msgid "Has no relationship selected"
msgstr "Has no relationship selected"

#: includes/admin/post-types/admin-field-group.php:123
msgid "Has any relationship selected"
msgstr "Has any relationship selected"

#: includes/admin/post-types/admin-field-group.php:122
msgid "Has no post selected"
msgstr "Has no post selected"

#: includes/admin/post-types/admin-field-group.php:121
msgid "Has any post selected"
msgstr "Has any post selected"

#: includes/admin/post-types/admin-field-group.php:120
msgid "Posts do not contain"
msgstr "Posts do not contain"

#: includes/admin/post-types/admin-field-group.php:119
msgid "Posts contain"
msgstr "Posts contain"

#: includes/admin/post-types/admin-field-group.php:118
msgid "Post is not equal to"
msgstr "Post is not equal to"

#: includes/admin/post-types/admin-field-group.php:117
msgid "Post is equal to"
msgstr "Post is equal to"

#: includes/admin/post-types/admin-field-group.php:116
msgid "Relationships do not contain"
msgstr "Relationships do not contain"

#: includes/admin/post-types/admin-field-group.php:115
msgid "Relationships contain"
msgstr "Relationships contain"

#: includes/admin/post-types/admin-field-group.php:114
msgid "Relationship is not equal to"
msgstr "Relationship is not equal to"

#: includes/admin/post-types/admin-field-group.php:113
msgid "Relationship is equal to"
msgstr "Relationship is equal to"

#: src/Blocks/Bindings.php:38
msgctxt "The core ACF block binding source name for fields on the current page"
msgid "ACF Fields"
msgstr "ACF Fields"

#: includes/admin/views/browse-fields-modal.php:14
msgid "ACF PRO Feature"
msgstr "ACF PRO Feature"

#: includes/admin/views/browse-fields-modal.php:10
msgid "Renew PRO to Unlock"
msgstr "Renew PRO to Unlock"

#: includes/admin/views/browse-fields-modal.php:8
msgid "Renew PRO License"
msgstr "Renew PRO Licence"

#: includes/admin/views/acf-field-group/field.php:41
msgid "PRO fields cannot be edited without an active license."
msgstr "PRO fields cannot be edited without an active licence."

#: includes/admin/admin-internal-post-type-list.php:232
msgid ""
"Please activate your ACF PRO license to edit field groups assigned to an ACF "
"Block."
msgstr ""
"Please activate your ACF PRO licence to edit field groups assigned to an ACF "
"Block."

#: includes/admin/admin-internal-post-type-list.php:231
msgid "Please activate your ACF PRO license to edit this options page."
msgstr "Please activate your ACF PRO licence to edit this options page."

#: includes/api/api-template.php:385 includes/api/api-template.php:439
msgid ""
"Returning escaped HTML values is only possible when format_value is also "
"true. The field values have not been returned for security."
msgstr ""
"Returning escaped HTML values is only possible when format_value is also "
"true. The field values have not been returned for security."

#: includes/api/api-template.php:46 includes/api/api-template.php:251
#: includes/api/api-template.php:947
msgid ""
"Returning an escaped HTML value is only possible when format_value is also "
"true. The field value has not been returned for security."
msgstr ""
"Returning an escaped HTML value is only possible when format_value is also "
"true. The field value has not been returned for security."

#. translators: %1$s - name of the ACF plugin. %2$s - Link to documentation.
#: includes/admin/views/escaped-html-notice.php:32
msgid ""
"%1$s ACF now automatically escapes unsafe HTML when rendered by "
"<code>the_field</code> or the ACF shortcode. We've detected the output of "
"some of your fields has been modified by this change, but this may not be a "
"breaking change. %2$s."
msgstr ""
"%1$s ACF now automatically escapes unsafe HTML when rendered by "
"<code>the_field</code> or the ACF shortcode. We've detected the output of "
"some of your fields has been modified by this change, but this may not be a "
"breaking change. %2$s."

#: includes/admin/views/escaped-html-notice.php:27
msgid "Please contact your site administrator or developer for more details."
msgstr "Please contact your site administrator or developer for more details."

#: includes/admin/views/escaped-html-notice.php:5
msgid "Learn&nbsp;more"
msgstr "Learn&nbsp;more"

#: includes/admin/admin.php:64
msgid "Hide&nbsp;details"
msgstr "Hide&nbsp;details"

#: includes/admin/admin.php:63 includes/admin/views/escaped-html-notice.php:24
msgid "Show&nbsp;details"
msgstr "Show&nbsp;details"

#. translators: %1$s - The selector used  %2$s The field name  3%$s The parent
#. function name
#: includes/admin/views/escaped-html-notice.php:49
msgid "%1$s (%2$s) - rendered via %3$s"
msgstr "%1$s (%2$s) - rendered via %3$s"

#: includes/admin/views/global/navigation.php:229
msgid "Renew ACF PRO License"
msgstr "Renew ACF PRO Licence"

#: includes/admin/views/acf-field-group/pro-features.php:17
msgid "Renew License"
msgstr "Renew Licence"

#: includes/admin/views/acf-field-group/pro-features.php:14
msgid "Manage License"
msgstr "Manage Licence"

#: includes/admin/views/acf-field-group/options.php:102
msgid "'High' position not supported in the Block Editor"
msgstr "'High' position not supported in the Block Editor"

#: includes/admin/views/options-page-preview.php:30
msgid "Upgrade to ACF PRO"
msgstr "Upgrade to ACF PRO"

#. translators: %s URL to ACF options pages documentation
#: includes/admin/views/options-page-preview.php:7
msgid ""
"ACF <a href=\"%s\" target=\"_blank\">options pages</a> are custom admin "
"pages for managing global settings via fields. You can create multiple pages "
"and sub-pages."
msgstr ""
"ACF <a href=\"%s\" target=\"_blank\">options pages</a> are custom admin "
"pages for managing global settings via fields. You can create multiple pages "
"and sub-pages."

#: includes/admin/views/global/header.php:35
msgid "Add Options Page"
msgstr "Add Options Page"

#: includes/admin/views/acf-post-type/advanced-settings.php:708
msgid "In the editor used as the placeholder of the title."
msgstr "In the editor used as the placeholder of the title."

#: includes/admin/views/acf-post-type/advanced-settings.php:707
msgid "Title Placeholder"
msgstr "Title Placeholder"

#: includes/admin/views/global/navigation.php:97
msgid "4 Months Free"
msgstr "4 Months Free"

#. translators: %s - A singular label for a post type or taxonomy.
#: includes/admin/views/global/form-top.php:59
msgid "(Duplicated from %s)"
msgstr "(Duplicated from %s)"

#: includes/admin/tools/class-acf-admin-tool-export.php:289
msgid "Select Options Pages"
msgstr "Select Options Pages"

#: includes/admin/post-types/admin-taxonomy.php:107
msgid "Duplicate taxonomy"
msgstr "Duplicate taxonomy"

#: includes/admin/post-types/admin-post-type.php:106
#: includes/admin/post-types/admin-taxonomy.php:106
msgid "Create taxonomy"
msgstr "Create taxonomy"

#: includes/admin/post-types/admin-post-type.php:105
msgid "Duplicate post type"
msgstr "Duplicate post type"

#: includes/admin/post-types/admin-post-type.php:104
#: includes/admin/post-types/admin-taxonomy.php:108
msgid "Create post type"
msgstr "Create post type"

#: includes/admin/post-types/admin-post-type.php:103
#: includes/admin/post-types/admin-taxonomy.php:105
msgid "Link field groups"
msgstr "Link field groups"

#: includes/admin/post-types/admin-post-type.php:102
#: includes/admin/post-types/admin-taxonomy.php:104
msgid "Add fields"
msgstr "Add fields"

#: includes/admin/post-types/admin-field-group.php:146
msgid "This Field"
msgstr "This Field"

#: includes/admin/admin.php:385
msgid "ACF PRO"
msgstr "ACF PRO"

#: includes/admin/admin.php:383
msgid "Feedback"
msgstr "Feedback"

#: includes/admin/admin.php:381
msgid "Support"
msgstr "Support"

#. translators: This text is prepended by a link to ACF's website, and appended
#. by a link to WP Engine's website.
#: includes/admin/admin.php:356
msgid "is developed and maintained by"
msgstr "is developed and maintained by"

#. translators: %s - either "post type" or "taxonomy"
#: includes/admin/admin-internal-post-type.php:313
msgid "Add this %s to the location rules of the selected field groups."
msgstr "Add this %s to the location rules of the selected field groups."

#. translators: %s the URL to ACF's bidirectional relationship documentation
#: includes/acf-bidirectional-functions.php:272
msgid ""
"Enabling the bidirectional setting allows you to update a value in the "
"target fields for each value selected for this field, adding or removing the "
"Post ID, Taxonomy ID or User ID of the item being updated. For more "
"information, please read the <a href=\"%s\" target=\"_blank\">documentation</"
"a>."
msgstr ""
"Enabling the bidirectional setting allows you to update a value in the "
"target fields for each value selected for this field, adding or removing the "
"Post ID, Taxonomy ID or User ID of the item being updated. For more "
"information, please read the <a href=\"%s\" target=\"_blank\">documentation</"
"a>."

#: includes/acf-bidirectional-functions.php:248
msgid ""
"Select field(s) to store the reference back to the item being updated. You "
"may select this field. Target fields must be compatible with where this "
"field is being displayed. For example, if this field is displayed on a "
"Taxonomy, your target field should be of type Taxonomy"
msgstr ""
"Select field(s) to store the reference back to the item being updated. You "
"may select this field. Target fields must be compatible with where this "
"field is being displayed. For example, if this field is displayed on a "
"Taxonomy, your target field should be of type Taxonomy"

#: includes/acf-bidirectional-functions.php:247
msgid "Target Field"
msgstr "Target Field"

#: includes/acf-bidirectional-functions.php:221
msgid "Update a field on the selected values, referencing back to this ID"
msgstr "Update a field on the selected values, referencing back to this ID"

#: includes/acf-bidirectional-functions.php:220
msgid "Bidirectional"
msgstr "Bidirectional"

#. translators: %s A field type name, such as "Relationship"
#: includes/acf-bidirectional-functions.php:193
msgid "%s Field"
msgstr "%s Field"

#: includes/fields/class-acf-field-page_link.php:498
#: includes/fields/class-acf-field-post_object.php:411
#: includes/fields/class-acf-field-select.php:378
#: includes/fields/class-acf-field-user.php:111
msgid "Select Multiple"
msgstr "Select Multiple"

#: includes/admin/views/global/navigation.php:241
msgid "WP Engine logo"
msgstr "WP Engine logo"

#: includes/admin/views/acf-taxonomy/basic-settings.php:58
msgid "Lower case letters, underscores and dashes only, Max 32 characters."
msgstr "Lower case letters, underscores and dashes only, Max 32 characters."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1156
msgid "The capability name for assigning terms of this taxonomy."
msgstr "The capability name for assigning terms of this taxonomy."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1155
msgid "Assign Terms Capability"
msgstr "Assign Terms Capability"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1139
msgid "The capability name for deleting terms of this taxonomy."
msgstr "The capability name for deleting terms of this taxonomy."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1138
msgid "Delete Terms Capability"
msgstr "Delete Terms Capability"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1122
msgid "The capability name for editing terms of this taxonomy."
msgstr "The capability name for editing terms of this taxonomy."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1121
msgid "Edit Terms Capability"
msgstr "Edit Terms Capability"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1105
msgid "The capability name for managing terms of this taxonomy."
msgstr "The capability name for managing terms of this taxonomy."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1104
msgid "Manage Terms Capability"
msgstr "Manage Terms Capability"

#: includes/admin/views/acf-post-type/advanced-settings.php:929
msgid ""
"Sets whether posts should be excluded from search results and taxonomy "
"archive pages."
msgstr ""
"Sets whether posts should be excluded from search results and taxonomy "
"archive pages."

#: includes/admin/views/acf-field-group/pro-features.php:78
msgid "More Tools from WP Engine"
msgstr "More Tools from WP Engine"

#. translators: %s - WP Engine logo
#: includes/admin/views/acf-field-group/pro-features.php:73
msgid "Built for those that build with WordPress, by the team at %s"
msgstr "Built for those that build with WordPress, by the team at %s"

#: includes/admin/views/acf-field-group/pro-features.php:6
msgid "View Pricing & Upgrade"
msgstr "View Pricing & Upgrade"

#: includes/admin/views/acf-field-group/pro-features.php:3
#: includes/admin/views/options-page-preview.php:29
#: includes/fields/class-acf-field-icon_picker.php:280
msgid "Learn More"
msgstr "Learn More"

#: includes/admin/views/acf-field-group/pro-features.php:28
msgid ""
"Speed up your workflow and develop better websites with features like ACF "
"Blocks and Options Pages, and sophisticated field types like Repeater, "
"Flexible Content, Clone, and Gallery."
msgstr ""
"Speed up your workflow and develop better websites with features like ACF "
"Blocks and Options Pages, and sophisticated field types like Repeater, "
"Flexible Content, Clone, and Gallery."

#: includes/admin/views/acf-field-group/pro-features.php:2
msgid "Unlock Advanced Features and Build Even More with ACF PRO"
msgstr "Unlock Advanced Features and Build Even More with ACF PRO"

#. translators: %s - singular label of post type/taxonomy, i.e. "Movie"/"Genre"
#: includes/admin/views/global/form-top.php:19
msgid "%s fields"
msgstr "%s fields"

#: includes/admin/post-types/admin-taxonomies.php:263
msgid "No terms"
msgstr "No terms"

#: includes/admin/post-types/admin-taxonomies.php:236
msgid "No post types"
msgstr "No post types"

#: includes/admin/post-types/admin-post-types.php:260
msgid "No posts"
msgstr "No posts"

#: includes/admin/post-types/admin-post-types.php:234
msgid "No taxonomies"
msgstr "No taxonomies"

#: includes/admin/post-types/admin-post-types.php:179
#: includes/admin/post-types/admin-taxonomies.php:178
msgid "No field groups"
msgstr "No field groups"

#: includes/admin/post-types/admin-field-groups.php:251
msgid "No fields"
msgstr "No fields"

#: includes/admin/post-types/admin-field-groups.php:124
#: includes/admin/post-types/admin-post-types.php:143
#: includes/admin/post-types/admin-taxonomies.php:142
msgid "No description"
msgstr "No description"

#: includes/fields/class-acf-field-page_link.php:465
#: includes/fields/class-acf-field-post_object.php:374
#: includes/fields/class-acf-field-relationship.php:573
msgid "Any post status"
msgstr "Any post status"

#: includes/post-types/class-acf-taxonomy.php:288
msgid ""
"This taxonomy key is already in use by another taxonomy registered outside "
"of ACF and cannot be used."
msgstr ""
"This taxonomy key is already in use by another taxonomy registered outside "
"of ACF and cannot be used."

#: includes/post-types/class-acf-taxonomy.php:284
msgid ""
"This taxonomy key is already in use by another taxonomy in ACF and cannot be "
"used."
msgstr ""
"This taxonomy key is already in use by another taxonomy in ACF and cannot be "
"used."

#: includes/post-types/class-acf-taxonomy.php:256
msgid ""
"The taxonomy key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""
"The taxonomy key must only contain lower case alphanumeric characters, "
"underscores or dashes."

#: includes/post-types/class-acf-taxonomy.php:251
msgid "The taxonomy key must be under 32 characters."
msgstr "The taxonomy key must be under 32 characters."

#: includes/post-types/class-acf-taxonomy.php:99
msgid "No Taxonomies found in Trash"
msgstr "No Taxonomies found in the bin"

#: includes/post-types/class-acf-taxonomy.php:98
msgid "No Taxonomies found"
msgstr "No Taxonomies found"

#: includes/post-types/class-acf-taxonomy.php:97
msgid "Search Taxonomies"
msgstr "Search Taxonomies"

#: includes/post-types/class-acf-taxonomy.php:96
msgid "View Taxonomy"
msgstr "View Taxonomy"

#: includes/post-types/class-acf-taxonomy.php:95
msgid "New Taxonomy"
msgstr "New Taxonomy"

#: includes/post-types/class-acf-taxonomy.php:94
msgid "Edit Taxonomy"
msgstr "Edit Taxonomy"

#: includes/post-types/class-acf-taxonomy.php:93
msgid "Add New Taxonomy"
msgstr "Add New Taxonomy"

#: includes/post-types/class-acf-post-type.php:100
msgid "No Post Types found in Trash"
msgstr "No Post Types found in the bin"

#: includes/post-types/class-acf-post-type.php:99
msgid "No Post Types found"
msgstr "No Post Types found"

#: includes/post-types/class-acf-post-type.php:98
msgid "Search Post Types"
msgstr "Search Post Types"

#: includes/post-types/class-acf-post-type.php:97
msgid "View Post Type"
msgstr "View Post Type"

#: includes/post-types/class-acf-post-type.php:96
msgid "New Post Type"
msgstr "New Post Type"

#: includes/post-types/class-acf-post-type.php:95
msgid "Edit Post Type"
msgstr "Edit Post Type"

#: includes/post-types/class-acf-post-type.php:94
msgid "Add New Post Type"
msgstr "Add New Post Type"

#: includes/post-types/class-acf-post-type.php:366
msgid ""
"This post type key is already in use by another post type registered outside "
"of ACF and cannot be used."
msgstr ""
"This post type key is already in use by another post type registered outside "
"of ACF and cannot be used."

#: includes/post-types/class-acf-post-type.php:361
msgid ""
"This post type key is already in use by another post type in ACF and cannot "
"be used."
msgstr ""
"This post type key is already in use by another post type in ACF and cannot "
"be used."

#. translators: %s a link to WordPress.org's Reserved Terms page
#: includes/post-types/class-acf-post-type.php:339
#: includes/post-types/class-acf-taxonomy.php:262
msgid ""
"This field must not be a WordPress <a href=\"%s\" target=\"_blank\">reserved "
"term</a>."
msgstr ""
"This field must not be a WordPress <a href=\"%s\" target=\"_blank\">reserved "
"term</a>."

#: includes/post-types/class-acf-post-type.php:333
msgid ""
"The post type key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""
"The post type key must only contain lower case alphanumeric characters, "
"underscores or dashes."

#: includes/post-types/class-acf-post-type.php:328
msgid "The post type key must be under 20 characters."
msgstr "The post type key must be under 20 characters."

#: includes/fields/class-acf-field-wysiwyg.php:24
msgid "We do not recommend using this field in ACF Blocks."
msgstr "We do not recommend using this field in ACF Blocks."

#: includes/fields/class-acf-field-wysiwyg.php:24
msgid ""
"Displays the WordPress WYSIWYG editor as seen in Posts and Pages allowing "
"for a rich text-editing experience that also allows for multimedia content."
msgstr ""
"Displays the WordPress WYSIWYG editor as seen in Posts and Pages allowing "
"for a rich text-editing experience that also allows for multimedia content."

#: includes/fields/class-acf-field-wysiwyg.php:22
msgid "WYSIWYG Editor"
msgstr "WYSIWYG Editor"

#: includes/fields/class-acf-field-user.php:17
msgid ""
"Allows the selection of one or more users which can be used to create "
"relationships between data objects."
msgstr ""
"Allows the selection of one or more users which can be used to create "
"relationships between data objects."

#: includes/fields/class-acf-field-url.php:20
msgid "A text input specifically designed for storing web addresses."
msgstr "A text input specifically designed for storing web addresses."

#: includes/fields/class-acf-field-icon_picker.php:56
#: includes/fields/class-acf-field-url.php:19
msgid "URL"
msgstr "URL"

#: includes/fields/class-acf-field-true_false.php:24
msgid ""
"A toggle that allows you to pick a value of 1 or 0 (on or off, true or "
"false, etc). Can be presented as a stylized switch or checkbox."
msgstr ""
"A toggle that allows you to pick a value of 1 or 0 (on or off, true or "
"false, etc). Can be presented as a stylised switch or checkbox."

#: includes/fields/class-acf-field-time_picker.php:24
msgid ""
"An interactive UI for picking a time. The time format can be customized "
"using the field settings."
msgstr ""
"An interactive UI for picking a time. The time format can be customised "
"using the field settings."

#: includes/fields/class-acf-field-textarea.php:23
msgid "A basic textarea input for storing paragraphs of text."
msgstr "A basic textarea input for storing paragraphs of text."

#: includes/fields/class-acf-field-text.php:23
msgid "A basic text input, useful for storing single string values."
msgstr "A basic text input, useful for storing single string values."

#: includes/fields/class-acf-field-taxonomy.php:22
msgid ""
"Allows the selection of one or more taxonomy terms based on the criteria and "
"options specified in the fields settings."
msgstr ""
"Allows the selection of one or more taxonomy terms based on the criteria and "
"options specified in the fields settings."

#: includes/fields/class-acf-field-tab.php:25
msgid ""
"Allows you to group fields into tabbed sections in the edit screen. Useful "
"for keeping fields organized and structured."
msgstr ""
"Allows you to group fields into tabbed sections in the edit screen. Useful "
"for keeping fields organised and structured."

#: includes/fields/class-acf-field-select.php:18
msgid "A dropdown list with a selection of choices that you specify."
msgstr "A dropdown list with a selection of choices that you specify."

#: includes/fields/class-acf-field-relationship.php:19
msgid ""
"A dual-column interface to select one or more posts, pages, or custom post "
"type items to create a relationship with the item that you're currently "
"editing. Includes options to search and filter."
msgstr ""
"A dual-column interface to select one or more posts, pages, or custom post "
"type items to create a relationship with the item that you're currently "
"editing. Includes options to search and filter."

#: includes/fields/class-acf-field-range.php:23
msgid ""
"An input for selecting a numerical value within a specified range using a "
"range slider element."
msgstr ""
"An input for selecting a numerical value within a specified range using a "
"range slider element."

#: includes/fields/class-acf-field-radio.php:24
msgid ""
"A group of radio button inputs that allows the user to make a single "
"selection from values that you specify."
msgstr ""
"A group of radio button inputs that allows the user to make a single "
"selection from values that you specify."

#: includes/fields/class-acf-field-post_object.php:17
msgid ""
"An interactive and customizable UI for picking one or many posts, pages or "
"post type items with the option to search. "
msgstr ""
"An interactive and customisable UI for picking one or many posts, pages or "
"post type items with the option to search. "

#: includes/fields/class-acf-field-password.php:23
msgid "An input for providing a password using a masked field."
msgstr "An input for providing a password using a masked field."

#: includes/fields/class-acf-field-page_link.php:457
#: includes/fields/class-acf-field-post_object.php:366
#: includes/fields/class-acf-field-relationship.php:565
msgid "Filter by Post Status"
msgstr "Filter by Post Status"

#: includes/fields/class-acf-field-page_link.php:24
msgid ""
"An interactive dropdown to select one or more posts, pages, custom post type "
"items or archive URLs, with the option to search."
msgstr ""
"An interactive dropdown to select one or more posts, pages, custom post type "
"items or archive URLs, with the option to search."

#: includes/fields/class-acf-field-oembed.php:23
msgid ""
"An interactive component for embedding videos, images, tweets, audio and "
"other content by making use of the native WordPress oEmbed functionality."
msgstr ""
"An interactive component for embedding videos, images, tweets, audio and "
"other content by making use of the native WordPress oEmbed functionality."

#: includes/fields/class-acf-field-number.php:23
msgid "An input limited to numerical values."
msgstr "An input limited to numerical values."

#: includes/fields/class-acf-field-message.php:25
msgid ""
"Used to display a message to editors alongside other fields. Useful for "
"providing additional context or instructions around your fields."
msgstr ""
"Used to display a message to editors alongside other fields. Useful for "
"providing additional context or instructions around your fields."

#: includes/fields/class-acf-field-link.php:24
msgid ""
"Allows you to specify a link and its properties such as title and target "
"using the WordPress native link picker."
msgstr ""
"Allows you to specify a link and its properties such as title and target "
"using the WordPress native link picker."

#: includes/fields/class-acf-field-image.php:24
msgid "Uses the native WordPress media picker to upload, or choose images."
msgstr "Uses the native WordPress media picker to upload, or choose images."

#: includes/fields/class-acf-field-group.php:23
msgid ""
"Provides a way to structure fields into groups to better organize the data "
"and the edit screen."
msgstr ""
"Provides a way to structure fields into groups to better organise the data "
"and the edit screen."

#: includes/fields/class-acf-field-google-map.php:23
msgid ""
"An interactive UI for selecting a location using Google Maps. Requires a "
"Google Maps API key and additional configuration to display correctly."
msgstr ""
"An interactive UI for selecting a location using Google Maps. Requires a "
"Google Maps API key and additional configuration to display correctly."

#: includes/fields/class-acf-field-file.php:24
msgid "Uses the native WordPress media picker to upload, or choose files."
msgstr "Uses the native WordPress media picker to upload, or choose files."

#: includes/fields/class-acf-field-email.php:23
msgid "A text input specifically designed for storing email addresses."
msgstr "A text input specifically designed for storing email addresses."

#: includes/fields/class-acf-field-date_time_picker.php:24
msgid ""
"An interactive UI for picking a date and time. The date return format can be "
"customized using the field settings."
msgstr ""
"An interactive UI for picking a date and time. The date return format can be "
"customised using the field settings."

#: includes/fields/class-acf-field-date_picker.php:24
msgid ""
"An interactive UI for picking a date. The date return format can be "
"customized using the field settings."
msgstr ""
"An interactive UI for picking a date. The date return format can be "
"customised using the field settings."

#: includes/fields/class-acf-field-color_picker.php:24
msgid "An interactive UI for selecting a color, or specifying a Hex value."
msgstr "An interactive UI for selecting a colour, or specifying a Hex value."

#: includes/fields/class-acf-field-checkbox.php:37
msgid ""
"A group of checkbox inputs that allow the user to select one, or multiple "
"values that you specify."
msgstr ""
"A group of checkbox inputs that allow the user to select one, or multiple "
"values that you specify."

#: includes/fields/class-acf-field-button-group.php:25
msgid ""
"A group of buttons with values that you specify, users can choose one option "
"from the values provided."
msgstr ""
"A group of buttons with values that you specify, users can choose one option "
"from the values provided."

#: includes/fields/class-acf-field-accordion.php:26
msgid ""
"Allows you to group and organize custom fields into collapsable panels that "
"are shown while editing content. Useful for keeping large datasets tidy."
msgstr ""
"Allows you to group and organise custom fields into collapsable panels that "
"are shown while editing content. Useful for keeping large datasets tidy."

#: includes/fields.php:450
msgid ""
"This provides a solution for repeating content such as slides, team members, "
"and call-to-action tiles, by acting as a parent to a set of subfields which "
"can be repeated again and again."
msgstr ""
"This provides a solution for repeating content such as slides, team members, "
"and call-to-action tiles, by acting as a parent to a set of subfields which "
"can be repeated again and again."

#: includes/fields.php:440
msgid ""
"This provides an interactive interface for managing a collection of "
"attachments. Most settings are similar to the Image field type. Additional "
"settings allow you to specify where new attachments are added in the gallery "
"and the minimum/maximum number of attachments allowed."
msgstr ""
"This provides an interactive interface for managing a collection of "
"attachments. Most settings are similar to the Image field type. Additional "
"settings allow you to specify where new attachments are added in the gallery "
"and the minimum/maximum number of attachments allowed."

#: includes/fields.php:430
msgid ""
"This provides a simple, structured, layout-based editor. The Flexible "
"Content field allows you to define, create and manage content with total "
"control by using layouts and subfields to design the available blocks."
msgstr ""
"This provides a simple, structured, layout-based editor. The Flexible "
"Content field allows you to define, create and manage content with total "
"control by using layouts and subfields to design the available blocks."

#: includes/fields.php:420
msgid ""
"This allows you to select and display existing fields. It does not duplicate "
"any fields in the database, but loads and displays the selected fields at "
"run-time. The Clone field can either replace itself with the selected fields "
"or display the selected fields as a group of subfields."
msgstr ""
"This allows you to select and display existing fields. It does not duplicate "
"any fields in the database, but loads and displays the selected fields at "
"run-time. The Clone field can either replace itself with the selected fields "
"or display the selected fields as a group of subfields."

#: includes/fields.php:417
msgctxt "noun"
msgid "Clone"
msgstr "Clone"

#: includes/admin/views/global/navigation.php:86 includes/fields.php:332
#: src/Site_Health/Site_Health.php:290
msgid "PRO"
msgstr "PRO"

#: includes/fields.php:330 includes/fields.php:387
msgid "Advanced"
msgstr "Advanced"

#: includes/ajax/class-acf-ajax-local-json-diff.php:90
msgid "JSON (newer)"
msgstr "JSON (newer)"

#: includes/ajax/class-acf-ajax-local-json-diff.php:86
msgid "Original"
msgstr "Original"

#: includes/ajax/class-acf-ajax-local-json-diff.php:60
msgid "Invalid post ID."
msgstr "Invalid post ID."

#: includes/ajax/class-acf-ajax-local-json-diff.php:52
msgid "Invalid post type selected for review."
msgstr "Invalid post type selected for review."

#: includes/admin/views/global/navigation.php:192
msgid "More"
msgstr "More"

#: includes/admin/views/browse-fields-modal.php:96
msgid "Tutorial"
msgstr "Tutorial"

#: includes/admin/views/browse-fields-modal.php:73
msgid "Select Field"
msgstr "Select Field"

#. translators: %s: A link to the popular fields used in ACF
#: includes/admin/views/browse-fields-modal.php:60
msgid "Try a different search term or browse %s"
msgstr "Try a different search term or browse %s"

#: includes/admin/views/browse-fields-modal.php:57
msgid "Popular fields"
msgstr "Popular fields"

#. translators: %s: The invalid search term
#: includes/admin/views/browse-fields-modal.php:50
#: includes/fields/class-acf-field-icon_picker.php:115
msgid "No search results for '%s'"
msgstr "No search results for '%s'"

#: includes/admin/views/browse-fields-modal.php:23
msgid "Search fields..."
msgstr "Search fields..."

#: includes/admin/views/browse-fields-modal.php:21
msgid "Select Field Type"
msgstr "Select Field Type"

#: includes/admin/views/browse-fields-modal.php:4
msgid "Popular"
msgstr "Popular"

#: includes/admin/views/acf-taxonomy/list-empty.php:15
msgid "Add Taxonomy"
msgstr "Add Taxonomy"

#: includes/admin/views/acf-taxonomy/list-empty.php:14
msgid "Create custom taxonomies to classify post type content"
msgstr "Create custom taxonomies to classify post type content"

#: includes/admin/views/acf-taxonomy/list-empty.php:13
msgid "Add Your First Taxonomy"
msgstr "Add Your First Taxonomy"

#: includes/admin/views/acf-taxonomy/basic-settings.php:122
msgid "Hierarchical taxonomies can have descendants (like categories)."
msgstr "Hierarchical taxonomies can have descendants (like categories)."

#: includes/admin/views/acf-taxonomy/basic-settings.php:107
msgid "Makes a taxonomy visible on the frontend and in the admin dashboard."
msgstr "Makes a taxonomy visible on the frontend and in the admin dashboard."

#: includes/admin/views/acf-taxonomy/basic-settings.php:91
msgid "One or many post types that can be classified with this taxonomy."
msgstr "One or many post types that can be classified with this taxonomy."

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:60
msgid "genre"
msgstr "genre"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:42
msgid "Genre"
msgstr "Genre"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:25
msgid "Genres"
msgstr "Genres"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1231
msgid ""
"Optional custom controller to use instead of `WP_REST_Terms_Controller `."
msgstr ""
"Optional custom controller to use instead of `WP_REST_Terms_Controller `."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1175
msgid "Expose this post type in the REST API."
msgstr "Expose this post type in the REST API."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1075
msgid "Customize the query variable name"
msgstr "Customise the query variable name"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1048
msgid ""
"Terms can be accessed using the non-pretty permalink, e.g., {query_var}"
"={term_slug}."
msgstr ""
"Terms can be accessed using the non-pretty permalink, e.g., {query_var}"
"={term_slug}."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1001
msgid "Parent-child terms in URLs for hierarchical taxonomies."
msgstr "Parent-child terms in URLs for hierarchical taxonomies."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:961
msgid "Customize the slug used in the URL"
msgstr "Customise the slug used in the URL"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:944
msgid "Permalinks for this taxonomy are disabled."
msgstr "Permalinks for this taxonomy are disabled."

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-taxonomy/advanced-settings.php:941
msgid ""
"Rewrite the URL using the taxonomy key as the slug. Your permalink structure "
"will be"
msgstr ""
"Rewrite the URL using the taxonomy key as the slug. Your permalink structure "
"will be"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:933
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1050
#: includes/admin/views/acf-taxonomy/basic-settings.php:57
msgid "Taxonomy Key"
msgstr "Taxonomy Key"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:931
msgid "Select the type of permalink to use for this taxonomy."
msgstr "Select the type of permalink to use for this taxonomy."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:916
msgid "Display a column for the taxonomy on post type listing screens."
msgstr "Display a column for the taxonomy on post type listing screens."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:915
msgid "Show Admin Column"
msgstr "Show Admin Column"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:902
msgid "Show the taxonomy in the quick/bulk edit panel."
msgstr "Show the taxonomy in the quick/bulk edit panel."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:901
msgid "Quick Edit"
msgstr "Quick Edit"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:888
msgid "List the taxonomy in the Tag Cloud Widget controls."
msgstr "List the taxonomy in the Tag Cloud Widget controls."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:887
msgid "Tag Cloud"
msgstr "Tag Cloud"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:842
msgid ""
"A PHP function name to be called for sanitizing taxonomy data saved from a "
"meta box."
msgstr ""
"A PHP function name to be called for sanitising taxonomy data saved from a "
"meta box."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:841
msgid "Meta Box Sanitization Callback"
msgstr "Meta Box Sanitisation Callback"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:821
msgid "Register Meta Box Callback"
msgstr "Register Meta Box Callback"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:752
msgid "No Meta Box"
msgstr "No Meta Box"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:751
msgid "Custom Meta Box"
msgstr "Custom Meta Box"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:768
msgid ""
"Controls the meta box on the content editor screen. By default, the "
"Categories meta box is shown for hierarchical taxonomies, and the Tags meta "
"box is shown for non-hierarchical taxonomies."
msgstr ""
"Controls the meta box on the content editor screen. By default, the "
"Categories meta box is shown for hierarchical taxonomies, and the Tags meta "
"box is shown for non-hierarchical taxonomies."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:767
msgid "Meta Box"
msgstr "Meta Box"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:746
#: includes/admin/views/acf-taxonomy/advanced-settings.php:773
msgid "Categories Meta Box"
msgstr "Categories Meta Box"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:745
#: includes/admin/views/acf-taxonomy/advanced-settings.php:772
msgid "Tags Meta Box"
msgstr "Tags Meta Box"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:704
msgid "A link to a tag"
msgstr "A link to a tag"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:703
msgid "Describes a navigation link block variation used in the block editor."
msgstr "Describes a navigation link block variation used in the block editor."

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:698
msgid "A link to a %s"
msgstr "A link to a %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:683
msgid "Tag Link"
msgstr "Tag Link"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:682
msgid ""
"Assigns a title for navigation link block variation used in the block editor."
msgstr ""
"Assigns a title for navigation link block variation used in the block editor."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:663
msgid "← Go to tags"
msgstr "← Go to tags"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:662
msgid ""
"Assigns the text used to link back to the main index after updating a term."
msgstr ""
"Assigns the text used to link back to the main index after updating a term."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:661
msgid "Back To Items"
msgstr "Back To Items"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:657
msgid "← Go to %s"
msgstr "← Go to %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:642
msgid "Tags list"
msgstr "Tags list"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:641
msgid "Assigns text to the table hidden heading."
msgstr "Assigns text to the table hidden heading."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:622
msgid "Tags list navigation"
msgstr "Tags list navigation"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:621
msgid "Assigns text to the table pagination hidden heading."
msgstr "Assigns text to the table pagination hidden heading."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:597
msgid "Filter by category"
msgstr "Filter by category"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:596
msgid "Assigns text to the filter button in the posts lists table."
msgstr "Assigns text to the filter button in the posts lists table."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:595
msgid "Filter By Item"
msgstr "Filter By Item"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:591
msgid "Filter by %s"
msgstr "Filter by %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:575
#: includes/admin/views/acf-taxonomy/advanced-settings.php:576
msgid ""
"The description is not prominent by default; however, some themes may show "
"it."
msgstr ""
"The description is not prominent by default; however, some themes may show "
"it."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:574
msgid "Describes the Description field on the Edit Tags screen."
msgstr "Describes the Description field on the Edit Tags screen."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:573
msgid "Description Field Description"
msgstr "Description Field Description"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:554
#: includes/admin/views/acf-taxonomy/advanced-settings.php:555
msgid ""
"Assign a parent term to create a hierarchy. The term Jazz, for example, "
"would be the parent of Bebop and Big Band"
msgstr ""
"Assign a parent term to create a hierarchy. The term Jazz, for example, "
"would be the parent of Bebop and Big Band"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:553
msgid "Describes the Parent field on the Edit Tags screen."
msgstr "Describes the Parent field on the Edit Tags screen."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:552
msgid "Parent Field Description"
msgstr "Parent Field Description"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:538
#: includes/admin/views/acf-taxonomy/advanced-settings.php:539
msgid ""
"The \"slug\" is the URL-friendly version of the name. It is usually all "
"lower case and contains only letters, numbers, and hyphens."
msgstr ""
"The \"slug\" is the URL-friendly version of the name. It is usually all "
"lower case and contains only letters, numbers, and hyphens."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:537
msgid "Describes the Slug field on the Edit Tags screen."
msgstr "Describes the Slug field on the Edit Tags screen."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:536
msgid "Slug Field Description"
msgstr "Slug Field Description"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:522
#: includes/admin/views/acf-taxonomy/advanced-settings.php:523
msgid "The name is how it appears on your site"
msgstr "The name is how it appears on your site"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:521
msgid "Describes the Name field on the Edit Tags screen."
msgstr "Describes the Name field on the Edit Tags screen."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:520
msgid "Name Field Description"
msgstr "Name Field Description"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:507
msgid "No tags"
msgstr "No tags"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:506
msgid ""
"Assigns the text displayed in the posts and media list tables when no tags "
"or categories are available."
msgstr ""
"Assigns the text displayed in the posts and media list tables when no tags "
"or categories are available."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:505
msgid "No Terms"
msgstr "No Terms"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:501
msgid "No %s"
msgstr "No %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:486
msgid "No tags found"
msgstr "No tags found"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:485
msgid ""
"Assigns the text displayed when clicking the 'choose from most used' text in "
"the taxonomy meta box when no tags are available, and assigns the text used "
"in the terms list table when there are no items for a taxonomy."
msgstr ""
"Assigns the text displayed when clicking the 'choose from most used' text in "
"the taxonomy meta box when no tags are available, and assigns the text used "
"in the terms list table when there are no items for a taxonomy."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:484
msgid "Not Found"
msgstr "Not Found"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:463
msgid "Assigns text to the Title field of the Most Used tab."
msgstr "Assigns text to the Title field of the Most Used tab."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:462
#: includes/admin/views/acf-taxonomy/advanced-settings.php:464
#: includes/admin/views/acf-taxonomy/advanced-settings.php:465
msgid "Most Used"
msgstr "Most Used"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:444
msgid "Choose from the most used tags"
msgstr "Choose from the most used tags"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:443
msgid ""
"Assigns the 'choose from most used' text used in the meta box when "
"JavaScript is disabled. Only used on non-hierarchical taxonomies."
msgstr ""
"Assigns the 'choose from most used' text used in the meta box when "
"JavaScript is disabled. Only used on non-hierarchical taxonomies."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:442
msgid "Choose From Most Used"
msgstr "Choose From Most Used"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:438
msgid "Choose from the most used %s"
msgstr "Choose from the most used %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:418
msgid "Add or remove tags"
msgstr "Add or remove tags"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:417
msgid ""
"Assigns the add or remove items text used in the meta box when JavaScript is "
"disabled. Only used on non-hierarchical taxonomies"
msgstr ""
"Assigns the add or remove items text used in the meta box when JavaScript is "
"disabled. Only used on non-hierarchical taxonomies"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:416
msgid "Add Or Remove Items"
msgstr "Add Or Remove Items"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:412
msgid "Add or remove %s"
msgstr "Add or remove %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:392
msgid "Separate tags with commas"
msgstr "Separate tags with commas"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:391
msgid ""
"Assigns the separate item with commas text used in the taxonomy meta box. "
"Only used on non-hierarchical taxonomies."
msgstr ""
"Assigns the separate item with commas text used in the taxonomy meta box. "
"Only used on non-hierarchical taxonomies."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:390
msgid "Separate Items With Commas"
msgstr "Separate Items With Commas"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:386
msgid "Separate %s with commas"
msgstr "Separate %s with commas"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:366
msgid "Popular Tags"
msgstr "Popular Tags"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:365
msgid "Assigns popular items text. Only used for non-hierarchical taxonomies."
msgstr "Assigns popular items text. Only used for non-hierarchical taxonomies."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:364
msgid "Popular Items"
msgstr "Popular Items"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:361
msgid "Popular %s"
msgstr "Popular %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:347
msgid "Search Tags"
msgstr "Search Tags"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:346
msgid "Assigns search items text."
msgstr "Assigns search items text."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:323
msgid "Parent Category:"
msgstr "Parent Category:"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:322
msgid "Assigns parent item text, but with a colon (:) added to the end."
msgstr "Assigns parent item text, but with a colon (:) added to the end."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:321
msgid "Parent Item With Colon"
msgstr "Parent Item With Colon"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:298
msgid "Parent Category"
msgstr "Parent Category"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:297
msgid "Assigns parent item text. Only used on hierarchical taxonomies."
msgstr "Assigns parent item text. Only used on hierarchical taxonomies."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:296
msgid "Parent Item"
msgstr "Parent Item"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:293
msgid "Parent %s"
msgstr "Parent %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:278
msgid "New Tag Name"
msgstr "New Tag Name"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:277
msgid "Assigns the new item name text."
msgstr "Assigns the new item name text."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:276
msgid "New Item Name"
msgstr "New Item Name"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:273
msgid "New %s Name"
msgstr "New %s Name"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:258
msgid "Add New Tag"
msgstr "Add New Tag"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:257
msgid "Assigns the add new item text."
msgstr "Assigns the add new item text."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:238
msgid "Update Tag"
msgstr "Update Tag"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:237
msgid "Assigns the update item text."
msgstr "Assigns the update item text."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:236
msgid "Update Item"
msgstr "Update Item"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:233
msgid "Update %s"
msgstr "Update %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:218
msgid "View Tag"
msgstr "View Tag"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:217
msgid "In the admin bar to view term during editing."
msgstr "In the admin bar to view term during editing."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:198
msgid "Edit Tag"
msgstr "Edit Tag"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:197
msgid "At the top of the editor screen when editing a term."
msgstr "At the top of the editor screen when editing a term."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:178
msgid "All Tags"
msgstr "All Tags"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:177
msgid "Assigns the all items text."
msgstr "Assigns the all items text."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:158
msgid "Assigns the menu name text."
msgstr "Assigns the menu name text."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:157
msgid "Menu Label"
msgstr "Menu Label"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:131
msgid "Active taxonomies are enabled and registered with WordPress."
msgstr "Active taxonomies are enabled and registered with WordPress."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:115
msgid "A descriptive summary of the taxonomy."
msgstr "A descriptive summary of the taxonomy."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:95
msgid "A descriptive summary of the term."
msgstr "A descriptive summary of the term."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:94
msgid "Term Description"
msgstr "Term Description"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:76
msgid "Single word, no spaces. Underscores and dashes allowed."
msgstr "Single word, no spaces. Underscores and dashes allowed."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:75
msgid "Term Slug"
msgstr "Term Slug"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:56
msgid "The name of the default term."
msgstr "The name of the default term."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:55
msgid "Term Name"
msgstr "Term Name"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:41
msgid ""
"Create a term for the taxonomy that cannot be deleted. It will not be "
"selected for posts by default."
msgstr ""
"Create a term for the taxonomy that cannot be deleted. It will not be "
"selected for posts by default."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:40
msgid "Default Term"
msgstr "Default Term"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:28
msgid ""
"Whether terms in this taxonomy should be sorted in the order they are "
"provided to `wp_set_object_terms()`."
msgstr ""
"Whether terms in this taxonomy should be sorted in the order they are "
"provided to `wp_set_object_terms()`."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:27
msgid "Sort Terms"
msgstr "Sort Terms"

#: includes/admin/views/acf-post-type/list-empty.php:14
msgid "Add Post Type"
msgstr "Add Post Type"

#: includes/admin/views/acf-post-type/list-empty.php:13
msgid ""
"Expand the functionality of WordPress beyond standard posts and pages with "
"custom post types."
msgstr ""
"Expand the functionality of WordPress beyond standard posts and pages with "
"custom post types."

#: includes/admin/views/acf-post-type/list-empty.php:12
msgid "Add Your First Post Type"
msgstr "Add Your First Post Type"

#: includes/admin/views/acf-post-type/basic-settings.php:136
#: includes/admin/views/acf-taxonomy/basic-settings.php:135
msgid "I know what I'm doing, show me all the options."
msgstr "I know what I'm doing, show me all the options."

#: includes/admin/views/acf-post-type/basic-settings.php:135
#: includes/admin/views/acf-taxonomy/basic-settings.php:134
msgid "Advanced Configuration"
msgstr "Advanced Configuration"

#: includes/admin/views/acf-post-type/basic-settings.php:123
msgid "Hierarchical post types can have descendants (like pages)."
msgstr "Hierarchical post types can have descendants (like pages)."

#: includes/admin/views/acf-post-type/basic-settings.php:122
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1000
#: includes/admin/views/acf-taxonomy/basic-settings.php:121
msgid "Hierarchical"
msgstr "Hierarchical"

#: includes/admin/views/acf-post-type/basic-settings.php:107
msgid "Visible on the frontend and in the admin dashboard."
msgstr "Visible on the frontend and in the admin dashboard."

#: includes/admin/views/acf-post-type/basic-settings.php:106
#: includes/admin/views/acf-taxonomy/basic-settings.php:106
msgid "Public"
msgstr "Public"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:59
msgid "movie"
msgstr "movie"

#: includes/admin/views/acf-post-type/basic-settings.php:57
msgid "Lower case letters, underscores and dashes only, Max 20 characters."
msgstr "Lower case letters, underscores and dashes only, Max 20 characters."

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:41
msgid "Movie"
msgstr "Movie"

#: includes/admin/views/acf-post-type/basic-settings.php:39
#: includes/admin/views/acf-taxonomy/basic-settings.php:40
msgid "Singular Label"
msgstr "Singular Label"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:24
msgid "Movies"
msgstr "Movies"

#: includes/admin/views/acf-post-type/basic-settings.php:22
#: includes/admin/views/acf-taxonomy/basic-settings.php:23
msgid "Plural Label"
msgstr "Plural Label"

#: includes/admin/views/acf-post-type/advanced-settings.php:1313
msgid ""
"Optional custom controller to use instead of `WP_REST_Posts_Controller`."
msgstr ""
"Optional custom controller to use instead of `WP_REST_Posts_Controller`."

#: includes/admin/views/acf-post-type/advanced-settings.php:1312
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1230
msgid "Controller Class"
msgstr "Controller Class"

#: includes/admin/views/acf-post-type/advanced-settings.php:1294
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1211
msgid "The namespace part of the REST API URL."
msgstr "The namespace part of the REST API URL."

#: includes/admin/views/acf-post-type/advanced-settings.php:1293
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1210
msgid "Namespace Route"
msgstr "Namespace Route"

#: includes/admin/views/acf-post-type/advanced-settings.php:1275
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1192
msgid "The base URL for the post type REST API URLs."
msgstr "The base URL for the post type REST API URLs."

#: includes/admin/views/acf-post-type/advanced-settings.php:1274
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1191
msgid "Base URL"
msgstr "Base URL"

#: includes/admin/views/acf-post-type/advanced-settings.php:1260
msgid ""
"Exposes this post type in the REST API. Required to use the block editor."
msgstr ""
"Exposes this post type in the REST API. Required to use the block editor."

#: includes/admin/views/acf-post-type/advanced-settings.php:1259
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1174
msgid "Show In REST API"
msgstr "Show In REST API"

#: includes/admin/views/acf-post-type/advanced-settings.php:1238
msgid "Customize the query variable name."
msgstr "Customise the query variable name."

#: includes/admin/views/acf-post-type/advanced-settings.php:1237
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1074
msgid "Query Variable"
msgstr "Query Variable"

#: includes/admin/views/acf-post-type/advanced-settings.php:1215
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1052
msgid "No Query Variable Support"
msgstr "No Query Variable Support"

#: includes/admin/views/acf-post-type/advanced-settings.php:1214
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1051
msgid "Custom Query Variable"
msgstr "Custom Query Variable"

#: includes/admin/views/acf-post-type/advanced-settings.php:1211
msgid ""
"Items can be accessed using the non-pretty permalink, eg. {post_type}"
"={post_slug}."
msgstr ""
"Items can be accessed using the non-pretty permalink, eg. {post_type}"
"={post_slug}."

#: includes/admin/views/acf-post-type/advanced-settings.php:1210
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1047
msgid "Query Variable Support"
msgstr "Query Variable Support"

#: includes/admin/views/acf-post-type/advanced-settings.php:1185
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1023
msgid "URLs for an item and items can be accessed with a query string."
msgstr "URLs for an item and items can be accessed with a query string."

#: includes/admin/views/acf-post-type/advanced-settings.php:1184
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1022
msgid "Publicly Queryable"
msgstr "Publicly Queryable"

#: includes/admin/views/acf-post-type/advanced-settings.php:1163
msgid "Custom slug for the Archive URL."
msgstr "Custom slug for the Archive URL."

#: includes/admin/views/acf-post-type/advanced-settings.php:1162
msgid "Archive Slug"
msgstr "Archive Slug"

#: includes/admin/views/acf-post-type/advanced-settings.php:1149
msgid ""
"Has an item archive that can be customized with an archive template file in "
"your theme."
msgstr ""
"Has an item archive that can be customised with an archive template file in "
"your theme."

#: includes/admin/views/acf-post-type/advanced-settings.php:1148
msgid "Archive"
msgstr "Archive"

#: includes/admin/views/acf-post-type/advanced-settings.php:1128
msgid "Pagination support for the items URLs such as the archives."
msgstr "Pagination support for the items URLs such as the archives."

#: includes/admin/views/acf-post-type/advanced-settings.php:1127
msgid "Pagination"
msgstr "Pagination"

#: includes/admin/views/acf-post-type/advanced-settings.php:1110
msgid "RSS feed URL for the post type items."
msgstr "RSS feed URL for the post type items."

#: includes/admin/views/acf-post-type/advanced-settings.php:1109
msgid "Feed URL"
msgstr "Feed URL"

#: includes/admin/views/acf-post-type/advanced-settings.php:1091
#: includes/admin/views/acf-taxonomy/advanced-settings.php:981
msgid ""
"Alters the permalink structure to add the `WP_Rewrite::$front` prefix to "
"URLs."
msgstr ""
"Alters the permalink structure to add the `WP_Rewrite::$front` prefix to "
"URLs."

#: includes/admin/views/acf-post-type/advanced-settings.php:1090
#: includes/admin/views/acf-taxonomy/advanced-settings.php:980
msgid "Front URL Prefix"
msgstr "Front URL Prefix"

#: includes/admin/views/acf-post-type/advanced-settings.php:1071
msgid "Customize the slug used in the URL."
msgstr "Customise the slug used in the URL."

#: includes/admin/views/acf-post-type/advanced-settings.php:1070
#: includes/admin/views/acf-taxonomy/advanced-settings.php:960
msgid "URL Slug"
msgstr "URL Slug"

#: includes/admin/views/acf-post-type/advanced-settings.php:1054
msgid "Permalinks for this post type are disabled."
msgstr "Permalinks for this post type are disabled."

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1053
#: includes/admin/views/acf-taxonomy/advanced-settings.php:943
msgid ""
"Rewrite the URL using a custom slug defined in the input below. Your "
"permalink structure will be"
msgstr ""
"Rewrite the URL using a custom slug defined in the input below. Your "
"permalink structure will be"

#: includes/admin/views/acf-post-type/advanced-settings.php:1045
#: includes/admin/views/acf-taxonomy/advanced-settings.php:935
msgid "No Permalink (prevent URL rewriting)"
msgstr "No Permalink (prevent URL rewriting)"

#: includes/admin/views/acf-post-type/advanced-settings.php:1044
#: includes/admin/views/acf-taxonomy/advanced-settings.php:934
msgid "Custom Permalink"
msgstr "Custom Permalink"

#: includes/admin/views/acf-post-type/advanced-settings.php:1043
#: includes/admin/views/acf-post-type/advanced-settings.php:1213
#: includes/admin/views/acf-post-type/basic-settings.php:56
msgid "Post Type Key"
msgstr "Post Type Key"

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1041
#: includes/admin/views/acf-post-type/advanced-settings.php:1051
msgid ""
"Rewrite the URL using the post type key as the slug. Your permalink "
"structure will be"
msgstr ""
"Rewrite the URL using the post type key as the slug. Your permalink "
"structure will be"

#: includes/admin/views/acf-post-type/advanced-settings.php:1039
#: includes/admin/views/acf-taxonomy/advanced-settings.php:930
msgid "Permalink Rewrite"
msgstr "Permalink Rewrite"

#: includes/admin/views/acf-post-type/advanced-settings.php:1025
msgid "Delete items by a user when that user is deleted."
msgstr "Delete items by a user when that user is deleted."

#: includes/admin/views/acf-post-type/advanced-settings.php:1024
msgid "Delete With User"
msgstr "Delete With User"

#: includes/admin/views/acf-post-type/advanced-settings.php:1010
msgid "Allow the post type to be exported from 'Tools' > 'Export'."
msgstr "Allow the post type to be exported from 'Tools' > 'Export'."

#: includes/admin/views/acf-post-type/advanced-settings.php:1009
msgid "Can Export"
msgstr "Can Export"

#: includes/admin/views/acf-post-type/advanced-settings.php:978
msgid "Optionally provide a plural to be used in capabilities."
msgstr "Optionally provide a plural to be used in capabilities."

#: includes/admin/views/acf-post-type/advanced-settings.php:977
msgid "Plural Capability Name"
msgstr "Plural Capability Name"

#: includes/admin/views/acf-post-type/advanced-settings.php:959
msgid "Choose another post type to base the capabilities for this post type."
msgstr "Choose another post type to base the capabilities for this post type."

#: includes/admin/views/acf-post-type/advanced-settings.php:958
msgid "Singular Capability Name"
msgstr "Singular Capability Name"

#: includes/admin/views/acf-post-type/advanced-settings.php:944
msgid ""
"By default the capabilities of the post type will inherit the 'Post' "
"capability names, eg. edit_post, delete_posts. Enable to use post type "
"specific capabilities, eg. edit_{singular}, delete_{plural}."
msgstr ""
"By default the capabilities of the post type will inherit the 'Post' "
"capability names, eg. edit_post, delete_posts. Enable to use post type "
"specific capabilities, eg. edit_{singular}, delete_{plural}."

#: includes/admin/views/acf-post-type/advanced-settings.php:943
msgid "Rename Capabilities"
msgstr "Rename Capabilities"

#: includes/admin/views/acf-post-type/advanced-settings.php:928
msgid "Exclude From Search"
msgstr "Exclude From Search"

#: includes/admin/views/acf-post-type/advanced-settings.php:915
#: includes/admin/views/acf-taxonomy/advanced-settings.php:874
msgid ""
"Allow items to be added to menus in the 'Appearance' > 'Menus' screen. Must "
"be turned on in 'Screen options'."
msgstr ""
"Allow items to be added to menus in the 'Appearance' > 'Menus' screen. Must "
"be turned on in 'Screen options'."

#: includes/admin/views/acf-post-type/advanced-settings.php:914
#: includes/admin/views/acf-taxonomy/advanced-settings.php:873
msgid "Appearance Menus Support"
msgstr "Appearance Menus Support"

#: includes/admin/views/acf-post-type/advanced-settings.php:896
msgid "Appears as an item in the 'New' menu in the admin bar."
msgstr "Appears as an item in the 'New' menu in the admin bar."

#: includes/admin/views/acf-post-type/advanced-settings.php:895
msgid "Show In Admin Bar"
msgstr "Show In Admin Bar"

#: includes/admin/views/acf-post-type/advanced-settings.php:861
msgid "Custom Meta Box Callback"
msgstr "Custom Meta Box Callback"

#: includes/admin/views/acf-post-type/advanced-settings.php:822
#: includes/fields/class-acf-field-icon_picker.php:643
msgid "Menu Icon"
msgstr "Menu Icon"

#: includes/admin/views/acf-post-type/advanced-settings.php:778
msgid "The position in the sidebar menu in the admin dashboard."
msgstr "The position in the sidebar menu in the admin dashboard."

#: includes/admin/views/acf-post-type/advanced-settings.php:777
msgid "Menu Position"
msgstr "Menu Position"

#: includes/admin/views/acf-post-type/advanced-settings.php:759
msgid ""
"By default the post type will get a new top level item in the admin menu. If "
"an existing top level item is supplied here, the post type will be added as "
"a submenu item under it."
msgstr ""
"By default the post type will get a new top level item in the admin menu. If "
"an existing top level item is supplied here, the post type will be added as "
"a submenu item under it."

#: includes/admin/views/acf-post-type/advanced-settings.php:758
msgid "Admin Menu Parent"
msgstr "Admin Menu Parent"

#: includes/admin/views/acf-post-type/advanced-settings.php:739
#: includes/admin/views/acf-taxonomy/advanced-settings.php:734
msgid "Admin editor navigation in the sidebar menu."
msgstr "Admin editor navigation in the sidebar menu."

#: includes/admin/views/acf-post-type/advanced-settings.php:738
#: includes/admin/views/acf-taxonomy/advanced-settings.php:733
msgid "Show In Admin Menu"
msgstr "Show In Admin Menu"

#: includes/admin/views/acf-post-type/advanced-settings.php:725
#: includes/admin/views/acf-taxonomy/advanced-settings.php:719
msgid "Items can be edited and managed in the admin dashboard."
msgstr "Items can be edited and managed in the admin dashboard."

#: includes/admin/views/acf-post-type/advanced-settings.php:724
#: includes/admin/views/acf-taxonomy/advanced-settings.php:718
msgid "Show In UI"
msgstr "Show In UI"

#: includes/admin/views/acf-post-type/advanced-settings.php:694
msgid "A link to a post."
msgstr "A link to a post."

#: includes/admin/views/acf-post-type/advanced-settings.php:693
msgid "Description for a navigation link block variation."
msgstr "Description for a navigation link block variation."

#: includes/admin/views/acf-post-type/advanced-settings.php:692
#: includes/admin/views/acf-taxonomy/advanced-settings.php:702
msgid "Item Link Description"
msgstr "Item Link Description"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:688
msgid "A link to a %s."
msgstr "A link to a %s."

#: includes/admin/views/acf-post-type/advanced-settings.php:673
msgid "Post Link"
msgstr "Post Link"

#: includes/admin/views/acf-post-type/advanced-settings.php:672
msgid "Title for a navigation link block variation."
msgstr "Title for a navigation link block variation."

#: includes/admin/views/acf-post-type/advanced-settings.php:671
#: includes/admin/views/acf-taxonomy/advanced-settings.php:681
msgid "Item Link"
msgstr "Item Link"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:668
#: includes/admin/views/acf-taxonomy/advanced-settings.php:678
msgid "%s Link"
msgstr "%s Link"

#: includes/admin/views/acf-post-type/advanced-settings.php:653
msgid "Post updated."
msgstr "Post updated."

#: includes/admin/views/acf-post-type/advanced-settings.php:652
msgid "In the editor notice after an item is updated."
msgstr "In the editor notice after an item is updated."

#: includes/admin/views/acf-post-type/advanced-settings.php:651
msgid "Item Updated"
msgstr "Item Updated"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:648
msgid "%s updated."
msgstr "%s updated."

#: includes/admin/views/acf-post-type/advanced-settings.php:633
msgid "Post scheduled."
msgstr "Post scheduled."

#: includes/admin/views/acf-post-type/advanced-settings.php:632
msgid "In the editor notice after scheduling an item."
msgstr "In the editor notice after scheduling an item."

#: includes/admin/views/acf-post-type/advanced-settings.php:631
msgid "Item Scheduled"
msgstr "Item Scheduled"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:628
msgid "%s scheduled."
msgstr "%s scheduled."

#: includes/admin/views/acf-post-type/advanced-settings.php:613
msgid "Post reverted to draft."
msgstr "Post reverted to draft."

#: includes/admin/views/acf-post-type/advanced-settings.php:612
msgid "In the editor notice after reverting an item to draft."
msgstr "In the editor notice after reverting an item to draft."

#: includes/admin/views/acf-post-type/advanced-settings.php:611
msgid "Item Reverted To Draft"
msgstr "Item Reverted To Draft"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:608
msgid "%s reverted to draft."
msgstr "%s reverted to draft."

#: includes/admin/views/acf-post-type/advanced-settings.php:593
msgid "Post published privately."
msgstr "Post published privately."

#: includes/admin/views/acf-post-type/advanced-settings.php:592
msgid "In the editor notice after publishing a private item."
msgstr "In the editor notice after publishing a private item."

#: includes/admin/views/acf-post-type/advanced-settings.php:591
msgid "Item Published Privately"
msgstr "Item Published Privately"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:588
msgid "%s published privately."
msgstr "%s published privately."

#: includes/admin/views/acf-post-type/advanced-settings.php:573
msgid "Post published."
msgstr "Post published."

#: includes/admin/views/acf-post-type/advanced-settings.php:572
msgid "In the editor notice after publishing an item."
msgstr "In the editor notice after publishing an item."

#: includes/admin/views/acf-post-type/advanced-settings.php:571
msgid "Item Published"
msgstr "Item Published"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:568
msgid "%s published."
msgstr "%s published."

#: includes/admin/views/acf-post-type/advanced-settings.php:553
msgid "Posts list"
msgstr "Posts list"

#: includes/admin/views/acf-post-type/advanced-settings.php:552
msgid "Used by screen readers for the items list on the post type list screen."
msgstr ""
"Used by screen readers for the items list on the post type list screen."

#: includes/admin/views/acf-post-type/advanced-settings.php:551
#: includes/admin/views/acf-taxonomy/advanced-settings.php:640
msgid "Items List"
msgstr "Items List"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:548
#: includes/admin/views/acf-taxonomy/advanced-settings.php:637
msgid "%s list"
msgstr "%s list"

#: includes/admin/views/acf-post-type/advanced-settings.php:533
msgid "Posts list navigation"
msgstr "Posts list navigation"

#: includes/admin/views/acf-post-type/advanced-settings.php:532
msgid ""
"Used by screen readers for the filter list pagination on the post type list "
"screen."
msgstr ""
"Used by screen readers for the filter list pagination on the post type list "
"screen."

#: includes/admin/views/acf-post-type/advanced-settings.php:531
#: includes/admin/views/acf-taxonomy/advanced-settings.php:620
msgid "Items List Navigation"
msgstr "Items List Navigation"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:528
#: includes/admin/views/acf-taxonomy/advanced-settings.php:617
msgid "%s list navigation"
msgstr "%s list navigation"

#: includes/admin/views/acf-post-type/advanced-settings.php:512
msgid "Filter posts by date"
msgstr "Filter posts by date"

#: includes/admin/views/acf-post-type/advanced-settings.php:511
msgid ""
"Used by screen readers for the filter by date heading on the post type list "
"screen."
msgstr ""
"Used by screen readers for the filter by date heading on the post type list "
"screen."

#: includes/admin/views/acf-post-type/advanced-settings.php:510
msgid "Filter Items By Date"
msgstr "Filter Items By Date"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:506
msgid "Filter %s by date"
msgstr "Filter %s by date"

#: includes/admin/views/acf-post-type/advanced-settings.php:491
msgid "Filter posts list"
msgstr "Filter posts list"

#: includes/admin/views/acf-post-type/advanced-settings.php:490
msgid ""
"Used by screen readers for the filter links heading on the post type list "
"screen."
msgstr ""
"Used by screen readers for the filter links heading on the post type list "
"screen."

#: includes/admin/views/acf-post-type/advanced-settings.php:489
msgid "Filter Items List"
msgstr "Filter Items List"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:485
msgid "Filter %s list"
msgstr "Filter %s list"

#: includes/admin/views/acf-post-type/advanced-settings.php:469
msgid "In the media modal showing all media uploaded to this item."
msgstr "In the media modal showing all media uploaded to this item."

#: includes/admin/views/acf-post-type/advanced-settings.php:468
msgid "Uploaded To This Item"
msgstr "Uploaded To This Item"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:464
msgid "Uploaded to this %s"
msgstr "Uploaded to this %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:449
msgid "Insert into post"
msgstr "Insert into post"

#: includes/admin/views/acf-post-type/advanced-settings.php:448
msgid "As the button label when adding media to content."
msgstr "As the button label when adding media to content."

#: includes/admin/views/acf-post-type/advanced-settings.php:447
msgid "Insert Into Media Button"
msgstr "Insert Into Media Button"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:443
msgid "Insert into %s"
msgstr "Insert into %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:428
msgid "Use as featured image"
msgstr "Use as featured image"

#: includes/admin/views/acf-post-type/advanced-settings.php:427
msgid ""
"As the button label for selecting to use an image as the featured image."
msgstr ""
"As the button label for selecting to use an image as the featured image."

#: includes/admin/views/acf-post-type/advanced-settings.php:426
msgid "Use Featured Image"
msgstr "Use Featured Image"

#: includes/admin/views/acf-post-type/advanced-settings.php:413
msgid "Remove featured image"
msgstr "Remove featured image"

#: includes/admin/views/acf-post-type/advanced-settings.php:412
msgid "As the button label when removing the featured image."
msgstr "As the button label when removing the featured image."

#: includes/admin/views/acf-post-type/advanced-settings.php:411
msgid "Remove Featured Image"
msgstr "Remove Featured Image"

#: includes/admin/views/acf-post-type/advanced-settings.php:398
msgid "Set featured image"
msgstr "Set featured image"

#: includes/admin/views/acf-post-type/advanced-settings.php:397
msgid "As the button label when setting the featured image."
msgstr "As the button label when setting the featured image."

#: includes/admin/views/acf-post-type/advanced-settings.php:396
msgid "Set Featured Image"
msgstr "Set Featured Image"

#: includes/admin/views/acf-post-type/advanced-settings.php:383
msgid "Featured image"
msgstr "Featured image"

#: includes/admin/views/acf-post-type/advanced-settings.php:382
msgid "In the editor used for the title of the featured image meta box."
msgstr "In the editor used for the title of the featured image meta box."

#: includes/admin/views/acf-post-type/advanced-settings.php:381
msgid "Featured Image Meta Box"
msgstr "Featured Image Meta Box"

#: includes/admin/views/acf-post-type/advanced-settings.php:368
msgid "Post Attributes"
msgstr "Post Attributes"

#: includes/admin/views/acf-post-type/advanced-settings.php:367
msgid "In the editor used for the title of the post attributes meta box."
msgstr "In the editor used for the title of the post attributes meta box."

#: includes/admin/views/acf-post-type/advanced-settings.php:366
msgid "Attributes Meta Box"
msgstr "Attributes Meta Box"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:363
msgid "%s Attributes"
msgstr "%s Attributes"

#: includes/admin/views/acf-post-type/advanced-settings.php:348
msgid "Post Archives"
msgstr "Post Archives"

#: includes/admin/views/acf-post-type/advanced-settings.php:347
msgid ""
"Adds 'Post Type Archive' items with this label to the list of posts shown "
"when adding items to an existing menu in a CPT with archives enabled. Only "
"appears when editing menus in 'Live Preview' mode and a custom archive slug "
"has been provided."
msgstr ""
"Adds 'Post Type Archive' items with this label to the list of posts shown "
"when adding items to an existing menu in a post type with archives enabled. "
"Only appears when editing menus in 'Live Preview' mode and a custom archive "
"slug has been provided."

#: includes/admin/views/acf-post-type/advanced-settings.php:346
msgid "Archives Nav Menu"
msgstr "Archives Nav Menu"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:343
msgid "%s Archives"
msgstr "%s Archives"

#: includes/admin/views/acf-post-type/advanced-settings.php:328
msgid "No posts found in Trash"
msgstr "No posts found in the bin"

#: includes/admin/views/acf-post-type/advanced-settings.php:327
msgid ""
"At the top of the post type list screen when there are no posts in the trash."
msgstr ""
"At the top of the post type list screen when there are no posts in the bin."

#: includes/admin/views/acf-post-type/advanced-settings.php:326
msgid "No Items Found in Trash"
msgstr "No Items Found in the bin"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:322
msgid "No %s found in Trash"
msgstr "No %s found in the bin"

#: includes/admin/views/acf-post-type/advanced-settings.php:307
msgid "No posts found"
msgstr "No posts found"

#: includes/admin/views/acf-post-type/advanced-settings.php:306
msgid ""
"At the top of the post type list screen when there are no posts to display."
msgstr ""
"At the top of the post type list screen when there are no posts to display."

#: includes/admin/views/acf-post-type/advanced-settings.php:305
msgid "No Items Found"
msgstr "No Items Found"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:301
#: includes/admin/views/acf-taxonomy/advanced-settings.php:480
msgid "No %s found"
msgstr "No %s found"

#: includes/admin/views/acf-post-type/advanced-settings.php:286
msgid "Search Posts"
msgstr "Search Posts"

#: includes/admin/views/acf-post-type/advanced-settings.php:285
msgid "At the top of the items screen when searching for an item."
msgstr "At the top of the items screen when searching for an item."

#: includes/admin/views/acf-post-type/advanced-settings.php:284
#: includes/admin/views/acf-taxonomy/advanced-settings.php:345
msgid "Search Items"
msgstr "Search Items"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:281
#: includes/admin/views/acf-taxonomy/advanced-settings.php:342
msgid "Search %s"
msgstr "Search %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:266
msgid "Parent Page:"
msgstr "Parent Page:"

#: includes/admin/views/acf-post-type/advanced-settings.php:265
msgid "For hierarchical types in the post type list screen."
msgstr "For hierarchical types in the post type list screen."

#: includes/admin/views/acf-post-type/advanced-settings.php:264
msgid "Parent Item Prefix"
msgstr "Parent Item Prefix"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:261
#: includes/admin/views/acf-taxonomy/advanced-settings.php:318
msgid "Parent %s:"
msgstr "Parent %s:"

#: includes/admin/views/acf-post-type/advanced-settings.php:246
msgid "New Post"
msgstr "New Post"

#: includes/admin/views/acf-post-type/advanced-settings.php:244
msgid "New Item"
msgstr "New Item"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:241
msgid "New %s"
msgstr "New %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:206
#: includes/admin/views/acf-post-type/advanced-settings.php:226
msgid "Add New Post"
msgstr "Add New Post"

#: includes/admin/views/acf-post-type/advanced-settings.php:205
msgid "At the top of the editor screen when adding a new item."
msgstr "At the top of the editor screen when adding a new item."

#: includes/admin/views/acf-post-type/advanced-settings.php:204
#: includes/admin/views/acf-taxonomy/advanced-settings.php:256
msgid "Add New Item"
msgstr "Add New Item"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:201
#: includes/admin/views/acf-post-type/advanced-settings.php:221
#: includes/admin/views/acf-taxonomy/advanced-settings.php:253
msgid "Add New %s"
msgstr "Add New %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:186
msgid "View Posts"
msgstr "View Posts"

#: includes/admin/views/acf-post-type/advanced-settings.php:185
msgid ""
"Appears in the admin bar in the 'All Posts' view, provided the post type "
"supports archives and the home page is not an archive of that post type."
msgstr ""
"Appears in the admin bar in the 'All Posts' view, provided the post type "
"supports archives and the home page is not an archive of that post type."

#: includes/admin/views/acf-post-type/advanced-settings.php:184
msgid "View Items"
msgstr "View Items"

#: includes/admin/views/acf-post-type/advanced-settings.php:166
msgid "View Post"
msgstr "View Post"

#: includes/admin/views/acf-post-type/advanced-settings.php:165
msgid "In the admin bar to view item when editing it."
msgstr "In the admin bar to view item when editing it."

#: includes/admin/views/acf-post-type/advanced-settings.php:164
#: includes/admin/views/acf-taxonomy/advanced-settings.php:216
msgid "View Item"
msgstr "View Item"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:161
#: includes/admin/views/acf-post-type/advanced-settings.php:181
#: includes/admin/views/acf-taxonomy/advanced-settings.php:213
msgid "View %s"
msgstr "View %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:146
msgid "Edit Post"
msgstr "Edit Post"

#: includes/admin/views/acf-post-type/advanced-settings.php:145
msgid "At the top of the editor screen when editing an item."
msgstr "At the top of the editor screen when editing an item."

#: includes/admin/views/acf-post-type/advanced-settings.php:144
#: includes/admin/views/acf-taxonomy/advanced-settings.php:196
msgid "Edit Item"
msgstr "Edit Item"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:141
#: includes/admin/views/acf-taxonomy/advanced-settings.php:193
msgid "Edit %s"
msgstr "Edit %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:126
msgid "All Posts"
msgstr "All Posts"

#: includes/admin/views/acf-post-type/advanced-settings.php:125
#: includes/admin/views/acf-post-type/advanced-settings.php:225
#: includes/admin/views/acf-post-type/advanced-settings.php:245
msgid "In the post type submenu in the admin dashboard."
msgstr "In the post type submenu in the admin dashboard."

#: includes/admin/views/acf-post-type/advanced-settings.php:124
#: includes/admin/views/acf-taxonomy/advanced-settings.php:176
msgid "All Items"
msgstr "All Items"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:121
#: includes/admin/views/acf-taxonomy/advanced-settings.php:173
msgid "All %s"
msgstr "All %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:105
msgid "Admin menu name for the post type."
msgstr "Admin menu name for the post type."

#: includes/admin/views/acf-post-type/advanced-settings.php:104
msgid "Menu Name"
msgstr "Menu Name"

#: includes/admin/views/acf-post-type/advanced-settings.php:90
#: includes/admin/views/acf-taxonomy/advanced-settings.php:142
msgid "Regenerate all labels using the Singular and Plural labels"
msgstr "Regenerate all labels using the Singular and Plural labels"

#: includes/admin/views/acf-post-type/advanced-settings.php:88
#: includes/admin/views/acf-taxonomy/advanced-settings.php:140
msgid "Regenerate"
msgstr "Regenerate"

#: includes/admin/views/acf-post-type/advanced-settings.php:79
msgid "Active post types are enabled and registered with WordPress."
msgstr "Active post types are enabled and registered with WordPress."

#: includes/admin/views/acf-post-type/advanced-settings.php:63
msgid "A descriptive summary of the post type."
msgstr "A descriptive summary of the post type."

#: includes/admin/views/acf-post-type/advanced-settings.php:48
msgid "Add Custom"
msgstr "Add Custom"

#: includes/admin/views/acf-post-type/advanced-settings.php:42
msgid "Enable various features in the content editor."
msgstr "Enable various features in the content editor."

#: includes/admin/views/acf-post-type/advanced-settings.php:31
msgid "Post Formats"
msgstr "Post Formats"

#: includes/admin/views/acf-post-type/advanced-settings.php:25
msgid "Editor"
msgstr "Editor"

#: includes/admin/views/acf-post-type/advanced-settings.php:24
msgid "Trackbacks"
msgstr "Trackbacks"

#: includes/admin/views/acf-post-type/basic-settings.php:87
msgid "Select existing taxonomies to classify items of the post type."
msgstr "Select existing taxonomies to classify items of the post type."

#: includes/admin/views/acf-field-group/field.php:158
msgid "Browse Fields"
msgstr "Browse Fields"

#: includes/admin/tools/class-acf-admin-tool-import.php:287
msgid "Nothing to import"
msgstr "Nothing to import"

#: includes/admin/tools/class-acf-admin-tool-import.php:282
msgid ". The Custom Post Type UI plugin can be deactivated."
msgstr ". The Custom Post Type UI plugin can be deactivated."

#. translators: %d - number of items imported from CPTUI
#: includes/admin/tools/class-acf-admin-tool-import.php:273
msgid "Imported %d item from Custom Post Type UI -"
msgid_plural "Imported %d items from Custom Post Type UI -"
msgstr[0] "Imported %d item from Custom Post Type UI -"
msgstr[1] "Imported %d items from Custom Post Type UI -"

#: includes/admin/tools/class-acf-admin-tool-import.php:257
msgid "Failed to import taxonomies."
msgstr "Failed to import taxonomies."

#: includes/admin/tools/class-acf-admin-tool-import.php:239
msgid "Failed to import post types."
msgstr "Failed to import post types."

#: includes/admin/tools/class-acf-admin-tool-import.php:228
msgid "Nothing from Custom Post Type UI plugin selected for import."
msgstr "Nothing from Custom Post Type UI plugin selected for import."

#: includes/admin/tools/class-acf-admin-tool-import.php:204
msgid "Imported 1 item"
msgid_plural "Imported %s items"
msgstr[0] "Imported 1 item"
msgstr[1] "Imported %s items"

#: includes/admin/tools/class-acf-admin-tool-import.php:119
msgid ""
"Importing a Post Type or Taxonomy with the same key as one that already "
"exists will overwrite the settings for the existing Post Type or Taxonomy "
"with those of the import."
msgstr ""
"Importing a Post Type or Taxonomy with the same key as one that already "
"exists will overwrite the settings for the existing Post Type or Taxonomy "
"with those of the import."

#: includes/admin/tools/class-acf-admin-tool-import.php:108
#: includes/admin/tools/class-acf-admin-tool-import.php:124
msgid "Import from Custom Post Type UI"
msgstr "Import from Custom Post Type UI"

#: includes/admin/tools/class-acf-admin-tool-export.php:354
msgid ""
"The following code can be used to register a local version of the selected "
"items. Storing field groups, post types, or taxonomies locally can provide "
"many benefits such as faster load times, version control & dynamic fields/"
"settings. Simply copy and paste the following code to your theme's "
"functions.php file or include it within an external file, then deactivate or "
"delete the items from the ACF admin."
msgstr ""
"The following code can be used to register a local version of the selected "
"items. Storing field groups, post types, or taxonomies locally can provide "
"many benefits such as faster load times, version control & dynamic fields/"
"settings. Simply copy and paste the following code to your theme's "
"functions.php file or include it within an external file, then deactivate or "
"delete the items from the ACF admin."

#: includes/admin/tools/class-acf-admin-tool-export.php:353
msgid "Export - Generate PHP"
msgstr "Export - Generate PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:330
msgid "Export"
msgstr "Export"

#: includes/admin/tools/class-acf-admin-tool-export.php:264
msgid "Select Taxonomies"
msgstr "Select Taxonomies"

#: includes/admin/tools/class-acf-admin-tool-export.php:239
msgid "Select Post Types"
msgstr "Select Post Types"

#: includes/admin/tools/class-acf-admin-tool-export.php:155
msgid "Exported 1 item."
msgid_plural "Exported %s items."
msgstr[0] "Exported 1 item."
msgstr[1] "Exported %s items."

#: includes/admin/post-types/admin-taxonomy.php:127
msgid "Category"
msgstr "Category"

#: includes/admin/post-types/admin-taxonomy.php:125
msgid "Tag"
msgstr "Tag"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:82
msgid "%s taxonomy created"
msgstr "%s taxonomy created"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:76
msgid "%s taxonomy updated"
msgstr "%s taxonomy updated"

#: includes/admin/post-types/admin-taxonomy.php:56
msgid "Taxonomy draft updated."
msgstr "Taxonomy draft updated."

#: includes/admin/post-types/admin-taxonomy.php:55
msgid "Taxonomy scheduled for."
msgstr "Taxonomy scheduled for."

#: includes/admin/post-types/admin-taxonomy.php:54
msgid "Taxonomy submitted."
msgstr "Taxonomy submitted."

#: includes/admin/post-types/admin-taxonomy.php:53
msgid "Taxonomy saved."
msgstr "Taxonomy saved."

#: includes/admin/post-types/admin-taxonomy.php:49
msgid "Taxonomy deleted."
msgstr "Taxonomy deleted."

#: includes/admin/post-types/admin-taxonomy.php:48
msgid "Taxonomy updated."
msgstr "Taxonomy updated."

#: includes/admin/post-types/admin-taxonomies.php:347
#: includes/admin/post-types/admin-taxonomy.php:153
msgid ""
"This taxonomy could not be registered because its key is in use by another "
"taxonomy registered by another plugin or theme."
msgstr ""
"This taxonomy could not be registered because its key is in use by another "
"taxonomy registered by another plugin or theme."

#. translators: %s number of taxonomies synchronized
#: includes/admin/post-types/admin-taxonomies.php:329
msgid "Taxonomy synchronized."
msgid_plural "%s taxonomies synchronized."
msgstr[0] "Taxonomy synchronised."
msgstr[1] "%s taxonomies synchronised."

#. translators: %s number of taxonomies duplicated
#: includes/admin/post-types/admin-taxonomies.php:322
msgid "Taxonomy duplicated."
msgid_plural "%s taxonomies duplicated."
msgstr[0] "Taxonomy duplicated."
msgstr[1] "%s taxonomies duplicated."

#. translators: %s number of taxonomies deactivated
#: includes/admin/post-types/admin-taxonomies.php:315
msgid "Taxonomy deactivated."
msgid_plural "%s taxonomies deactivated."
msgstr[0] "Taxonomy deactivated."
msgstr[1] "%s taxonomies deactivated."

#. translators: %s number of taxonomies activated
#: includes/admin/post-types/admin-taxonomies.php:308
msgid "Taxonomy activated."
msgid_plural "%s taxonomies activated."
msgstr[0] "Taxonomy activated."
msgstr[1] "%s taxonomies activated."

#: includes/admin/post-types/admin-taxonomies.php:109
msgid "Terms"
msgstr "Terms"

#. translators: %s number of post types synchronized
#: includes/admin/post-types/admin-post-types.php:323
msgid "Post type synchronized."
msgid_plural "%s post types synchronized."
msgstr[0] "Post type synchronised."
msgstr[1] "%s post types synchronised."

#. translators: %s number of post types duplicated
#: includes/admin/post-types/admin-post-types.php:316
msgid "Post type duplicated."
msgid_plural "%s post types duplicated."
msgstr[0] "Post type duplicated."
msgstr[1] "%s post types duplicated."

#. translators: %s number of post types deactivated
#: includes/admin/post-types/admin-post-types.php:309
msgid "Post type deactivated."
msgid_plural "%s post types deactivated."
msgstr[0] "Post type deactivated."
msgstr[1] "%s post types deactivated."

#. translators: %s number of post types activated
#: includes/admin/post-types/admin-post-types.php:302
msgid "Post type activated."
msgid_plural "%s post types activated."
msgstr[0] "Post type activated."
msgstr[1] "%s post types activated."

#: includes/admin/post-types/admin-post-types.php:84
#: includes/admin/post-types/admin-taxonomies.php:107
#: includes/admin/tools/class-acf-admin-tool-import.php:79
#: includes/admin/views/acf-taxonomy/basic-settings.php:82
#: includes/post-types/class-acf-post-type.php:91
msgid "Post Types"
msgstr "Post Types"

#: includes/admin/post-types/admin-post-type.php:158
#: includes/admin/post-types/admin-taxonomy.php:160
msgid "Advanced Settings"
msgstr "Advanced Settings"

#: includes/admin/post-types/admin-post-type.php:157
#: includes/admin/post-types/admin-taxonomy.php:159
msgid "Basic Settings"
msgstr "Basic Settings"

#: includes/admin/post-types/admin-post-type.php:151
#: includes/admin/post-types/admin-post-types.php:341
msgid ""
"This post type could not be registered because its key is in use by another "
"post type registered by another plugin or theme."
msgstr ""
"This post type could not be registered because its key is in use by another "
"post type registered by another plugin or theme."

#: includes/admin/post-types/admin-post-type.php:126
msgid "Pages"
msgstr "Pages"

#: includes/admin/admin-internal-post-type.php:347
msgid "Link Existing Field Groups"
msgstr "Link Existing Field Groups"

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:80
msgid "%s post type created"
msgstr "%s post type created"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:78
msgid "Add fields to %s"
msgstr "Add fields to %s"

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:76
msgid "%s post type updated"
msgstr "%s post type updated"

#: includes/admin/post-types/admin-post-type.php:56
msgid "Post type draft updated."
msgstr "Post type draft updated."

#: includes/admin/post-types/admin-post-type.php:55
msgid "Post type scheduled for."
msgstr "Post type scheduled for."

#: includes/admin/post-types/admin-post-type.php:54
msgid "Post type submitted."
msgstr "Post type submitted."

#: includes/admin/post-types/admin-post-type.php:53
msgid "Post type saved."
msgstr "Post type saved."

#: includes/admin/post-types/admin-post-type.php:50
msgid "Post type updated."
msgstr "Post type updated."

#: includes/admin/post-types/admin-post-type.php:49
msgid "Post type deleted."
msgstr "Post type deleted."

#: includes/admin/post-types/admin-field-group.php:145
msgid "Type to search..."
msgstr "Type to search..."

#: includes/admin/post-types/admin-field-group.php:100
msgid "PRO Only"
msgstr "PRO Only"

#: includes/admin/post-types/admin-field-group.php:92
msgid "Field groups linked successfully."
msgstr "Field groups linked successfully."

#. translators: %s - URL to ACF tools page.
#: includes/admin/admin.php:195
msgid ""
"Import Post Types and Taxonomies registered with Custom Post Type UI and "
"manage them with ACF. <a href=\"%s\">Get Started</a>."
msgstr ""
"Import Post Types and Taxonomies registered with Custom Post Type UI and "
"manage them with ACF. <a href=\"%s\">Get Started</a>."

#: includes/admin/admin.php:47 includes/admin/admin.php:385
#: src/Site_Health/Site_Health.php:254
msgid "ACF"
msgstr "ACF"

#: includes/admin/admin-internal-post-type.php:314
msgid "taxonomy"
msgstr "taxonomy"

#: includes/admin/admin-internal-post-type.php:314
msgid "post type"
msgstr "post type"

#: includes/admin/admin-internal-post-type.php:338
msgid "Done"
msgstr "Done"

#: includes/admin/admin-internal-post-type.php:324
msgid "Field Group(s)"
msgstr "Field Group(s)"

#: includes/admin/admin-internal-post-type.php:323
msgid "Select one or many field groups..."
msgstr "Select one or many field groups..."

#: includes/admin/admin-internal-post-type.php:322
msgid "Please select the field groups to link."
msgstr "Please select the field groups to link."

#: includes/admin/admin-internal-post-type.php:280
msgid "Field group linked successfully."
msgid_plural "Field groups linked successfully."
msgstr[0] "Field group linked successfully."
msgstr[1] "Field groups linked successfully."

#: includes/admin/admin-internal-post-type-list.php:277
#: includes/admin/post-types/admin-post-types.php:342
#: includes/admin/post-types/admin-taxonomies.php:348
msgctxt "post status"
msgid "Registration Failed"
msgstr "Registration Failed"

#: includes/admin/admin-internal-post-type-list.php:276
msgid ""
"This item could not be registered because its key is in use by another item "
"registered by another plugin or theme."
msgstr ""
"This item could not be registered because its key is in use by another item "
"registered by another plugin or theme."

#: includes/acf-internal-post-type-functions.php:509
#: includes/acf-internal-post-type-functions.php:538
msgid "REST API"
msgstr "REST API"

#: includes/acf-internal-post-type-functions.php:508
#: includes/acf-internal-post-type-functions.php:537
#: includes/acf-internal-post-type-functions.php:564
msgid "Permissions"
msgstr "Permissions"

#: includes/acf-internal-post-type-functions.php:507
#: includes/acf-internal-post-type-functions.php:536
msgid "URLs"
msgstr "URLs"

#: includes/acf-internal-post-type-functions.php:506
#: includes/acf-internal-post-type-functions.php:535
#: includes/acf-internal-post-type-functions.php:562
msgid "Visibility"
msgstr "Visibility"

#: includes/acf-internal-post-type-functions.php:505
#: includes/acf-internal-post-type-functions.php:534
#: includes/acf-internal-post-type-functions.php:563
msgid "Labels"
msgstr "Labels"

#: includes/admin/post-types/admin-field-group.php:278
msgid "Field Settings Tabs"
msgstr "Field Settings Tabs"

#. Author URI of the plugin
#: acf.php
msgid ""
"https://wpengine.com/?"
"utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"
msgstr ""
"https://wpengine.com/?"
"utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"

#: includes/api/api-template.php:1027
msgid "[ACF shortcode value disabled for preview]"
msgstr "[ACF shortcode value disabled for preview]"

#: includes/admin/admin-internal-post-type.php:290
#: includes/admin/post-types/admin-field-group.php:571
msgid "Close Modal"
msgstr "Close Modal"

#: includes/admin/post-types/admin-field-group.php:91
msgid "Field moved to other group"
msgstr "Field moved to other group"

#: includes/assets.php:351
msgid "Close modal"
msgstr "Close modal"

#: includes/fields/class-acf-field-tab.php:122
msgid "Start a new group of tabs at this tab."
msgstr "Start a new group of tabs at this tab."

#: includes/fields/class-acf-field-tab.php:121
msgid "New Tab Group"
msgstr "New Tab Group"

#: includes/fields/class-acf-field-select.php:421
#: includes/fields/class-acf-field-true_false.php:188
msgid "Use a stylized checkbox using select2"
msgstr "Use a stylised checkbox using select2"

#: includes/fields/class-acf-field-radio.php:250
msgid "Save Other Choice"
msgstr "Save Other Choice"

#: includes/fields/class-acf-field-radio.php:239
msgid "Allow Other Choice"
msgstr "Allow Other Choice"

#: includes/fields/class-acf-field-checkbox.php:433
msgid "Add Toggle All"
msgstr "Add Toggle All"

#: includes/fields/class-acf-field-checkbox.php:392
msgid "Save Custom Values"
msgstr "Save Custom Values"

#: includes/fields/class-acf-field-checkbox.php:381
msgid "Allow Custom Values"
msgstr "Allow Custom Values"

#: includes/fields/class-acf-field-checkbox.php:147
msgid "Checkbox custom values cannot be empty. Uncheck any empty values."
msgstr "Checkbox custom values cannot be empty. Uncheck any empty values."

#: includes/admin/views/global/navigation.php:256
msgid "Updates"
msgstr "Updates"

#: includes/admin/views/global/navigation.php:180
#: includes/admin/views/global/navigation.php:184
msgid "Advanced Custom Fields logo"
msgstr "Advanced Custom Fields logo"

#: includes/admin/views/global/form-top.php:92
msgid "Save Changes"
msgstr "Save Changes"

#: includes/admin/views/global/form-top.php:79
msgid "Field Group Title"
msgstr "Field Group Title"

#: includes/admin/views/acf-post-type/advanced-settings.php:709
#: includes/admin/views/global/form-top.php:3
msgid "Add title"
msgstr "Add title"

#. translators: %s url to getting started guide
#: includes/admin/views/acf-field-group/list-empty.php:30
#: includes/admin/views/acf-post-type/list-empty.php:20
#: includes/admin/views/acf-taxonomy/list-empty.php:21
#: includes/admin/views/options-page-preview.php:13
msgid ""
"New to ACF? Take a look at our <a href=\"%s\" target=\"_blank\">getting "
"started guide</a>."
msgstr ""
"New to ACF? Take a look at our <a href=\"%s\" target=\"_blank\">getting "
"started guide</a>."

#: includes/admin/views/acf-field-group/list-empty.php:24
msgid "Add Field Group"
msgstr "Add Field Group"

#. translators: %s url to creating a field group page
#: includes/admin/views/acf-field-group/list-empty.php:18
msgid ""
"ACF uses <a href=\"%s\" target=\"_blank\">field groups</a> to group custom "
"fields together, and then attach those fields to edit screens."
msgstr ""
"ACF uses <a href=\"%s\" target=\"_blank\">field groups</a> to group custom "
"fields together, and then attach those fields to edit screens."

#: includes/admin/views/acf-field-group/list-empty.php:12
msgid "Add Your First Field Group"
msgstr "Add Your First Field Group"

#: includes/admin/admin-options-pages-preview.php:28
#: includes/admin/views/acf-field-group/pro-features.php:58
#: includes/admin/views/global/navigation.php:86
#: includes/admin/views/global/navigation.php:258
msgid "Options Pages"
msgstr "Options Pages"

#: includes/admin/views/acf-field-group/pro-features.php:54
msgid "ACF Blocks"
msgstr "ACF Blocks"

#: includes/admin/views/acf-field-group/pro-features.php:62
msgid "Gallery Field"
msgstr "Gallery Field"

#: includes/admin/views/acf-field-group/pro-features.php:42
msgid "Flexible Content Field"
msgstr "Flexible Content Field"

#: includes/admin/views/acf-field-group/pro-features.php:46
msgid "Repeater Field"
msgstr "Repeater Field"

#: includes/admin/views/global/navigation.php:218
msgid "Unlock Extra Features with ACF PRO"
msgstr "Unlock Extra Features with ACF PRO"

#: includes/admin/views/acf-field-group/options.php:267
msgid "Delete Field Group"
msgstr "Delete Field Group"

#. translators: 1: Post creation date 2: Post creation time
#: includes/admin/views/acf-field-group/options.php:261
msgid "Created on %1$s at %2$s"
msgstr "Created on %1$s at %2$s"

#: includes/acf-field-group-functions.php:497
msgid "Group Settings"
msgstr "Group Settings"

#: includes/acf-field-group-functions.php:495
msgid "Location Rules"
msgstr "Location Rules"

#. translators: %s url to field types list
#: includes/admin/views/acf-field-group/fields.php:73
msgid ""
"Choose from over 30 field types. <a href=\"%s\" target=\"_blank\">Learn "
"more</a>."
msgstr ""
"Choose from over 30 field types. <a href=\"%s\" target=\"_blank\">Learn "
"more</a>."

#: includes/admin/views/acf-field-group/fields.php:65
msgid ""
"Get started creating new custom fields for your posts, pages, custom post "
"types and other WordPress content."
msgstr ""
"Get started creating new custom fields for your posts, pages, custom post "
"types and other WordPress content."

#: includes/admin/views/acf-field-group/fields.php:64
msgid "Add Your First Field"
msgstr "Add Your First Field"

#. translators: A symbol (or text, if not available in your locale) meaning
#. "Order Number", in terms of positional placement.
#: includes/admin/views/acf-field-group/fields.php:43
msgid "#"
msgstr "#"

#: includes/admin/views/acf-field-group/fields.php:33
#: includes/admin/views/acf-field-group/fields.php:67
#: includes/admin/views/acf-field-group/fields.php:101
#: includes/admin/views/global/form-top.php:88
msgid "Add Field"
msgstr "Add Field"

#: includes/acf-field-group-functions.php:496 includes/fields.php:385
msgid "Presentation"
msgstr "Presentation"

#: includes/fields.php:384
msgid "Validation"
msgstr "Validation"

#: includes/acf-internal-post-type-functions.php:504
#: includes/acf-internal-post-type-functions.php:533 includes/fields.php:383
msgid "General"
msgstr "General"

#: includes/admin/tools/class-acf-admin-tool-import.php:67
msgid "Import JSON"
msgstr "Import JSON"

#: includes/admin/tools/class-acf-admin-tool-export.php:338
msgid "Export As JSON"
msgstr "Export As JSON"

#. translators: %s number of field groups deactivated
#: includes/admin/post-types/admin-field-groups.php:356
msgid "Field group deactivated."
msgid_plural "%s field groups deactivated."
msgstr[0] "Field group deactivated."
msgstr[1] "%s field groups deactivated."

#. translators: %s number of field groups activated
#: includes/admin/post-types/admin-field-groups.php:349
msgid "Field group activated."
msgid_plural "%s field groups activated."
msgstr[0] "Field group activated."
msgstr[1] "%s field groups activated."

#: includes/admin/admin-internal-post-type-list.php:470
#: includes/admin/admin-internal-post-type-list.php:496
msgid "Deactivate"
msgstr "Deactivate"

#: includes/admin/admin-internal-post-type-list.php:470
msgid "Deactivate this item"
msgstr "Deactivate this item"

#: includes/admin/admin-internal-post-type-list.php:466
#: includes/admin/admin-internal-post-type-list.php:495
msgid "Activate"
msgstr "Activate"

#: includes/admin/admin-internal-post-type-list.php:466
msgid "Activate this item"
msgstr "Activate this item"

#: includes/admin/post-types/admin-field-group.php:88
msgid "Move field group to trash?"
msgstr "Move field group to trash?"

#: acf.php:561 includes/admin/admin-internal-post-type-list.php:264
#: includes/admin/post-types/admin-field-group.php:303
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Inactive"
msgstr "Inactive"

#. Author of the plugin
#: acf.php includes/admin/views/global/navigation.php:240
msgid "WP Engine"
msgstr "WP Engine"

#: acf.php:619
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields PRO."
msgstr ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields PRO."

#: acf.php:617
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields."
msgstr ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields."

#: includes/fields/class-acf-field-user.php:578
msgid "%1$s must have a user with the %2$s role."
msgid_plural "%1$s must have a user with one of the following roles: %2$s"
msgstr[0] "%1$s must have a user with the %2$s role."
msgstr[1] "%1$s must have a user with one of the following roles: %2$s"

#: includes/fields/class-acf-field-user.php:569
msgid "%1$s must have a valid user ID."
msgstr "%1$s must have a valid user ID."

#: includes/fields/class-acf-field-user.php:408
msgid "Invalid request."
msgstr "Invalid request."

#: includes/fields/class-acf-field-select.php:689
msgid "%1$s is not one of %2$s"
msgstr "%1$s is not one of %2$s"

#: includes/fields/class-acf-field-post_object.php:660
msgid "%1$s must have term %2$s."
msgid_plural "%1$s must have one of the following terms: %2$s"
msgstr[0] "%1$s must have term %2$s."
msgstr[1] "%1$s must have one of the following terms: %2$s"

#: includes/fields/class-acf-field-post_object.php:644
msgid "%1$s must be of post type %2$s."
msgid_plural "%1$s must be of one of the following post types: %2$s"
msgstr[0] "%1$s must be of post type %2$s."
msgstr[1] "%1$s must be of one of the following post types: %2$s"

#: includes/fields/class-acf-field-post_object.php:635
msgid "%1$s must have a valid post ID."
msgstr "%1$s must have a valid post ID."

#: includes/fields/class-acf-field-file.php:447
msgid "%s requires a valid attachment ID."
msgstr "%s requires a valid attachment ID."

#: includes/admin/views/acf-field-group/options.php:233
msgid "Show in REST API"
msgstr "Show in REST API"

#: includes/fields/class-acf-field-color_picker.php:156
msgid "Enable Transparency"
msgstr "Enable Transparency"

#: includes/fields/class-acf-field-color_picker.php:175
msgid "RGBA Array"
msgstr "RGBA Array"

#: includes/fields/class-acf-field-color_picker.php:92
msgid "RGBA String"
msgstr "RGBA String"

#: includes/fields/class-acf-field-color_picker.php:91
#: includes/fields/class-acf-field-color_picker.php:174
msgid "Hex String"
msgstr "Hex String"

#: includes/admin/views/browse-fields-modal.php:12
msgid "Upgrade to PRO"
msgstr "Upgrade to PRO"

#: includes/admin/post-types/admin-field-group.php:303
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Active"
msgstr "Active"

#: includes/fields/class-acf-field-email.php:166
msgid "'%s' is not a valid email address"
msgstr "'%s' is not a valid email address"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Color value"
msgstr "Colour value"

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Select default color"
msgstr "Select default colour"

#: includes/fields/class-acf-field-color_picker.php:66
msgid "Clear color"
msgstr "Clear colour"

#: includes/acf-wp-functions.php:90
msgid "Blocks"
msgstr "Blocks"

#: includes/acf-wp-functions.php:86
msgid "Options"
msgstr "Options"

#: includes/acf-wp-functions.php:82
msgid "Users"
msgstr "Users"

#: includes/acf-wp-functions.php:78
msgid "Menu items"
msgstr "Menu items"

#: includes/acf-wp-functions.php:70
msgid "Widgets"
msgstr "Widgets"

#: includes/acf-wp-functions.php:62
msgid "Attachments"
msgstr "Attachments"

#: includes/acf-wp-functions.php:57
#: includes/admin/post-types/admin-post-types.php:108
#: includes/admin/post-types/admin-taxonomies.php:83
#: includes/admin/tools/class-acf-admin-tool-import.php:90
#: includes/admin/views/acf-post-type/basic-settings.php:86
#: includes/post-types/class-acf-taxonomy.php:90
#: includes/post-types/class-acf-taxonomy.php:91
msgid "Taxonomies"
msgstr "Taxonomies"

#: includes/acf-wp-functions.php:44
#: includes/admin/post-types/admin-post-type.php:124
#: includes/admin/post-types/admin-post-types.php:110
#: includes/admin/views/acf-post-type/advanced-settings.php:106
msgid "Posts"
msgstr "Posts"

#: includes/ajax/class-acf-ajax-local-json-diff.php:81
msgid "Last updated: %s"
msgstr "Last updated: %s"

#: includes/ajax/class-acf-ajax-local-json-diff.php:75
msgid "Sorry, this post is unavailable for diff comparison."
msgstr "Sorry, this post is unavailable for diff comparison."

#: includes/ajax/class-acf-ajax-local-json-diff.php:47
msgid "Invalid field group parameter(s)."
msgstr "Invalid field group parameter(s)."

#: includes/admin/admin-internal-post-type-list.php:429
msgid "Awaiting save"
msgstr "Awaiting save"

#: includes/admin/admin-internal-post-type-list.php:426
msgid "Saved"
msgstr "Saved"

#: includes/admin/admin-internal-post-type-list.php:422
#: includes/admin/tools/class-acf-admin-tool-import.php:46
msgid "Import"
msgstr "Import"

#: includes/admin/admin-internal-post-type-list.php:418
msgid "Review changes"
msgstr "Review changes"

#: includes/admin/admin-internal-post-type-list.php:394
msgid "Located in: %s"
msgstr "Located in: %s"

#: includes/admin/admin-internal-post-type-list.php:391
msgid "Located in plugin: %s"
msgstr "Located in plugin: %s"

#: includes/admin/admin-internal-post-type-list.php:388
msgid "Located in theme: %s"
msgstr "Located in theme: %s"

#: includes/admin/post-types/admin-field-groups.php:231
msgid "Various"
msgstr "Various"

#: includes/admin/admin-internal-post-type-list.php:230
#: includes/admin/admin-internal-post-type-list.php:503
msgid "Sync changes"
msgstr "Sync changes"

#: includes/admin/admin-internal-post-type-list.php:229
msgid "Loading diff"
msgstr "Loading diff"

#: includes/admin/admin-internal-post-type-list.php:228
msgid "Review local JSON changes"
msgstr "Review local JSON changes"

#: includes/admin/admin.php:170
msgid "Visit website"
msgstr "Visit website"

#: includes/admin/admin.php:169
msgid "View details"
msgstr "View details"

#: includes/admin/admin.php:168
msgid "Version %s"
msgstr "Version %s"

#: includes/admin/admin.php:167
msgid "Information"
msgstr "Information"

#: includes/admin/admin.php:158
msgid ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals on "
"our Help Desk will assist with your more in depth, technical challenges."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals on "
"our Help Desk will assist with your more in depth, technical challenges."

#: includes/admin/admin.php:154
msgid ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
"friendly community on our Community Forums who may be able to help you "
"figure out the 'how-tos' of the ACF world."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
"friendly community on our Community Forums who may be able to help you "
"figure out the 'how-tos' of the ACF world."

#: includes/admin/admin.php:150
msgid ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
"documentation contains references and guides for most situations you may "
"encounter."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
"documentation contains references and guides for most situations you may "
"encounter."

#: includes/admin/admin.php:147
msgid ""
"We are fanatical about support, and want you to get the best out of your "
"website with ACF. If you run into any difficulties, there are several places "
"you can find help:"
msgstr ""
"We are fanatical about support, and want you to get the best out of your "
"website with ACF. If you run into any difficulties, there are several places "
"you can find help:"

#: includes/admin/admin.php:144 includes/admin/admin.php:146
msgid "Help & Support"
msgstr "Help & Support"

#: includes/admin/admin.php:135
msgid ""
"Please use the Help & Support tab to get in touch should you find yourself "
"requiring assistance."
msgstr ""
"Please use the Help & Support tab to get in touch should you find yourself "
"requiring assistance."

#: includes/admin/admin.php:132
msgid ""
"Before creating your first Field Group, we recommend first reading our <a "
"href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarize "
"yourself with the plugin's philosophy and best practises."
msgstr ""
"Before creating your first Field Group, we recommend first reading our <a "
"href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarise "
"yourself with the plugin's philosophy and best practises."

#: includes/admin/admin.php:130
msgid ""
"The Advanced Custom Fields plugin provides a visual form builder to "
"customize WordPress edit screens with extra fields, and an intuitive API to "
"display custom field values in any theme template file."
msgstr ""
"The Advanced Custom Fields plugin provides a visual form builder to "
"customise WordPress edit screens with extra fields, and an intuitive API to "
"display custom field values in any theme template file."

#: includes/admin/admin.php:127 includes/admin/admin.php:129
msgid "Overview"
msgstr "Overview"

#. translators: %s the name of the location type
#: includes/locations.php:38
msgid "Location type \"%s\" is already registered."
msgstr "Location type \"%s\" is already registered."

#. translators: %s class name for a location that could not be found
#: includes/locations.php:26
msgid "Class \"%s\" does not exist."
msgstr "Class \"%s\" does not exist."

#: includes/ajax/class-acf-ajax-query-users.php:43
#: includes/ajax/class-acf-ajax.php:157
msgid "Invalid nonce."
msgstr "Invalid nonce."

#: includes/fields/class-acf-field-user.php:400
msgid "Error loading field."
msgstr "Error loading field."

#: includes/forms/form-user.php:328
msgid "<strong>Error</strong>: %s"
msgstr "<strong>Error</strong>: %s"

#: includes/locations/class-acf-location-widget.php:22
msgid "Widget"
msgstr "Widget"

#: includes/locations/class-acf-location-user-role.php:24
msgid "User Role"
msgstr "User Role"

#: includes/locations/class-acf-location-comment.php:22
msgid "Comment"
msgstr "Comment"

#: includes/locations/class-acf-location-post-format.php:22
msgid "Post Format"
msgstr "Post Format"

#: includes/locations/class-acf-location-nav-menu-item.php:22
msgid "Menu Item"
msgstr "Menu Item"

#: includes/locations/class-acf-location-post-status.php:22
msgid "Post Status"
msgstr "Post Status"

#: includes/acf-wp-functions.php:74
#: includes/locations/class-acf-location-nav-menu.php:89
msgid "Menus"
msgstr "Menus"

#: includes/locations/class-acf-location-nav-menu.php:80
msgid "Menu Locations"
msgstr "Menu Locations"

#: includes/locations/class-acf-location-nav-menu.php:22
msgid "Menu"
msgstr "Menu"

#: includes/locations/class-acf-location-post-taxonomy.php:22
msgid "Post Taxonomy"
msgstr "Post Taxonomy"

#: includes/locations/class-acf-location-page-type.php:114
msgid "Child Page (has parent)"
msgstr "Child Page (has parent)"

#: includes/locations/class-acf-location-page-type.php:113
msgid "Parent Page (has children)"
msgstr "Parent Page (has children)"

#: includes/locations/class-acf-location-page-type.php:112
msgid "Top Level Page (no parent)"
msgstr "Top Level Page (no parent)"

#: includes/locations/class-acf-location-page-type.php:111
msgid "Posts Page"
msgstr "Posts Page"

#: includes/locations/class-acf-location-page-type.php:110
msgid "Front Page"
msgstr "Front Page"

#: includes/locations/class-acf-location-page-type.php:22
msgid "Page Type"
msgstr "Page Type"

#: includes/locations/class-acf-location-current-user.php:73
msgid "Viewing back end"
msgstr "Viewing back end"

#: includes/locations/class-acf-location-current-user.php:72
msgid "Viewing front end"
msgstr "Viewing front end"

#: includes/locations/class-acf-location-current-user.php:71
msgid "Logged in"
msgstr "Logged in"

#: includes/locations/class-acf-location-current-user.php:22
msgid "Current User"
msgstr "Current User"

#: includes/locations/class-acf-location-page-template.php:22
msgid "Page Template"
msgstr "Page Template"

#: includes/locations/class-acf-location-user-form.php:74
msgid "Register"
msgstr "Register"

#: includes/locations/class-acf-location-user-form.php:73
msgid "Add / Edit"
msgstr "Add / Edit"

#: includes/locations/class-acf-location-user-form.php:22
msgid "User Form"
msgstr "User Form"

#: includes/locations/class-acf-location-page-parent.php:22
msgid "Page Parent"
msgstr "Page Parent"

#: includes/locations/class-acf-location-current-user-role.php:77
msgid "Super Admin"
msgstr "Super Admin"

#: includes/locations/class-acf-location-current-user-role.php:22
msgid "Current User Role"
msgstr "Current User Role"

#: includes/locations/class-acf-location-page-template.php:73
#: includes/locations/class-acf-location-post-template.php:85
msgid "Default Template"
msgstr "Default Template"

#: includes/locations/class-acf-location-post-template.php:22
msgid "Post Template"
msgstr "Post Template"

#: includes/locations/class-acf-location-post-category.php:22
msgid "Post Category"
msgstr "Post Category"

#: includes/locations/class-acf-location-attachment.php:84
msgid "All %s formats"
msgstr "All %s formats"

#: includes/locations/class-acf-location-attachment.php:22
msgid "Attachment"
msgstr "Attachment"

#: includes/validation.php:324
msgid "%s value is required"
msgstr "%s value is required"

#: includes/admin/views/acf-field-group/conditional-logic.php:64
msgid "Show this field if"
msgstr "Show this field if"

#: includes/admin/views/acf-field-group/conditional-logic.php:25
#: includes/admin/views/acf-field-group/field.php:122 includes/fields.php:386
msgid "Conditional Logic"
msgstr "Conditional Logic"

#: includes/admin/views/acf-field-group/conditional-logic.php:169
#: includes/admin/views/acf-field-group/location-rule.php:84
msgid "and"
msgstr "and"

#: includes/admin/post-types/admin-field-groups.php:93
#: includes/admin/post-types/admin-post-types.php:114
#: includes/admin/post-types/admin-taxonomies.php:113
msgid "Local JSON"
msgstr "Local JSON"

#: includes/admin/views/acf-field-group/pro-features.php:50
msgid "Clone Field"
msgstr "Clone Field"

#. translators: %s a list of plugin
#: includes/admin/views/upgrade/notice.php:32
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr ""
"Please also check all premium add-ons (%s) are updated to the latest version."

#: includes/admin/views/upgrade/notice.php:29
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr ""
"This version contains improvements to your database and requires an upgrade."

#. translators: %1 plugin name, %2 version number
#: includes/admin/views/upgrade/notice.php:28
msgid "Thank you for updating to %1$s v%2$s!"
msgstr "Thank you for updating to %1$s v%2$s!"

#: includes/admin/views/upgrade/notice.php:26
msgid "Database Upgrade Required"
msgstr "Database Upgrade Required"

#: includes/admin/post-types/admin-field-group.php:158
#: includes/admin/views/upgrade/notice.php:17
msgid "Options Page"
msgstr "Options Page"

#: includes/admin/views/upgrade/notice.php:14 includes/fields.php:437
msgid "Gallery"
msgstr "Gallery"

#: includes/admin/views/upgrade/notice.php:11 includes/fields.php:427
msgid "Flexible Content"
msgstr "Flexible Content"

#: includes/admin/views/upgrade/notice.php:8 includes/fields.php:447
msgid "Repeater"
msgstr "Repeater"

#: includes/admin/views/tools/tools.php:16
msgid "Back to all tools"
msgstr "Back to all tools"

#: includes/admin/views/acf-field-group/options.php:195
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"

#: includes/admin/views/acf-field-group/options.php:195
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr "<b>Select</b> items to <b>hide</b> them from the edit screen."

#: includes/admin/views/acf-field-group/options.php:194
msgid "Hide on screen"
msgstr "Hide on screen"

#: includes/admin/views/acf-field-group/options.php:186
msgid "Send Trackbacks"
msgstr "Send Trackbacks"

#: includes/admin/post-types/admin-taxonomy.php:126
#: includes/admin/views/acf-field-group/options.php:185
#: includes/admin/views/acf-taxonomy/advanced-settings.php:159
msgid "Tags"
msgstr "Tags"

#: includes/admin/post-types/admin-taxonomy.php:128
#: includes/admin/views/acf-field-group/options.php:184
msgid "Categories"
msgstr "Categories"

#: includes/admin/views/acf-field-group/options.php:182
#: includes/admin/views/acf-post-type/advanced-settings.php:28
msgid "Page Attributes"
msgstr "Page Attributes"

#: includes/admin/views/acf-field-group/options.php:181
msgid "Format"
msgstr "Format"

#: includes/admin/views/acf-field-group/options.php:180
#: includes/admin/views/acf-post-type/advanced-settings.php:22
msgid "Author"
msgstr "Author"

#: includes/admin/views/acf-field-group/options.php:179
msgid "Slug"
msgstr "Slug"

#: includes/admin/views/acf-field-group/options.php:178
#: includes/admin/views/acf-post-type/advanced-settings.php:27
msgid "Revisions"
msgstr "Revisions"

#: includes/acf-wp-functions.php:66
#: includes/admin/views/acf-field-group/options.php:177
#: includes/admin/views/acf-post-type/advanced-settings.php:23
msgid "Comments"
msgstr "Comments"

#: includes/admin/views/acf-field-group/options.php:176
msgid "Discussion"
msgstr "Discussion"

#: includes/admin/views/acf-field-group/options.php:174
#: includes/admin/views/acf-post-type/advanced-settings.php:26
msgid "Excerpt"
msgstr "Excerpt"

#: includes/admin/views/acf-field-group/options.php:173
msgid "Content Editor"
msgstr "Content Editor"

#: includes/admin/views/acf-field-group/options.php:172
msgid "Permalink"
msgstr "Permalink"

#: includes/admin/views/acf-field-group/options.php:250
msgid "Shown in field group list"
msgstr "Shown in field group list"

#: includes/admin/views/acf-field-group/options.php:157
msgid "Field groups with a lower order will appear first"
msgstr "Field groups with a lower order will appear first"

#: includes/admin/views/acf-field-group/options.php:156
msgid "Order No."
msgstr "Order No."

#: includes/admin/views/acf-field-group/options.php:147
msgid "Below fields"
msgstr "Below fields"

#: includes/admin/views/acf-field-group/options.php:146
msgid "Below labels"
msgstr "Below labels"

#: includes/admin/views/acf-field-group/options.php:139
msgid "Instruction Placement"
msgstr "Instruction Placement"

#: includes/admin/views/acf-field-group/options.php:122
msgid "Label Placement"
msgstr "Label Placement"

#: includes/admin/views/acf-field-group/options.php:110
msgid "Side"
msgstr "Side"

#: includes/admin/views/acf-field-group/options.php:109
msgid "Normal (after content)"
msgstr "Normal (after content)"

#: includes/admin/views/acf-field-group/options.php:108
msgid "High (after title)"
msgstr "High (after title)"

#: includes/admin/views/acf-field-group/options.php:101
msgid "Position"
msgstr "Position"

#: includes/admin/views/acf-field-group/options.php:92
msgid "Seamless (no metabox)"
msgstr "Seamless (no metabox)"

#: includes/admin/views/acf-field-group/options.php:91
msgid "Standard (WP metabox)"
msgstr "Standard (WP metabox)"

#: includes/admin/views/acf-field-group/options.php:84
msgid "Style"
msgstr "Style"

#: includes/admin/views/acf-field-group/fields.php:55
msgid "Type"
msgstr "Type"

#: includes/admin/post-types/admin-field-groups.php:87
#: includes/admin/post-types/admin-post-types.php:107
#: includes/admin/post-types/admin-taxonomies.php:106
#: includes/admin/views/acf-field-group/fields.php:54
msgid "Key"
msgstr "Key"

#. translators: Hidden accessibility text for the positional order number of
#. the field.
#: includes/admin/views/acf-field-group/fields.php:48
msgid "Order"
msgstr "Order"

#: includes/admin/views/acf-field-group/field.php:321
msgid "Close Field"
msgstr "Close Field"

#: includes/admin/views/acf-field-group/field.php:252
msgid "id"
msgstr "id"

#: includes/admin/views/acf-field-group/field.php:236
msgid "class"
msgstr "class"

#: includes/admin/views/acf-field-group/field.php:278
msgid "width"
msgstr "width"

#: includes/admin/views/acf-field-group/field.php:272
msgid "Wrapper Attributes"
msgstr "Wrapper Attributes"

#: includes/fields/class-acf-field.php:317
msgid "Required"
msgstr "Required"

#: includes/admin/views/acf-field-group/field.php:219
msgid "Instructions"
msgstr "Instructions"

#: includes/admin/views/acf-field-group/field.php:142
msgid "Field Type"
msgstr "Field Type"

#: includes/admin/views/acf-field-group/field.php:183
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "Single word, no spaces. Underscores and dashes allowed"

#: includes/admin/views/acf-field-group/field.php:182
msgid "Field Name"
msgstr "Field Name"

#: includes/admin/views/acf-field-group/field.php:170
msgid "This is the name which will appear on the EDIT page"
msgstr "This is the name which will appear on the EDIT page"

#: includes/admin/views/acf-field-group/field.php:169
#: includes/admin/views/browse-fields-modal.php:69
msgid "Field Label"
msgstr "Field Label"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete"
msgstr "Delete"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete field"
msgstr "Delete field"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move"
msgstr "Move"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move field to another group"
msgstr "Move field to another group"

#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate field"
msgstr "Duplicate field"

#: includes/admin/views/acf-field-group/field.php:86
#: includes/admin/views/acf-field-group/field.php:89
msgid "Edit field"
msgstr "Edit field"

#: includes/admin/views/acf-field-group/field.php:82
msgid "Drag to reorder"
msgstr "Drag to reorder"

#: includes/admin/post-types/admin-field-group.php:98
#: includes/admin/views/acf-field-group/location-group.php:3
msgid "Show this field group if"
msgstr "Show this field group if"

#: includes/admin/views/upgrade/upgrade.php:93
#: includes/ajax/class-acf-ajax-upgrade.php:34
msgid "No updates available."
msgstr "No updates available."

#. translators: %s the url to the field group page.
#: includes/admin/views/upgrade/upgrade.php:32
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr "Database upgrade complete. <a href=\"%s\">See what's new</a>"

#: includes/admin/views/upgrade/upgrade.php:27
msgid "Reading upgrade tasks..."
msgstr "Reading upgrade tasks..."

#: includes/admin/views/upgrade/network.php:165
#: includes/admin/views/upgrade/upgrade.php:64
msgid "Upgrade failed."
msgstr "Upgrade failed."

#: includes/admin/views/upgrade/network.php:162
msgid "Upgrade complete."
msgstr "Upgrade complete."

#. translators: %s the version being upgraded to.
#. translators: %s the new ACF version
#: includes/admin/views/upgrade/network.php:148
#: includes/admin/views/upgrade/upgrade.php:29
msgid "Upgrading data to version %s"
msgstr "Upgrading data to version %s"

#: includes/admin/views/upgrade/network.php:120
#: includes/admin/views/upgrade/notice.php:46
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"

#: includes/admin/views/upgrade/network.php:116
msgid "Please select at least one site to upgrade."
msgstr "Please select at least one site to upgrade."

#. translators: %s admin dashboard url page
#: includes/admin/views/upgrade/network.php:96
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"

#: includes/admin/views/upgrade/network.php:79
msgid "Site is up to date"
msgstr "Site is up to date"

#. translators: %1 current db version, %2 available db version
#: includes/admin/views/upgrade/network.php:77
msgid "Site requires database upgrade from %1$s to %2$s"
msgstr "Site requires database upgrade from %1$s to %2$s"

#: includes/admin/views/upgrade/network.php:34
#: includes/admin/views/upgrade/network.php:45
msgid "Site"
msgstr "Site"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
#: includes/admin/views/upgrade/network.php:25
#: includes/admin/views/upgrade/network.php:94
msgid "Upgrade Sites"
msgstr "Upgrade Sites"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."

#: includes/admin/views/acf-field-group/conditional-logic.php:184
#: includes/admin/views/acf-field-group/locations.php:37
msgid "Add rule group"
msgstr "Add rule group"

#: includes/admin/views/acf-field-group/locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"

#: includes/admin/views/acf-field-group/locations.php:9
msgid "Rules"
msgstr "Rules"

#: includes/admin/tools/class-acf-admin-tool-export.php:449
msgid "Copied"
msgstr "Copied"

#: includes/admin/tools/class-acf-admin-tool-export.php:425
msgid "Copy to clipboard"
msgstr "Copy to clipboard"

#: includes/admin/tools/class-acf-admin-tool-export.php:331
msgid ""
"Select the items you would like to export and then select your export "
"method. Export As JSON to export to a .json file which you can then import "
"to another ACF installation. Generate PHP to export to PHP code which you "
"can place in your theme."
msgstr ""
"Select the items you would like to export and then select your export "
"method. Export As JSON to export to a .json file which you can then import "
"to another ACF installation. Generate PHP to export to PHP code which you "
"can place in your theme."

#: includes/admin/tools/class-acf-admin-tool-export.php:215
msgid "Select Field Groups"
msgstr "Select Field Groups"

#: includes/admin/tools/class-acf-admin-tool-export.php:88
#: includes/admin/tools/class-acf-admin-tool-export.php:121
msgid "No field groups selected"
msgstr "No field groups selected"

#: includes/admin/tools/class-acf-admin-tool-export.php:38
#: includes/admin/tools/class-acf-admin-tool-export.php:339
#: includes/admin/tools/class-acf-admin-tool-export.php:363
msgid "Generate PHP"
msgstr "Generate PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:34
msgid "Export Field Groups"
msgstr "Export Field Groups"

#: includes/admin/tools/class-acf-admin-tool-import.php:172
msgid "Import file empty"
msgstr "Import file empty"

#: includes/admin/tools/class-acf-admin-tool-import.php:163
msgid "Incorrect file type"
msgstr "Incorrect file type"

#: includes/admin/tools/class-acf-admin-tool-import.php:158
msgid "Error uploading file. Please try again"
msgstr "Error uploading file. Please try again"

#: includes/admin/tools/class-acf-admin-tool-import.php:47
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the items in that file."
msgstr ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the items in that file."

#: includes/admin/tools/class-acf-admin-tool-import.php:26
msgid "Import Field Groups"
msgstr "Import Field Groups"

#: includes/admin/admin-internal-post-type-list.php:417
msgid "Sync"
msgstr "Sync"

#. translators: %s: field group title
#: includes/admin/admin-internal-post-type-list.php:960
msgid "Select %s"
msgstr "Select %s"

#: includes/admin/admin-internal-post-type-list.php:460
#: includes/admin/admin-internal-post-type-list.php:492
#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate"
msgstr "Duplicate"

#: includes/admin/admin-internal-post-type-list.php:460
msgid "Duplicate this item"
msgstr "Duplicate this item"

#: includes/admin/views/acf-post-type/advanced-settings.php:41
msgid "Supports"
msgstr "Supports"

#: includes/admin/admin.php:379
#: includes/admin/views/browse-fields-modal.php:102
msgid "Documentation"
msgstr "Documentation"

#: includes/admin/post-types/admin-field-groups.php:86
#: includes/admin/post-types/admin-post-types.php:106
#: includes/admin/post-types/admin-taxonomies.php:105
#: includes/admin/views/acf-field-group/options.php:249
#: includes/admin/views/acf-post-type/advanced-settings.php:62
#: includes/admin/views/acf-taxonomy/advanced-settings.php:114
#: includes/admin/views/upgrade/network.php:36
#: includes/admin/views/upgrade/network.php:47
msgid "Description"
msgstr "Description"

#: includes/admin/admin-internal-post-type-list.php:414
#: includes/admin/admin-internal-post-type-list.php:832
msgid "Sync available"
msgstr "Sync available"

#. translators: %s number of field groups synchronized
#: includes/admin/post-types/admin-field-groups.php:370
msgid "Field group synchronized."
msgid_plural "%s field groups synchronized."
msgstr[0] "Field group synchronised."
msgstr[1] "%s field groups synchronised."

#. translators: %s number of field groups duplicated
#: includes/admin/post-types/admin-field-groups.php:363
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "Field group duplicated."
msgstr[1] "%s field groups duplicated."

#: includes/admin/admin-internal-post-type-list.php:155
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Active <span class=\"count\">(%s)</span>"
msgstr[1] "Active <span class=\"count\">(%s)</span>"

#: includes/admin/admin-upgrade.php:251
msgid "Review sites & upgrade"
msgstr "Review sites & upgrade"

#: includes/admin/admin-upgrade.php:59 includes/admin/admin-upgrade.php:90
#: includes/admin/admin-upgrade.php:91 includes/admin/admin-upgrade.php:227
#: includes/admin/views/upgrade/network.php:21
#: includes/admin/views/upgrade/upgrade.php:23
msgid "Upgrade Database"
msgstr "Upgrade Database"

#: includes/admin/views/acf-field-group/options.php:175
#: includes/admin/views/acf-post-type/advanced-settings.php:30
msgid "Custom Fields"
msgstr "Custom Fields"

#: includes/admin/post-types/admin-field-group.php:608
msgid "Move Field"
msgstr "Move Field"

#: includes/admin/post-types/admin-field-group.php:601
#: includes/admin/post-types/admin-field-group.php:605
msgid "Please select the destination for this field"
msgstr "Please select the destination for this field"

#. translators: Confirmation message once a field has been moved to a different
#. field group.
#: includes/admin/post-types/admin-field-group.php:567
msgid "The %1$s field can now be found in the %2$s field group"
msgstr "The %1$s field can now be found in the %2$s field group"

#: includes/admin/post-types/admin-field-group.php:564
msgid "Move Complete."
msgstr "Move Complete."

#: includes/admin/views/acf-field-group/field.php:52
#: includes/admin/views/acf-field-group/options.php:217
#: includes/admin/views/acf-post-type/advanced-settings.php:78
#: includes/admin/views/acf-taxonomy/advanced-settings.php:130
msgid "Active"
msgstr "Active"

#: includes/admin/post-types/admin-field-group.php:275
msgid "Field Keys"
msgstr "Field Keys"

#: includes/admin/post-types/admin-field-group.php:179
msgid "Settings"
msgstr "Settings"

#: includes/admin/post-types/admin-field-groups.php:88
msgid "Location"
msgstr "Location"

#: includes/admin/post-types/admin-field-group.php:99
msgid "Null"
msgstr "Null"

#: includes/admin/post-types/admin-field-group.php:96
#: includes/class-acf-internal-post-type.php:728
#: includes/post-types/class-acf-field-group.php:345
msgid "copy"
msgstr "copy"

#: includes/admin/post-types/admin-field-group.php:95
msgid "(this field)"
msgstr "(this field)"

#: includes/admin/post-types/admin-field-group.php:93
msgid "Checked"
msgstr "Checked"

#: includes/admin/post-types/admin-field-group.php:90
msgid "Move Custom Field"
msgstr "Move Custom Field"

#: includes/admin/post-types/admin-field-group.php:89
msgid "No toggle fields available"
msgstr "No toggle fields available"

#: includes/admin/post-types/admin-field-group.php:87
msgid "Field group title is required"
msgstr "Field group title is required"

#: includes/admin/post-types/admin-field-group.php:86
msgid "This field cannot be moved until its changes have been saved"
msgstr "This field cannot be moved until its changes have been saved"

#: includes/admin/post-types/admin-field-group.php:85
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "The string \"field_\" may not be used at the start of a field name"

#: includes/admin/post-types/admin-field-group.php:69
msgid "Field group draft updated."
msgstr "Field group draft updated."

#: includes/admin/post-types/admin-field-group.php:68
msgid "Field group scheduled for."
msgstr "Field group scheduled for."

#: includes/admin/post-types/admin-field-group.php:67
msgid "Field group submitted."
msgstr "Field group submitted."

#: includes/admin/post-types/admin-field-group.php:66
msgid "Field group saved."
msgstr "Field group saved."

#: includes/admin/post-types/admin-field-group.php:65
msgid "Field group published."
msgstr "Field group published."

#: includes/admin/post-types/admin-field-group.php:62
msgid "Field group deleted."
msgstr "Field group deleted."

#: includes/admin/post-types/admin-field-group.php:60
#: includes/admin/post-types/admin-field-group.php:61
#: includes/admin/post-types/admin-field-group.php:63
msgid "Field group updated."
msgstr "Field group updated."

#: includes/admin/admin-tools.php:112
#: includes/admin/views/global/navigation.php:254
#: includes/admin/views/tools/tools.php:14
msgid "Tools"
msgstr "Tools"

#: includes/locations/abstract-acf-location.php:105
msgid "is not equal to"
msgstr "is not equal to"

#: includes/locations/abstract-acf-location.php:104
msgid "is equal to"
msgstr "is equal to"

#: includes/locations.php:104
msgid "Forms"
msgstr "Forms"

#: includes/admin/post-types/admin-post-type.php:125 includes/locations.php:102
#: includes/locations/class-acf-location-page.php:22
msgid "Page"
msgstr "Page"

#: includes/admin/post-types/admin-post-type.php:123 includes/locations.php:101
#: includes/locations/class-acf-location-post.php:22
msgid "Post"
msgstr "Post"

#: includes/fields.php:329
msgid "Relational"
msgstr "Relational"

#: includes/fields.php:328
msgid "Choice"
msgstr "Choice"

#: includes/fields.php:326
msgid "Basic"
msgstr "Basic"

#: includes/fields.php:277
msgid "Unknown"
msgstr "Unknown"

#: includes/fields.php:277
msgid "Field type does not exist"
msgstr "Field type does not exist"

#: includes/forms/form-front.php:219
msgid "Spam Detected"
msgstr "Spam Detected"

#: includes/forms/form-front.php:102
msgid "Post updated"
msgstr "Post updated"

#: includes/forms/form-front.php:101
msgid "Update"
msgstr "Update"

#: includes/forms/form-front.php:62
msgid "Validate Email"
msgstr "Validate Email"

#: includes/fields.php:327 includes/forms/form-front.php:54
msgid "Content"
msgstr "Content"

#: includes/admin/views/acf-post-type/advanced-settings.php:21
#: includes/forms/form-front.php:45
msgid "Title"
msgstr "Title"

#: includes/assets.php:376 includes/forms/form-comment.php:140
msgid "Edit field group"
msgstr "Edit field group"

#: includes/admin/post-types/admin-field-group.php:112
msgid "Selection is less than"
msgstr "Selection is less than"

#: includes/admin/post-types/admin-field-group.php:111
msgid "Selection is greater than"
msgstr "Selection is greater than"

#: includes/admin/post-types/admin-field-group.php:110
msgid "Value is less than"
msgstr "Value is less than"

#: includes/admin/post-types/admin-field-group.php:109
msgid "Value is greater than"
msgstr "Value is greater than"

#: includes/admin/post-types/admin-field-group.php:108
msgid "Value contains"
msgstr "Value contains"

#: includes/admin/post-types/admin-field-group.php:107
msgid "Value matches pattern"
msgstr "Value matches pattern"

#: includes/admin/post-types/admin-field-group.php:106
msgid "Value is not equal to"
msgstr "Value is not equal to"

#: includes/admin/post-types/admin-field-group.php:105
msgid "Value is equal to"
msgstr "Value is equal to"

#: includes/admin/post-types/admin-field-group.php:104
msgid "Has no value"
msgstr "Has no value"

#: includes/admin/post-types/admin-field-group.php:103
msgid "Has any value"
msgstr "Has any value"

#: includes/admin/admin-internal-post-type.php:337
#: includes/admin/views/browse-fields-modal.php:72 includes/assets.php:350
msgid "Cancel"
msgstr "Cancel"

#: includes/assets.php:346
msgid "Are you sure?"
msgstr "Are you sure?"

#. translators: %d is the number of fields that require attention
#: includes/assets.php:370
msgid "%d fields require attention"
msgstr "%d fields require attention"

#: includes/assets.php:368
msgid "1 field requires attention"
msgstr "1 field requires attention"

#: includes/assets.php:367 includes/validation.php:258
#: includes/validation.php:266
msgid "Validation failed"
msgstr "Validation failed"

#: includes/assets.php:366
msgid "Validation successful"
msgstr "Validation successful"

#: includes/media.php:54
msgid "Restricted"
msgstr "Restricted"

#: includes/media.php:53
msgid "Collapse Details"
msgstr "Collapse Details"

#: includes/media.php:52
msgid "Expand Details"
msgstr "Expand Details"

#: includes/admin/views/acf-post-type/advanced-settings.php:470
#: includes/media.php:51
msgid "Uploaded to this post"
msgstr "Uploaded to this post"

#: includes/media.php:50
msgctxt "verb"
msgid "Update"
msgstr "Update"

#: includes/media.php:49
msgctxt "verb"
msgid "Edit"
msgstr "Edit"

#: includes/assets.php:360
msgid "The changes you made will be lost if you navigate away from this page"
msgstr "The changes you made will be lost if you navigate away from this page"

#: includes/api/api-helpers.php:3000
msgid "File type must be %s."
msgstr "File type must be %s."

#: includes/admin/post-types/admin-field-group.php:97
#: includes/admin/views/acf-field-group/conditional-logic.php:64
#: includes/admin/views/acf-field-group/conditional-logic.php:182
#: includes/admin/views/acf-field-group/location-group.php:3
#: includes/admin/views/acf-field-group/locations.php:35
#: includes/api/api-helpers.php:2997
msgid "or"
msgstr "or"

#: includes/api/api-helpers.php:2973
msgid "File size must not exceed %s."
msgstr "File size must not exceed %s."

#: includes/api/api-helpers.php:2969
msgid "File size must be at least %s."
msgstr "File size must be at least %s."

#: includes/api/api-helpers.php:2956
msgid "Image height must not exceed %dpx."
msgstr "Image height must not exceed %dpx."

#: includes/api/api-helpers.php:2952
msgid "Image height must be at least %dpx."
msgstr "Image height must be at least %dpx."

#: includes/api/api-helpers.php:2940
msgid "Image width must not exceed %dpx."
msgstr "Image width must not exceed %dpx."

#: includes/api/api-helpers.php:2936
msgid "Image width must be at least %dpx."
msgstr "Image width must be at least %dpx."

#: includes/api/api-helpers.php:1425 includes/api/api-term.php:140
msgid "(no title)"
msgstr "(no title)"

#: includes/api/api-helpers.php:781
msgid "Full Size"
msgstr "Full Size"

#: includes/api/api-helpers.php:746
msgid "Large"
msgstr "Large"

#: includes/api/api-helpers.php:745
msgid "Medium"
msgstr "Medium"

#: includes/api/api-helpers.php:744
msgid "Thumbnail"
msgstr "Thumbnail"

#: includes/acf-field-functions.php:854
#: includes/admin/post-types/admin-field-group.php:94
msgid "(no label)"
msgstr "(no label)"

#: includes/fields/class-acf-field-textarea.php:135
msgid "Sets the textarea height"
msgstr "Sets the textarea height"

#: includes/fields/class-acf-field-textarea.php:134
msgid "Rows"
msgstr "Rows"

#: includes/fields/class-acf-field-textarea.php:22
msgid "Text Area"
msgstr "Text Area"

#: includes/fields/class-acf-field-checkbox.php:434
msgid "Prepend an extra checkbox to toggle all choices"
msgstr "Prepend an extra checkbox to toggle all choices"

#: includes/fields/class-acf-field-checkbox.php:396
msgid "Save 'custom' values to the field's choices"
msgstr "Save 'custom' values to the field's choices"

#: includes/fields/class-acf-field-checkbox.php:385
msgid "Allow 'custom' values to be added"
msgstr "Allow 'custom' values to be added"

#: includes/fields/class-acf-field-checkbox.php:48
msgid "Add new choice"
msgstr "Add new choice"

#: includes/fields/class-acf-field-checkbox.php:170
msgid "Toggle All"
msgstr "Toggle All"

#: includes/fields/class-acf-field-page_link.php:487
msgid "Allow Archives URLs"
msgstr "Allow Archive URLs"

#: includes/fields/class-acf-field-page_link.php:196
msgid "Archives"
msgstr "Archives"

#: includes/fields/class-acf-field-page_link.php:22
msgid "Page Link"
msgstr "Page Link"

#: includes/fields/class-acf-field-taxonomy.php:884
#: includes/locations/class-acf-location-user-form.php:72
msgid "Add"
msgstr "Add"

#: includes/admin/views/acf-field-group/fields.php:53
#: includes/fields/class-acf-field-taxonomy.php:854
msgid "Name"
msgstr "Name"

#: includes/fields/class-acf-field-taxonomy.php:839
msgid "%s added"
msgstr "%s added"

#: includes/fields/class-acf-field-taxonomy.php:803
msgid "%s already exists"
msgstr "%s already exists"

#: includes/fields/class-acf-field-taxonomy.php:791
msgid "User unable to add new %s"
msgstr "User unable to add new %s"

#: includes/fields/class-acf-field-taxonomy.php:678
msgid "Term ID"
msgstr "Term ID"

#: includes/fields/class-acf-field-taxonomy.php:677
msgid "Term Object"
msgstr "Term Object"

#: includes/fields/class-acf-field-taxonomy.php:662
msgid "Load value from posts terms"
msgstr "Load value from posts terms"

#: includes/fields/class-acf-field-taxonomy.php:661
msgid "Load Terms"
msgstr "Load Terms"

#: includes/fields/class-acf-field-taxonomy.php:651
msgid "Connect selected terms to the post"
msgstr "Connect selected terms to the post"

#: includes/fields/class-acf-field-taxonomy.php:650
msgid "Save Terms"
msgstr "Save Terms"

#: includes/fields/class-acf-field-taxonomy.php:640
msgid "Allow new terms to be created whilst editing"
msgstr "Allow new terms to be created whilst editing"

#: includes/fields/class-acf-field-taxonomy.php:639
msgid "Create Terms"
msgstr "Create Terms"

#: includes/fields/class-acf-field-taxonomy.php:698
msgid "Radio Buttons"
msgstr "Radio Buttons"

#: includes/fields/class-acf-field-taxonomy.php:697
msgid "Single Value"
msgstr "Single Value"

#: includes/fields/class-acf-field-taxonomy.php:695
msgid "Multi Select"
msgstr "Multi Select"

#: includes/fields/class-acf-field-checkbox.php:35
#: includes/fields/class-acf-field-taxonomy.php:694
msgid "Checkbox"
msgstr "Checkbox"

#: includes/fields/class-acf-field-taxonomy.php:693
msgid "Multiple Values"
msgstr "Multiple Values"

#: includes/fields/class-acf-field-taxonomy.php:688
msgid "Select the appearance of this field"
msgstr "Select the appearance of this field"

#: includes/fields/class-acf-field-taxonomy.php:687
msgid "Appearance"
msgstr "Appearance"

#: includes/fields/class-acf-field-taxonomy.php:629
msgid "Select the taxonomy to be displayed"
msgstr "Select the taxonomy to be displayed"

#: includes/fields/class-acf-field-taxonomy.php:593
msgctxt "No Terms"
msgid "No %s"
msgstr "No %s"

#: includes/fields/class-acf-field-number.php:240
msgid "Value must be equal to or lower than %d"
msgstr "Value must be equal to or lower than %d"

#: includes/fields/class-acf-field-number.php:235
msgid "Value must be equal to or higher than %d"
msgstr "Value must be equal to or higher than %d"

#: includes/fields/class-acf-field-number.php:223
msgid "Value must be a number"
msgstr "Value must be a number"

#: includes/fields/class-acf-field-number.php:22
msgid "Number"
msgstr "Number"

#: includes/fields/class-acf-field-radio.php:254
msgid "Save 'other' values to the field's choices"
msgstr "Save 'other' values to the field's choices"

#: includes/fields/class-acf-field-radio.php:243
msgid "Add 'other' choice to allow for custom values"
msgstr "Add 'other' choice to allow for custom values"

#: includes/admin/views/global/navigation.php:202
msgid "Other"
msgstr "Other"

#: includes/fields/class-acf-field-radio.php:22
msgid "Radio Button"
msgstr "Radio Button"

#: includes/fields/class-acf-field-accordion.php:106
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."

#: includes/fields/class-acf-field-accordion.php:95
msgid "Allow this accordion to open without closing others."
msgstr "Allow this accordion to open without closing others."

#: includes/fields/class-acf-field-accordion.php:94
msgid "Multi-Expand"
msgstr "Multi-Expand"

#: includes/fields/class-acf-field-accordion.php:84
msgid "Display this accordion as open on page load."
msgstr "Display this accordion as open on page load."

#: includes/fields/class-acf-field-accordion.php:83
msgid "Open"
msgstr "Open"

#: includes/fields/class-acf-field-accordion.php:24
msgid "Accordion"
msgstr "Accordion"

#: includes/fields/class-acf-field-file.php:253
#: includes/fields/class-acf-field-file.php:265
msgid "Restrict which files can be uploaded"
msgstr "Restrict which files can be uploaded"

#: includes/fields/class-acf-field-file.php:207
msgid "File ID"
msgstr "File ID"

#: includes/fields/class-acf-field-file.php:206
msgid "File URL"
msgstr "File URL"

#: includes/fields/class-acf-field-file.php:205
msgid "File Array"
msgstr "File Array"

#: includes/fields/class-acf-field-file.php:176
msgid "Add File"
msgstr "Add File"

#: includes/admin/tools/class-acf-admin-tool-import.php:151
#: includes/fields/class-acf-field-file.php:176
msgid "No file selected"
msgstr "No file selected"

#: includes/fields/class-acf-field-file.php:140
msgid "File name"
msgstr "File name"

#: includes/fields/class-acf-field-file.php:57
msgid "Update File"
msgstr "Update File"

#: includes/fields/class-acf-field-file.php:56
msgid "Edit File"
msgstr "Edit File"

#: includes/admin/tools/class-acf-admin-tool-import.php:55
#: includes/fields/class-acf-field-file.php:55
msgid "Select File"
msgstr "Select File"

#: includes/fields/class-acf-field-file.php:22
msgid "File"
msgstr "File"

#: includes/fields/class-acf-field-password.php:22
msgid "Password"
msgstr "Password"

#: includes/fields/class-acf-field-select.php:363
msgid "Specify the value returned"
msgstr "Specify the value returned"

#: includes/fields/class-acf-field-select.php:431
msgid "Use AJAX to lazy load choices?"
msgstr "Use AJAX to lazy load choices?"

#: includes/fields/class-acf-field-checkbox.php:346
#: includes/fields/class-acf-field-select.php:352
msgid "Enter each default value on a new line"
msgstr "Enter each default value on a new line"

#: includes/fields/class-acf-field-select.php:217 includes/media.php:48
msgctxt "verb"
msgid "Select"
msgstr "Select"

#: includes/fields/class-acf-field-select.php:95
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Loading failed"

#: includes/fields/class-acf-field-select.php:94
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Searching&hellip;"

#: includes/fields/class-acf-field-select.php:93
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Loading more results&hellip;"

#. translators: %d - maximum number of items that can be selected in the select
#. field
#: includes/fields/class-acf-field-select.php:92
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "You can only select %d items"

#: includes/fields/class-acf-field-select.php:90
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "You can only select 1 item"

#. translators: %d - number of characters that should be removed from select
#. field
#: includes/fields/class-acf-field-select.php:89
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Please delete %d characters"

#: includes/fields/class-acf-field-select.php:87
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Please delete 1 character"

#. translators: %d - number of characters to enter into select field input
#: includes/fields/class-acf-field-select.php:86
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Please enter %d or more characters"

#: includes/fields/class-acf-field-select.php:84
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Please enter 1 or more characters"

#: includes/fields/class-acf-field-select.php:83
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "No matches found"

#. translators: %d - number of results available in select field
#: includes/fields/class-acf-field-select.php:82
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr "%d results are available, use up and down arrow keys to navigate."

#: includes/fields/class-acf-field-select.php:80
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "One result is available, press enter to select it."

#: includes/fields/class-acf-field-select.php:16
#: includes/fields/class-acf-field-taxonomy.php:699
msgctxt "noun"
msgid "Select"
msgstr "Select"

#: includes/fields/class-acf-field-user.php:102
msgid "User ID"
msgstr "User ID"

#: includes/fields/class-acf-field-user.php:101
msgid "User Object"
msgstr "User Object"

#: includes/fields/class-acf-field-user.php:100
msgid "User Array"
msgstr "User Array"

#: includes/fields/class-acf-field-user.php:88
msgid "All user roles"
msgstr "All user roles"

#: includes/fields/class-acf-field-user.php:80
msgid "Filter by Role"
msgstr "Filter by Role"

#: includes/fields/class-acf-field-user.php:15 includes/locations.php:103
msgid "User"
msgstr "User"

#: includes/fields/class-acf-field-separator.php:22
msgid "Separator"
msgstr "Separator"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Select Color"
msgstr "Select Colour"

#: includes/admin/post-types/admin-post-type.php:127
#: includes/admin/post-types/admin-taxonomy.php:129
#: includes/fields/class-acf-field-color_picker.php:67
msgid "Default"
msgstr "Default"

#: includes/admin/views/acf-post-type/advanced-settings.php:89
#: includes/admin/views/acf-taxonomy/advanced-settings.php:141
#: includes/fields/class-acf-field-color_picker.php:65
msgid "Clear"
msgstr "Clear"

#: includes/fields/class-acf-field-color_picker.php:22
msgid "Color Picker"
msgstr "Colour Picker"

#: includes/fields/class-acf-field-date_time_picker.php:83
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-date_time_picker.php:82
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:79
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:76
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Select"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Done"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Now"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Time Zone"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Microsecond"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Millisecond"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Second"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Minute"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Hour"

#: includes/fields/class-acf-field-date_time_picker.php:67
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Time"

#: includes/fields/class-acf-field-date_time_picker.php:66
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Choose Time"

#: includes/fields/class-acf-field-date_time_picker.php:22
msgid "Date Time Picker"
msgstr "Date Time Picker"

#: includes/fields/class-acf-field-accordion.php:105
msgid "Endpoint"
msgstr "Endpoint"

#: includes/admin/views/acf-field-group/options.php:130
#: includes/fields/class-acf-field-tab.php:112
msgid "Left aligned"
msgstr "Left aligned"

#: includes/admin/views/acf-field-group/options.php:129
#: includes/fields/class-acf-field-tab.php:111
msgid "Top aligned"
msgstr "Top aligned"

#: includes/fields/class-acf-field-tab.php:107
msgid "Placement"
msgstr "Placement"

#: includes/fields/class-acf-field-tab.php:23
msgid "Tab"
msgstr "Tab"

#: includes/fields/class-acf-field-url.php:138
msgid "Value must be a valid URL"
msgstr "Value must be a valid URL"

#: includes/fields/class-acf-field-link.php:153
msgid "Link URL"
msgstr "Link URL"

#: includes/fields/class-acf-field-link.php:152
msgid "Link Array"
msgstr "Link Array"

#: includes/fields/class-acf-field-link.php:124
msgid "Opens in a new window/tab"
msgstr "Opens in a new window/tab"

#: includes/fields/class-acf-field-link.php:119
msgid "Select Link"
msgstr "Select Link"

#: includes/fields/class-acf-field-link.php:22
msgid "Link"
msgstr "Link"

#: includes/fields/class-acf-field-email.php:22
msgid "Email"
msgstr "Email"

#: includes/fields/class-acf-field-number.php:173
#: includes/fields/class-acf-field-range.php:206
msgid "Step Size"
msgstr "Step Size"

#: includes/fields/class-acf-field-number.php:143
#: includes/fields/class-acf-field-range.php:184
msgid "Maximum Value"
msgstr "Maximum Value"

#: includes/fields/class-acf-field-number.php:133
#: includes/fields/class-acf-field-range.php:173
msgid "Minimum Value"
msgstr "Minimum Value"

#: includes/fields/class-acf-field-range.php:22
msgid "Range"
msgstr "Range"

#: includes/fields/class-acf-field-button-group.php:165
#: includes/fields/class-acf-field-checkbox.php:363
#: includes/fields/class-acf-field-radio.php:210
#: includes/fields/class-acf-field-select.php:370
msgid "Both (Array)"
msgstr "Both (Array)"

#: includes/admin/views/acf-field-group/fields.php:52
#: includes/fields/class-acf-field-button-group.php:164
#: includes/fields/class-acf-field-checkbox.php:362
#: includes/fields/class-acf-field-radio.php:209
#: includes/fields/class-acf-field-select.php:369
msgid "Label"
msgstr "Label"

#: includes/fields/class-acf-field-button-group.php:163
#: includes/fields/class-acf-field-checkbox.php:361
#: includes/fields/class-acf-field-radio.php:208
#: includes/fields/class-acf-field-select.php:368
msgid "Value"
msgstr "Value"

#: includes/fields/class-acf-field-button-group.php:211
#: includes/fields/class-acf-field-checkbox.php:424
#: includes/fields/class-acf-field-radio.php:282
msgid "Vertical"
msgstr "Vertical"

#: includes/fields/class-acf-field-button-group.php:210
#: includes/fields/class-acf-field-checkbox.php:425
#: includes/fields/class-acf-field-radio.php:283
msgid "Horizontal"
msgstr "Horizontal"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:336
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "red : Red"
msgstr "red : Red"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:336
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "For more control, you may specify both a value and label like this:"
msgstr "For more control, you may specify both a value and label like this:"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:336
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "Enter each choice on a new line."
msgstr "Enter each choice on a new line."

#: includes/fields/class-acf-field-button-group.php:137
#: includes/fields/class-acf-field-checkbox.php:335
#: includes/fields/class-acf-field-radio.php:182
#: includes/fields/class-acf-field-select.php:340
msgid "Choices"
msgstr "Choices"

#: includes/fields/class-acf-field-button-group.php:23
msgid "Button Group"
msgstr "Button Group"

#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-page_link.php:519
#: includes/fields/class-acf-field-post_object.php:432
#: includes/fields/class-acf-field-radio.php:228
#: includes/fields/class-acf-field-select.php:399
#: includes/fields/class-acf-field-taxonomy.php:708
#: includes/fields/class-acf-field-user.php:132
msgid "Allow Null"
msgstr "Allow Null"

#: includes/fields/class-acf-field-page_link.php:273
#: includes/fields/class-acf-field-post_object.php:254
#: includes/fields/class-acf-field-taxonomy.php:872
msgid "Parent"
msgstr "Parent"

#: includes/fields/class-acf-field-wysiwyg.php:367
msgid "TinyMCE will not be initialized until field is clicked"
msgstr "TinyMCE will not be initialised until field is clicked"

#: includes/fields/class-acf-field-wysiwyg.php:366
msgid "Delay Initialization"
msgstr "Delay Initialisation"

#: includes/fields/class-acf-field-wysiwyg.php:355
msgid "Show Media Upload Buttons"
msgstr "Show Media Upload Buttons"

#: includes/fields/class-acf-field-wysiwyg.php:339
msgid "Toolbar"
msgstr "Toolbar"

#: includes/fields/class-acf-field-wysiwyg.php:331
msgid "Text Only"
msgstr "Text Only"

#: includes/fields/class-acf-field-wysiwyg.php:330
msgid "Visual Only"
msgstr "Visual Only"

#: includes/fields/class-acf-field-wysiwyg.php:329
msgid "Visual & Text"
msgstr "Visual and Text"

#: includes/fields/class-acf-field-icon_picker.php:269
#: includes/fields/class-acf-field-wysiwyg.php:324
msgid "Tabs"
msgstr "Tabs"

#: includes/fields/class-acf-field-wysiwyg.php:268
msgid "Click to initialize TinyMCE"
msgstr "Click to initialise TinyMCE"

#: includes/fields/class-acf-field-wysiwyg.php:262
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Text"

#: includes/fields/class-acf-field-wysiwyg.php:261
msgid "Visual"
msgstr "Visual"

#: includes/fields/class-acf-field-text.php:181
#: includes/fields/class-acf-field-textarea.php:217
msgid "Value must not exceed %d characters"
msgstr "Value must not exceed %d characters"

#: includes/fields/class-acf-field-text.php:116
#: includes/fields/class-acf-field-textarea.php:114
msgid "Leave blank for no limit"
msgstr "Leave blank for no limit"

#: includes/fields/class-acf-field-text.php:115
#: includes/fields/class-acf-field-textarea.php:113
msgid "Character Limit"
msgstr "Character Limit"

#: includes/fields/class-acf-field-email.php:144
#: includes/fields/class-acf-field-number.php:194
#: includes/fields/class-acf-field-password.php:95
#: includes/fields/class-acf-field-range.php:228
#: includes/fields/class-acf-field-text.php:156
msgid "Appears after the input"
msgstr "Appears after the input"

#: includes/fields/class-acf-field-email.php:143
#: includes/fields/class-acf-field-number.php:193
#: includes/fields/class-acf-field-password.php:94
#: includes/fields/class-acf-field-range.php:227
#: includes/fields/class-acf-field-text.php:155
msgid "Append"
msgstr "Append"

#: includes/fields/class-acf-field-email.php:134
#: includes/fields/class-acf-field-number.php:184
#: includes/fields/class-acf-field-password.php:85
#: includes/fields/class-acf-field-range.php:218
#: includes/fields/class-acf-field-text.php:146
msgid "Appears before the input"
msgstr "Appears before the input"

#: includes/fields/class-acf-field-email.php:133
#: includes/fields/class-acf-field-number.php:183
#: includes/fields/class-acf-field-password.php:84
#: includes/fields/class-acf-field-range.php:217
#: includes/fields/class-acf-field-text.php:145
msgid "Prepend"
msgstr "Prepend"

#: includes/fields/class-acf-field-email.php:124
#: includes/fields/class-acf-field-number.php:164
#: includes/fields/class-acf-field-password.php:75
#: includes/fields/class-acf-field-text.php:136
#: includes/fields/class-acf-field-textarea.php:146
#: includes/fields/class-acf-field-url.php:105
msgid "Appears within the input"
msgstr "Appears within the input"

#: includes/fields/class-acf-field-email.php:123
#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-password.php:74
#: includes/fields/class-acf-field-text.php:135
#: includes/fields/class-acf-field-textarea.php:145
#: includes/fields/class-acf-field-url.php:104
msgid "Placeholder Text"
msgstr "Placeholder Text"

#: includes/fields/class-acf-field-button-group.php:148
#: includes/fields/class-acf-field-email.php:104
#: includes/fields/class-acf-field-number.php:114
#: includes/fields/class-acf-field-radio.php:193
#: includes/fields/class-acf-field-range.php:154
#: includes/fields/class-acf-field-text.php:96
#: includes/fields/class-acf-field-textarea.php:94
#: includes/fields/class-acf-field-url.php:85
#: includes/fields/class-acf-field-wysiwyg.php:292
msgid "Appears when creating a new post"
msgstr "Appears when creating a new post"

#: includes/fields/class-acf-field-text.php:22
msgid "Text"
msgstr "Text"

#: includes/fields/class-acf-field-relationship.php:753
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] "%1$s requires at least %2$s selection"
msgstr[1] "%1$s requires at least %2$s selections"

#: includes/fields/class-acf-field-post_object.php:402
#: includes/fields/class-acf-field-relationship.php:616
msgid "Post ID"
msgstr "Post ID"

#: includes/fields/class-acf-field-post_object.php:15
#: includes/fields/class-acf-field-post_object.php:401
#: includes/fields/class-acf-field-relationship.php:615
msgid "Post Object"
msgstr "Post Object"

#: includes/fields/class-acf-field-relationship.php:648
msgid "Maximum Posts"
msgstr "Maximum Posts"

#: includes/fields/class-acf-field-relationship.php:638
msgid "Minimum Posts"
msgstr "Minimum Posts"

#: includes/admin/views/acf-field-group/options.php:183
#: includes/admin/views/acf-post-type/advanced-settings.php:29
#: includes/fields/class-acf-field-relationship.php:673
msgid "Featured Image"
msgstr "Featured Image"

#: includes/fields/class-acf-field-relationship.php:669
msgid "Selected elements will be displayed in each result"
msgstr "Selected elements will be displayed in each result"

#: includes/fields/class-acf-field-relationship.php:668
msgid "Elements"
msgstr "Elements"

#: includes/fields/class-acf-field-relationship.php:602
#: includes/fields/class-acf-field-taxonomy.php:20
#: includes/fields/class-acf-field-taxonomy.php:628
#: includes/locations/class-acf-location-taxonomy.php:22
msgid "Taxonomy"
msgstr "Taxonomy"

#: includes/fields/class-acf-field-relationship.php:601
#: includes/locations/class-acf-location-post-type.php:22
#: includes/post-types/class-acf-post-type.php:92
msgid "Post Type"
msgstr "Post Type"

#: includes/fields/class-acf-field-relationship.php:595
msgid "Filters"
msgstr "Filters"

#: includes/fields/class-acf-field-page_link.php:480
#: includes/fields/class-acf-field-post_object.php:389
#: includes/fields/class-acf-field-relationship.php:588
msgid "All taxonomies"
msgstr "All taxonomies"

#: includes/fields/class-acf-field-page_link.php:472
#: includes/fields/class-acf-field-post_object.php:381
#: includes/fields/class-acf-field-relationship.php:580
msgid "Filter by Taxonomy"
msgstr "Filter by Taxonomy"

#: includes/fields/class-acf-field-page_link.php:450
#: includes/fields/class-acf-field-post_object.php:359
#: includes/fields/class-acf-field-relationship.php:558
msgid "All post types"
msgstr "All post types"

#: includes/fields/class-acf-field-page_link.php:442
#: includes/fields/class-acf-field-post_object.php:351
#: includes/fields/class-acf-field-relationship.php:550
msgid "Filter by Post Type"
msgstr "Filter by Post Type"

#: includes/fields/class-acf-field-relationship.php:450
msgid "Search..."
msgstr "Search..."

#: includes/fields/class-acf-field-relationship.php:380
msgid "Select taxonomy"
msgstr "Select taxonomy"

#: includes/fields/class-acf-field-relationship.php:372
msgid "Select post type"
msgstr "Select post type"

#: includes/fields/class-acf-field-relationship.php:78
msgid "No matches found"
msgstr "No matches found"

#: includes/fields/class-acf-field-relationship.php:77
msgid "Loading"
msgstr "Loading"

#: includes/fields/class-acf-field-relationship.php:76
msgid "Maximum values reached ( {max} values )"
msgstr "Maximum values reached ( {max} values )"

#: includes/fields/class-acf-field-relationship.php:17
msgid "Relationship"
msgstr "Relationship"

#: includes/fields/class-acf-field-file.php:277
#: includes/fields/class-acf-field-image.php:307
msgid "Comma separated list. Leave blank for all types"
msgstr "Comma separated list. Leave blank for all types"

#: includes/fields/class-acf-field-file.php:276
#: includes/fields/class-acf-field-image.php:306
msgid "Allowed File Types"
msgstr "Allowed File Types"

#: includes/fields/class-acf-field-file.php:264
#: includes/fields/class-acf-field-image.php:270
msgid "Maximum"
msgstr "Maximum"

#: includes/fields/class-acf-field-file.php:144
#: includes/fields/class-acf-field-file.php:256
#: includes/fields/class-acf-field-file.php:268
#: includes/fields/class-acf-field-image.php:261
#: includes/fields/class-acf-field-image.php:297
msgid "File size"
msgstr "File size"

#: includes/fields/class-acf-field-image.php:235
#: includes/fields/class-acf-field-image.php:271
msgid "Restrict which images can be uploaded"
msgstr "Restrict which images can be uploaded"

#: includes/fields/class-acf-field-file.php:252
#: includes/fields/class-acf-field-image.php:234
msgid "Minimum"
msgstr "Minimum"

#: includes/fields/class-acf-field-file.php:222
#: includes/fields/class-acf-field-image.php:200
msgid "Uploaded to post"
msgstr "Uploaded to post"

#: includes/fields/class-acf-field-file.php:221
#: includes/fields/class-acf-field-image.php:199
#: includes/locations/class-acf-location-attachment.php:73
#: includes/locations/class-acf-location-comment.php:61
#: includes/locations/class-acf-location-nav-menu.php:74
#: includes/locations/class-acf-location-taxonomy.php:63
#: includes/locations/class-acf-location-user-form.php:71
#: includes/locations/class-acf-location-user-role.php:78
#: includes/locations/class-acf-location-widget.php:65
msgid "All"
msgstr "All"

#: includes/fields/class-acf-field-file.php:216
#: includes/fields/class-acf-field-image.php:194
msgid "Limit the media library choice"
msgstr "Limit the media library choice"

#: includes/fields/class-acf-field-file.php:215
#: includes/fields/class-acf-field-image.php:193
msgid "Library"
msgstr "Library"

#: includes/fields/class-acf-field-image.php:326
msgid "Preview Size"
msgstr "Preview Size"

#: includes/fields/class-acf-field-image.php:185
msgid "Image ID"
msgstr "Image ID"

#: includes/fields/class-acf-field-image.php:184
msgid "Image URL"
msgstr "Image URL"

#: includes/fields/class-acf-field-image.php:183
msgid "Image Array"
msgstr "Image Array"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-checkbox.php:356
#: includes/fields/class-acf-field-file.php:200
#: includes/fields/class-acf-field-link.php:147
#: includes/fields/class-acf-field-radio.php:203
msgid "Specify the returned value on front end"
msgstr "Specify the returned value on front end"

#: includes/fields/class-acf-field-button-group.php:157
#: includes/fields/class-acf-field-checkbox.php:355
#: includes/fields/class-acf-field-file.php:199
#: includes/fields/class-acf-field-link.php:146
#: includes/fields/class-acf-field-radio.php:202
#: includes/fields/class-acf-field-taxonomy.php:672
msgid "Return Value"
msgstr "Return Value"

#: includes/fields/class-acf-field-image.php:155
msgid "Add Image"
msgstr "Add Image"

#: includes/fields/class-acf-field-image.php:155
msgid "No image selected"
msgstr "No image selected"

#: includes/assets.php:349 includes/fields/class-acf-field-file.php:152
#: includes/fields/class-acf-field-image.php:135
#: includes/fields/class-acf-field-link.php:124
msgid "Remove"
msgstr "Remove"

#: includes/admin/views/acf-field-group/field.php:89
#: includes/fields/class-acf-field-file.php:150
#: includes/fields/class-acf-field-image.php:133
#: includes/fields/class-acf-field-link.php:124
msgid "Edit"
msgstr "Edit"

#: includes/fields/class-acf-field-image.php:63 includes/media.php:55
msgid "All images"
msgstr "All images"

#: includes/fields/class-acf-field-image.php:62
msgid "Update Image"
msgstr "Update Image"

#: includes/fields/class-acf-field-image.php:61
msgid "Edit Image"
msgstr "Edit Image"

#: includes/fields/class-acf-field-image.php:60
msgid "Select Image"
msgstr "Select Image"

#: includes/fields/class-acf-field-image.php:22
msgid "Image"
msgstr "Image"

#: includes/fields/class-acf-field-message.php:113
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr "Allow HTML markup to display as visible text instead of rendering"

#: includes/fields/class-acf-field-message.php:112
msgid "Escape HTML"
msgstr "Escape HTML"

#: includes/fields/class-acf-field-message.php:104
#: includes/fields/class-acf-field-textarea.php:162
msgid "No Formatting"
msgstr "No Formatting"

#: includes/fields/class-acf-field-message.php:103
#: includes/fields/class-acf-field-textarea.php:161
msgid "Automatically add &lt;br&gt;"
msgstr "Automatically add &lt;br&gt;"

#: includes/fields/class-acf-field-message.php:102
#: includes/fields/class-acf-field-textarea.php:160
msgid "Automatically add paragraphs"
msgstr "Automatically add paragraphs"

#: includes/fields/class-acf-field-message.php:98
#: includes/fields/class-acf-field-textarea.php:156
msgid "Controls how new lines are rendered"
msgstr "Controls how new lines are rendered"

#: includes/fields/class-acf-field-message.php:97
#: includes/fields/class-acf-field-textarea.php:155
msgid "New Lines"
msgstr "New Lines"

#: includes/fields/class-acf-field-date_picker.php:223
#: includes/fields/class-acf-field-date_time_picker.php:210
msgid "Week Starts On"
msgstr "Week Starts On"

#: includes/fields/class-acf-field-date_picker.php:192
msgid "The format used when saving a value"
msgstr "The format used when saving a value"

#: includes/fields/class-acf-field-date_picker.php:191
msgid "Save Format"
msgstr "Save Format"

#: includes/fields/class-acf-field-date_picker.php:62
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Wk"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Prev"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Next"

#: includes/fields/class-acf-field-date_picker.php:59
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Today"

#: includes/fields/class-acf-field-date_picker.php:58
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Done"

#: includes/fields/class-acf-field-date_picker.php:22
msgid "Date Picker"
msgstr "Date Picker"

#: includes/fields/class-acf-field-image.php:238
#: includes/fields/class-acf-field-image.php:274
#: includes/fields/class-acf-field-oembed.php:240
msgid "Width"
msgstr "Width"

#: includes/fields/class-acf-field-oembed.php:237
#: includes/fields/class-acf-field-oembed.php:249
msgid "Embed Size"
msgstr "Embed Size"

#: includes/fields/class-acf-field-oembed.php:197
msgid "Enter URL"
msgstr "Enter URL"

#: includes/fields/class-acf-field-oembed.php:21
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-true_false.php:172
msgid "Text shown when inactive"
msgstr "Text shown when inactive"

#: includes/fields/class-acf-field-true_false.php:171
msgid "Off Text"
msgstr "Off Text"

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr "Text shown when active"

#: includes/fields/class-acf-field-true_false.php:155
msgid "On Text"
msgstr "On Text"

#: includes/fields/class-acf-field-select.php:420
#: includes/fields/class-acf-field-true_false.php:187
msgid "Stylized UI"
msgstr "Stylised UI"

#: includes/fields/class-acf-field-button-group.php:147
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-color_picker.php:144
#: includes/fields/class-acf-field-email.php:103
#: includes/fields/class-acf-field-number.php:113
#: includes/fields/class-acf-field-radio.php:192
#: includes/fields/class-acf-field-range.php:153
#: includes/fields/class-acf-field-select.php:351
#: includes/fields/class-acf-field-text.php:95
#: includes/fields/class-acf-field-textarea.php:93
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:84
#: includes/fields/class-acf-field-wysiwyg.php:291
msgid "Default Value"
msgstr "Default Value"

#: includes/fields/class-acf-field-true_false.php:126
msgid "Displays text alongside the checkbox"
msgstr "Displays text alongside the checkbox"

#: includes/fields/class-acf-field-message.php:23
#: includes/fields/class-acf-field-message.php:87
#: includes/fields/class-acf-field-true_false.php:125
msgid "Message"
msgstr "Message"

#: includes/assets.php:348 includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:175
#: src/Site_Health/Site_Health.php:281 src/Site_Health/Site_Health.php:343
msgid "No"
msgstr "No"

#: includes/assets.php:347 includes/fields/class-acf-field-true_false.php:76
#: includes/fields/class-acf-field-true_false.php:159
#: src/Site_Health/Site_Health.php:280 src/Site_Health/Site_Health.php:343
msgid "Yes"
msgstr "Yes"

#: includes/fields/class-acf-field-true_false.php:22
msgid "True / False"
msgstr "True / False"

#: includes/fields/class-acf-field-group.php:414
msgid "Row"
msgstr "Row"

#: includes/fields/class-acf-field-group.php:413
msgid "Table"
msgstr "Table"

#: includes/admin/post-types/admin-field-group.php:157
#: includes/fields/class-acf-field-group.php:412
msgid "Block"
msgstr "Block"

#: includes/fields/class-acf-field-group.php:407
msgid "Specify the style used to render the selected fields"
msgstr "Specify the style used to render the selected fields"

#: includes/fields.php:331 includes/fields/class-acf-field-button-group.php:204
#: includes/fields/class-acf-field-checkbox.php:418
#: includes/fields/class-acf-field-group.php:406
#: includes/fields/class-acf-field-radio.php:276
msgid "Layout"
msgstr "Layout"

#: includes/fields/class-acf-field-group.php:390
msgid "Sub Fields"
msgstr "Sub Fields"

#: includes/fields/class-acf-field-group.php:21
msgid "Group"
msgstr "Group"

#: includes/fields/class-acf-field-google-map.php:221
msgid "Customize the map height"
msgstr "Customise the map height"

#: includes/fields/class-acf-field-google-map.php:220
#: includes/fields/class-acf-field-image.php:249
#: includes/fields/class-acf-field-image.php:285
#: includes/fields/class-acf-field-oembed.php:252
msgid "Height"
msgstr "Height"

#: includes/fields/class-acf-field-google-map.php:209
msgid "Set the initial zoom level"
msgstr "Set the initial zoom level"

#: includes/fields/class-acf-field-google-map.php:208
msgid "Zoom"
msgstr "Zoom"

#: includes/fields/class-acf-field-google-map.php:182
#: includes/fields/class-acf-field-google-map.php:195
msgid "Center the initial map"
msgstr "Centre the initial map"

#: includes/fields/class-acf-field-google-map.php:181
#: includes/fields/class-acf-field-google-map.php:194
msgid "Center"
msgstr "Centre"

#: includes/fields/class-acf-field-google-map.php:153
msgid "Search for address..."
msgstr "Search for address..."

#: includes/fields/class-acf-field-google-map.php:150
msgid "Find current location"
msgstr "Find current location"

#: includes/fields/class-acf-field-google-map.php:149
msgid "Clear location"
msgstr "Clear location"

#: includes/fields/class-acf-field-google-map.php:148
#: includes/fields/class-acf-field-relationship.php:600
msgid "Search"
msgstr "Search"

#: includes/fields/class-acf-field-google-map.php:56
msgid "Sorry, this browser does not support geolocation"
msgstr "Sorry, this browser does not support geolocation"

#: includes/fields/class-acf-field-google-map.php:21
msgid "Google Map"
msgstr "Google Map"

#: includes/fields/class-acf-field-date_picker.php:203
#: includes/fields/class-acf-field-date_time_picker.php:191
#: includes/fields/class-acf-field-time_picker.php:122
msgid "The format returned via template functions"
msgstr "The format returned via template functions"

#: includes/fields/class-acf-field-color_picker.php:168
#: includes/fields/class-acf-field-date_picker.php:202
#: includes/fields/class-acf-field-date_time_picker.php:190
#: includes/fields/class-acf-field-icon_picker.php:292
#: includes/fields/class-acf-field-image.php:177
#: includes/fields/class-acf-field-post_object.php:396
#: includes/fields/class-acf-field-relationship.php:610
#: includes/fields/class-acf-field-select.php:362
#: includes/fields/class-acf-field-time_picker.php:121
#: includes/fields/class-acf-field-user.php:95
msgid "Return Format"
msgstr "Return Format"

#: includes/fields/class-acf-field-date_picker.php:181
#: includes/fields/class-acf-field-date_picker.php:212
#: includes/fields/class-acf-field-date_time_picker.php:182
#: includes/fields/class-acf-field-date_time_picker.php:200
#: includes/fields/class-acf-field-time_picker.php:113
#: includes/fields/class-acf-field-time_picker.php:129
msgid "Custom:"
msgstr "Custom:"

#: includes/fields/class-acf-field-date_picker.php:173
#: includes/fields/class-acf-field-date_time_picker.php:173
#: includes/fields/class-acf-field-time_picker.php:106
msgid "The format displayed when editing a post"
msgstr "The format displayed when editing a post"

#: includes/fields/class-acf-field-date_picker.php:172
#: includes/fields/class-acf-field-date_time_picker.php:172
#: includes/fields/class-acf-field-time_picker.php:105
msgid "Display Format"
msgstr "Display Format"

#: includes/fields/class-acf-field-time_picker.php:22
msgid "Time Picker"
msgstr "Time Picker"

#. translators: counts for inactive field groups
#: acf.php:567
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "Inactive <span class=\"count\">(%s)</span>"
msgstr[1] "Inactive <span class=\"count\">(%s)</span>"

#: acf.php:528
msgid "No Fields found in Trash"
msgstr "No Fields found in Bin"

#: acf.php:527
msgid "No Fields found"
msgstr "No Fields found"

#: acf.php:526
msgid "Search Fields"
msgstr "Search Fields"

#: acf.php:525
msgid "View Field"
msgstr "View Field"

#: acf.php:524 includes/admin/views/acf-field-group/fields.php:113
msgid "New Field"
msgstr "New Field"

#: acf.php:523
msgid "Edit Field"
msgstr "Edit Field"

#: acf.php:522
msgid "Add New Field"
msgstr "Add New Field"

#: acf.php:520
msgid "Field"
msgstr "Field"

#: acf.php:519 includes/admin/post-types/admin-field-group.php:178
#: includes/admin/post-types/admin-field-groups.php:89
#: includes/admin/views/acf-field-group/fields.php:32
msgid "Fields"
msgstr "Fields"

#: acf.php:494
msgid "No Field Groups found in Trash"
msgstr "No Field Groups found in bin"

#: acf.php:493
msgid "No Field Groups found"
msgstr "No Field Groups found"

#: acf.php:492
msgid "Search Field Groups"
msgstr "Search Field Groups"

#: acf.php:491
msgid "View Field Group"
msgstr "View Field Group"

#: acf.php:490
msgid "New Field Group"
msgstr "New Field Group"

#: acf.php:489
msgid "Edit Field Group"
msgstr "Edit Field Group"

#: acf.php:488
msgid "Add New Field Group"
msgstr "Add New Field Group"

#: acf.php:487 acf.php:521
#: includes/admin/views/acf-post-type/advanced-settings.php:224
#: includes/post-types/class-acf-post-type.php:93
#: includes/post-types/class-acf-taxonomy.php:92
msgid "Add New"
msgstr "Add New"

#: acf.php:486
msgid "Field Group"
msgstr "Field Group"

#: acf.php:485 includes/admin/post-types/admin-field-groups.php:52
#: includes/admin/post-types/admin-post-types.php:109
#: includes/admin/post-types/admin-taxonomies.php:108
msgid "Field Groups"
msgstr "Field Groups"

#. Description of the plugin
#: acf.php
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr "Customise WordPress with powerful, professional and intuitive fields."

#. Plugin URI of the plugin
#: acf.php
msgid "https://www.advancedcustomfields.com"
msgstr "https://www.advancedcustomfields.com"

#. Plugin Name of the plugin
#: acf.php acf.php:331
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: pro/acf-pro.php:21
msgid "Advanced Custom Fields PRO"
msgstr ""

#: pro/acf-pro.php:174
msgid ""
"Your ACF PRO license is no longer active. Please renew to continue to have "
"access to updates, support, & PRO features."
msgstr ""
"Your ACF PRO licence is no longer active. Please renew to continue to have "
"access to updates, support, & PRO features."

#: pro/acf-pro.php:171
msgid ""
"Your license has expired. Please renew to continue to have access to "
"updates, support &amp; PRO features."
msgstr ""
"Your licence has expired. Please renew to continue to have access to "
"updates, support &amp; PRO features."

#: pro/acf-pro.php:168
msgid ""
"Activate your license to enable access to updates, support &amp; PRO "
"features."
msgstr ""
"Activate your licence to enable access to updates, support &amp; PRO "
"features."

#: pro/acf-pro.php:257
msgid "A valid license is required to edit options pages."
msgstr "A valid licence is required to edit options pages."

#: pro/acf-pro.php:255
msgid "A valid license is required to edit field groups assigned to a block."
msgstr "A valid licence is required to edit field groups assigned to a block."

#: pro/blocks.php:186
msgid "Block type name is required."
msgstr ""

#. translators: The name of the block type
#: pro/blocks.php:194
msgid "Block type \"%s\" is already registered."
msgstr ""

#: pro/blocks.php:740
msgid "The render template for this ACF Block was not found"
msgstr ""

#: pro/blocks.php:790
msgid "Switch to Edit"
msgstr ""

#: pro/blocks.php:791
msgid "Switch to Preview"
msgstr ""

#: pro/blocks.php:792
msgid "Change content alignment"
msgstr ""

#: pro/blocks.php:793
msgid "An error occurred when loading the preview for this block."
msgstr ""

#: pro/blocks.php:794
msgid "An error occurred when loading the block in edit mode."
msgstr ""

#. translators: %s: Block type title
#: pro/blocks.php:797
msgid "%s settings"
msgstr ""

#: pro/blocks.php:1039
msgid "This block contains no editable fields."
msgstr ""

#. translators: %s: an admin URL to the field group edit screen
#: pro/blocks.php:1045
msgid ""
"Assign a <a href=\"%s\" target=\"_blank\">field group</a> to add fields to "
"this block."
msgstr ""

#: pro/options-page.php:74, pro/post-types/acf-ui-options-page.php:174
msgid "Options Updated"
msgstr ""

#. translators: %1 A link to the updates page. %2 link to the pricing page
#: pro/updates.php:75
msgid ""
"To enable updates, please enter your license key on the <a "
"href=\"%1$s\">Updates</a> page. If you don't have a license key, please see "
"<a href=\"%2$s\" target=\"_blank\">details & pricing</a>."
msgstr ""
"To enable updates, please enter your licence key on the <a "
"href=\"%1$s\">Updates</a> page. If you don’t have a licence key, please see "
"<a href=\"%2$s\" target=\"_blank\">details & pricing</a>."

#: pro/updates.php:71
msgid ""
"To enable updates, please enter your license key on the <a "
"href=\"%1$s\">Updates</a> page of the main site. If you don't have a license "
"key, please see <a href=\"%2$s\" target=\"_blank\">details & pricing</a>."
msgstr ""
"To enable updates, please enter your licence key on the <a "
"href=\"%1$s\">Updates</a> page of the main site. If you don’t have a licence "
"key, please see <a href=\"%2$s\" target=\"_blank\">details & pricing</a>."

#: pro/updates.php:136
msgid ""
"Your defined license key has changed, but an error occurred when "
"deactivating your old license"
msgstr ""
"Your defined licence key has changed, but an error occurred when "
"deactivating your old licence"

#: pro/updates.php:133
msgid ""
"Your defined license key has changed, but an error occurred when connecting "
"to activation server"
msgstr ""
"Your defined licence key has changed, but an error occurred when connecting "
"to activation server"

#: pro/updates.php:168
msgid ""
"<strong>ACF PRO &mdash;</strong> Your license key has been activated "
"successfully. Access to updates, support &amp; PRO features is now enabled."
msgstr ""
"<strong>ACF PRO &mdash;</strong> Your licence key has been activated "
"successfully. Access to updates, support &amp; PRO features is now enabled."

#: pro/updates.php:159
msgid "There was an issue activating your license key."
msgstr "There was an issue activating your licence key."

#: pro/updates.php:155
msgid "An error occurred when connecting to activation server"
msgstr ""

#: pro/updates.php:258
msgid ""
"The ACF activation service is temporarily unavailable. Please try again "
"later."
msgstr ""

#: pro/updates.php:256
msgid ""
"The ACF activation service is temporarily unavailable for scheduled "
"maintenance. Please try again later."
msgstr ""

#: pro/updates.php:254
msgid ""
"An upstream API error occurred when checking your ACF PRO license status. We "
"will retry again shortly."
msgstr ""
"An upstream API error occurred when checking your ACF PRO licence status. We "
"will retry again shortly."

#: pro/updates.php:224
msgid "You have reached the activation limit for the license."
msgstr "You have reached the activation limit for the licence."

#: pro/updates.php:233, pro/updates.php:205
msgid "View your licenses"
msgstr "View your licences"

#: pro/updates.php:246
msgid "check again"
msgstr ""

#: pro/updates.php:250
msgid "%1$s or %2$s."
msgstr ""

#: pro/updates.php:210
msgid "Your license key has expired and cannot be activated."
msgstr "Your licence key has expired and cannot be activated."

#: pro/updates.php:219
msgid "View your subscriptions"
msgstr ""

#: pro/updates.php:196
msgid ""
"License key not found. Make sure you have copied your license key exactly as "
"it appears in your receipt or your account."
msgstr ""
"Licence key not found. Make sure you have copied your licence key exactly as "
"it appears in your receipt or your account."

#: pro/updates.php:194
msgid "Your license key has been deactivated."
msgstr "Your licence key has been deactivated."

#: pro/updates.php:192
msgid ""
"Your license key has been activated successfully. Access to updates, support "
"&amp; PRO features is now enabled."
msgstr ""
"Your licence key has been activated successfully. Access to updates, support "
"&amp; PRO features is now enabled."

#. translators: %s an untranslatable internal upstream error message
#: pro/updates.php:262
msgid ""
"An unknown error occurred while trying to communicate with the ACF "
"activation service: %s."
msgstr ""

#: pro/updates.php:333, pro/updates.php:949
msgid "<strong>ACF PRO &mdash;</strong>"
msgstr ""

#: pro/updates.php:342
msgid "Check again"
msgstr ""

#: pro/updates.php:657
msgid "Could not connect to the activation server"
msgstr ""

#. translators: %s - URL to ACF updates page
#: pro/updates.php:727
msgid ""
"Your license key is valid but not activated on this site. Please <a "
"href=\"%s\">deactivate</a> and then reactivate the license."
msgstr ""
"Your licence key is valid but not activated on this site. Please <a "
"href=\"%s\">deactivate</a> and then reactivate the licence."

#: pro/updates.php:949
msgid ""
"Your site URL has changed since last activating your license. We've "
"automatically activated it for this site URL."
msgstr ""
"Your site URL has changed since last activating your licence. We’ve "
"automatically activated it for this site URL."

#: pro/updates.php:941
msgid ""
"Your site URL has changed since last activating your license, but we weren't "
"able to automatically reactivate it: %s"
msgstr ""
"Your site URL has changed since last activating your licence, but we weren’t "
"able to automatically reactivate it: %s"

#: pro/admin/admin-options-page.php:159
msgid "Publish"
msgstr ""

#: pro/admin/admin-options-page.php:162
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""

#: pro/admin/admin-updates.php:52
msgid "<strong>Error</strong>. Could not connect to the update server"
msgstr ""

#. translators: %s the version of WordPress required for this ACF update
#: pro/admin/admin-updates.php:203
msgid ""
"An update to ACF is available, but it is not compatible with your version of "
"WordPress. Please upgrade to WordPress %s or newer to update ACF."
msgstr ""

#: pro/admin/admin-updates.php:224
msgid ""
"<strong>Error</strong>. Could not authenticate update package. Please check "
"again or deactivate and reactivate your ACF PRO license."
msgstr ""
"<strong>Error</strong>. Could not authenticate update package. Please check "
"again or deactivate and reactivate your ACF PRO licence."

#: pro/admin/admin-updates.php:214
msgid ""
"<strong>Error</strong>. Your license for this site has expired or been "
"deactivated. Please reactivate your ACF PRO license."
msgstr ""
"<strong>Error</strong>. Your licence for this site has expired or been "
"deactivated. Please reactivate your ACF PRO licence."

#: pro/fields/class-acf-field-clone.php:24
msgid ""
"Allows you to select and display existing fields. It does not duplicate any "
"fields in the database, but loads and displays the selected fields at run-"
"time. The Clone field can either replace itself with the selected fields or "
"display the selected fields as a group of subfields."
msgstr ""

#: pro/fields/class-acf-field-clone.php:725
msgid "Select one or more fields you wish to clone"
msgstr ""

#: pro/fields/class-acf-field-clone.php:745
msgid "Display"
msgstr ""

#: pro/fields/class-acf-field-clone.php:746
msgid "Specify the style used to render the clone field"
msgstr ""

#: pro/fields/class-acf-field-clone.php:751
msgid "Group (displays selected fields in a group within this field)"
msgstr ""

#: pro/fields/class-acf-field-clone.php:752
msgid "Seamless (replaces this field with selected fields)"
msgstr ""

#: pro/fields/class-acf-field-clone.php:775
msgid "Labels will be displayed as %s"
msgstr ""

#: pro/fields/class-acf-field-clone.php:780
msgid "Prefix Field Labels"
msgstr ""

#: pro/fields/class-acf-field-clone.php:790
msgid "Values will be saved as %s"
msgstr ""

#: pro/fields/class-acf-field-clone.php:795
msgid "Prefix Field Names"
msgstr ""

#: pro/fields/class-acf-field-clone.php:892
msgid "Unknown field"
msgstr ""

#: pro/fields/class-acf-field-clone.php:925
msgid "Unknown field group"
msgstr ""

#: pro/fields/class-acf-field-clone.php:929
msgid "All fields from %s field group"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:24
msgid ""
"Allows you to define, create and manage content with total control by "
"creating layouts that contain subfields that content editors can choose from."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:34,
#: pro/fields/class-acf-field-repeater.php:104,
#: pro/fields/class-acf-field-repeater.php:298
msgid "Add Row"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:70,
#: pro/fields/class-acf-field-flexible-content.php:867,
#: pro/fields/class-acf-field-flexible-content.php:949
msgid "layout"
msgid_plural "layouts"
msgstr[0] ""
msgstr[1] ""

#: pro/fields/class-acf-field-flexible-content.php:71
msgid "layouts"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:75,
#: pro/fields/class-acf-field-flexible-content.php:866,
#: pro/fields/class-acf-field-flexible-content.php:948
msgid "This field requires at least {min} {label} {identifier}"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:76
msgid "This field has a limit of {max} {label} {identifier}"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:79
msgid "{available} {label} {identifier} available (max {max})"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:80
msgid "{required} {label} {identifier} required (min {min})"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:83
msgid "Flexible Content requires at least 1 layout"
msgstr ""

#. translators: %s the button label used for adding a new layout.
#: pro/fields/class-acf-field-flexible-content.php:255
msgid "Click the \"%s\" button below to start creating your layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:378
msgid "Add layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:379
msgid "Duplicate layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:380
msgid "Remove layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:381,
#: pro/fields/class-acf-repeater-table.php:380
msgid "Click to toggle"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:517
msgid "Delete Layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:518
msgid "Duplicate Layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:519
msgid "Add New Layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:519
msgid "Add Layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:603
msgid "Min"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:618
msgid "Max"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:659
msgid "Minimum Layouts"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:670
msgid "Maximum Layouts"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:681,
#: pro/fields/class-acf-field-repeater.php:294
msgid "Button Label"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:1552,
#: pro/fields/class-acf-field-repeater.php:913
msgid "%s must be of type array or null."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:1563
msgid "%1$s must contain at least %2$s %3$s layout."
msgid_plural "%1$s must contain at least %2$s %3$s layouts."
msgstr[0] ""
msgstr[1] ""

#: pro/fields/class-acf-field-flexible-content.php:1579
msgid "%1$s must contain at most %2$s %3$s layout."
msgid_plural "%1$s must contain at most %2$s %3$s layouts."
msgstr[0] ""
msgstr[1] ""

#: pro/fields/class-acf-field-gallery.php:24
msgid ""
"An interactive interface for managing a collection of attachments, such as "
"images."
msgstr ""

#: pro/fields/class-acf-field-gallery.php:72
msgid "Add Image to Gallery"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:73
msgid "Maximum selection reached"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:282
msgid "Length"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:326
msgid "Caption"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:338
msgid "Alt Text"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:460
msgid "Add to gallery"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:464
msgid "Bulk actions"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:465
msgid "Sort by date uploaded"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:466
msgid "Sort by date modified"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:467
msgid "Sort by title"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:468
msgid "Reverse current order"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:480
msgid "Close"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:567
msgid "Minimum Selection"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:577
msgid "Maximum Selection"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:679
msgid "Insert"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:680
msgid "Specify where new attachments are added"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:684
msgid "Append to the end"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:685
msgid "Prepend to the beginning"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:31
msgid ""
"Provides a solution for repeating content such as slides, team members, and "
"call-to-action tiles, by acting as a parent to a set of subfields which can "
"be repeated again and again."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:67,
#: pro/fields/class-acf-field-repeater.php:462
msgid "Minimum rows not reached ({min} rows)"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:68
msgid "Maximum rows reached ({max} rows)"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:69
msgid "Error loading page"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:70
msgid "Order will be assigned upon save"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:197
msgid "Useful for fields with a large number of rows."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:208
msgid "Rows Per Page"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:209
msgid "Set the number of rows to be displayed on a page."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:241
msgid "Minimum Rows"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:252
msgid "Maximum Rows"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:282
msgid "Collapsed"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:283
msgid "Select a sub field to show when row is collapsed"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:1055
msgid "Invalid field key or name."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:1064
msgid "There was an error retrieving the field."
msgstr ""

#: pro/fields/class-acf-repeater-table.php:367
msgid "Click to reorder"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:400
msgid "Add row"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:401
msgid "Duplicate row"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:402
msgid "Remove row"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:446,
#: pro/fields/class-acf-repeater-table.php:463,
#: pro/fields/class-acf-repeater-table.php:464
msgid "Current Page"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:454,
#: pro/fields/class-acf-repeater-table.php:455
msgid "First Page"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:458,
#: pro/fields/class-acf-repeater-table.php:459
msgid "Previous Page"
msgstr ""

#. translators: 1: Current page, 2: Total pages.
#: pro/fields/class-acf-repeater-table.php:468
msgctxt "paging"
msgid "%1$s of %2$s"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:475,
#: pro/fields/class-acf-repeater-table.php:476
msgid "Next Page"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:479,
#: pro/fields/class-acf-repeater-table.php:480
msgid "Last Page"
msgstr ""

#: pro/locations/class-acf-location-block.php:73
msgid "No block types exist"
msgstr ""

#: pro/locations/class-acf-location-options-page.php:70
msgid "Select options page..."
msgstr ""

#: pro/locations/class-acf-location-options-page.php:74,
#: pro/post-types/acf-ui-options-page.php:95,
#: pro/admin/post-types/admin-ui-options-page.php:482
msgid "Add New Options Page"
msgstr ""

#: pro/post-types/acf-ui-options-page.php:96
msgid "Edit Options Page"
msgstr ""

#: pro/post-types/acf-ui-options-page.php:97
msgid "New Options Page"
msgstr ""

#: pro/post-types/acf-ui-options-page.php:98
msgid "View Options Page"
msgstr ""

#: pro/post-types/acf-ui-options-page.php:99
msgid "Search Options Pages"
msgstr ""

#: pro/post-types/acf-ui-options-page.php:100
msgid "No Options Pages found"
msgstr ""

#: pro/post-types/acf-ui-options-page.php:101
msgid "No Options Pages found in Trash"
msgstr "No Options Pages found in Bin"

#: pro/post-types/acf-ui-options-page.php:203
msgid ""
"The menu slug must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""

#: pro/post-types/acf-ui-options-page.php:235
msgid "This Menu Slug is already in use by another ACF Options Page."
msgstr ""

#: pro/admin/post-types/admin-ui-options-page.php:56
msgid "Options page deleted."
msgstr ""

#: pro/admin/post-types/admin-ui-options-page.php:57
msgid "Options page updated."
msgstr ""

#: pro/admin/post-types/admin-ui-options-page.php:60
msgid "Options page saved."
msgstr ""

#: pro/admin/post-types/admin-ui-options-page.php:61
msgid "Options page submitted."
msgstr ""

#: pro/admin/post-types/admin-ui-options-page.php:62
msgid "Options page scheduled for."
msgstr ""

#: pro/admin/post-types/admin-ui-options-page.php:63
msgid "Options page draft updated."
msgstr ""

#. translators: %s options page name
#: pro/admin/post-types/admin-ui-options-page.php:83
msgid "%s options page updated"
msgstr ""

#. translators: %s options page name
#: pro/admin/post-types/admin-ui-options-page.php:89
msgid "%s options page created"
msgstr ""

#: pro/admin/post-types/admin-ui-options-page.php:102
msgid "Link existing field groups"
msgstr ""

#: pro/admin/post-types/admin-ui-options-page.php:361
msgid "No Parent"
msgstr ""

#: pro/admin/post-types/admin-ui-options-page.php:450
msgid "The provided Menu Slug already exists."
msgstr ""

#. translators: %s number of post types activated
#: pro/admin/post-types/admin-ui-options-pages.php:179
msgid "Options page activated."
msgid_plural "%s options pages activated."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of post types deactivated
#: pro/admin/post-types/admin-ui-options-pages.php:186
msgid "Options page deactivated."
msgid_plural "%s options pages deactivated."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of post types duplicated
#: pro/admin/post-types/admin-ui-options-pages.php:193
msgid "Options page duplicated."
msgid_plural "%s options pages duplicated."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of post types synchronized
#: pro/admin/post-types/admin-ui-options-pages.php:200
msgid "Options page synchronized."
msgid_plural "%s options pages synchronized."
msgstr[0] ""
msgstr[1] ""

#: pro/admin/views/html-settings-updates.php:9
msgid "Deactivate License"
msgstr "Deactivate Licence"

#: pro/admin/views/html-settings-updates.php:9
msgid "Activate License"
msgstr "Activate Licence"

#: pro/admin/views/html-settings-updates.php:26
msgctxt "license status"
msgid "Inactive"
msgstr ""

#: pro/admin/views/html-settings-updates.php:34
msgctxt "license status"
msgid "Cancelled"
msgstr ""

#: pro/admin/views/html-settings-updates.php:32
msgctxt "license status"
msgid "Expired"
msgstr ""

#: pro/admin/views/html-settings-updates.php:30
msgctxt "license status"
msgid "Active"
msgstr ""

#: pro/admin/views/html-settings-updates.php:47
msgid "Subscription Status"
msgstr ""

#: pro/admin/views/html-settings-updates.php:60
msgid "Subscription Type"
msgstr ""

#: pro/admin/views/html-settings-updates.php:67
msgid "Lifetime - "
msgstr ""

#: pro/admin/views/html-settings-updates.php:81
msgid "Subscription Expires"
msgstr ""

#: pro/admin/views/html-settings-updates.php:79
msgid "Subscription Expired"
msgstr ""

#: pro/admin/views/html-settings-updates.php:118
msgid "Renew Subscription"
msgstr ""

#: pro/admin/views/html-settings-updates.php:136
msgid "License Information"
msgstr "Licence Information"

#: pro/admin/views/html-settings-updates.php:170
msgid "License Key"
msgstr "Licence Key"

#: pro/admin/views/html-settings-updates.php:191,
#: pro/admin/views/html-settings-updates.php:157
msgid "Recheck License"
msgstr "Recheck Licence"

#: pro/admin/views/html-settings-updates.php:142
msgid "Your license key is defined in wp-config.php."
msgstr "Your licence key is defined in wp-config.php."

#: pro/admin/views/html-settings-updates.php:211
msgid "View pricing & purchase"
msgstr ""

#. translators: %s - link to ACF website
#: pro/admin/views/html-settings-updates.php:220
msgid "Don't have an ACF PRO license? %s"
msgstr "Don’t have an ACF PRO licence? %s"

#: pro/admin/views/html-settings-updates.php:235
msgid "Update Information"
msgstr ""

#: pro/admin/views/html-settings-updates.php:242
msgid "Current Version"
msgstr ""

#: pro/admin/views/html-settings-updates.php:250
msgid "Latest Version"
msgstr ""

#: pro/admin/views/html-settings-updates.php:258
msgid "Update Available"
msgstr ""

#: pro/admin/views/html-settings-updates.php:272
msgid "Upgrade Notice"
msgstr ""

#: pro/admin/views/html-settings-updates.php:303
msgid "Check For Updates"
msgstr ""

#: pro/admin/views/html-settings-updates.php:300
msgid "Enter your license key to unlock updates"
msgstr "Enter your licence key to unlock updates"

#: pro/admin/views/html-settings-updates.php:298
msgid "Update Plugin"
msgstr ""

#: pro/admin/views/html-settings-updates.php:296
msgid "Updates must be manually installed in this configuration"
msgstr ""

#: pro/admin/views/html-settings-updates.php:294
msgid "Update ACF in Network Admin"
msgstr ""

#: pro/admin/views/html-settings-updates.php:292
msgid "Please reactivate your license to unlock updates"
msgstr "Please reactivate your licence to unlock updates"

#: pro/admin/views/html-settings-updates.php:290
msgid "Please upgrade WordPress to update ACF"
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:20
msgid "Dashicon class name"
msgstr ""

#. translators: %s = "dashicon class name", link to the WordPress dashicon documentation.
#: pro/admin/views/acf-ui-options-page/advanced-settings.php:25
msgid ""
"The icon used for the options page menu item in the admin dashboard. Can be "
"a URL or %s to use for the icon."
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:80
msgid "Menu Title"
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:94
msgid "Learn more about menu positions."
msgstr ""

#. translators: %s - link to WordPress docs to learn more about menu positions.
#: pro/admin/views/acf-ui-options-page/advanced-settings.php:98,
#: pro/admin/views/acf-ui-options-page/advanced-settings.php:104
msgid "The position in the menu where this page should appear. %s"
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:108
msgid ""
"The position in the menu where this child page should appear. The first "
"child page is 0, the next is 1, etc."
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:129
msgid "Redirect to Child Page"
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:130
msgid ""
"When child pages exist for this parent page, this page will redirect to the "
"first child page."
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:154
msgid "A descriptive summary of the options page."
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:163
msgid "Update Button Label"
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:164
msgid ""
"The label used for the submit button which updates the fields on the options "
"page."
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:178
msgid "Updated Message"
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:179
msgid ""
"The message that is displayed after successfully updating the options page."
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:180
msgid "Updated Options"
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:217
msgid "Capability"
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:218
msgid "The capability required for this menu to be displayed to the user."
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:234
msgid "Data Storage"
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:235
msgid ""
"By default, the option page stores field data in the options table. You can "
"make the page load field data from a post, user, or term."
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:238,
#: pro/admin/views/acf-ui-options-page/advanced-settings.php:269
msgid "Custom Storage"
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:258
msgid "Learn more about available settings."
msgstr ""

#. translators: %s = link to learn more about storage locations.
#: pro/admin/views/acf-ui-options-page/advanced-settings.php:263
msgid ""
"Set a custom storage location. Can be a numeric post ID (123), or a string "
"(`user_2`). %s"
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:288
msgid "Autoload Options"
msgstr ""

#: pro/admin/views/acf-ui-options-page/advanced-settings.php:289
msgid ""
"Improve performance by loading the fields in the option records "
"automatically when WordPress loads."
msgstr ""

#: pro/admin/views/acf-ui-options-page/basic-settings.php:20,
#: pro/admin/views/acf-ui-options-page/create-options-page-modal.php:16
msgid "Page Title"
msgstr ""

#. translators: example options page name
#: pro/admin/views/acf-ui-options-page/basic-settings.php:22,
#: pro/admin/views/acf-ui-options-page/create-options-page-modal.php:18
msgid "Site Settings"
msgstr ""

#: pro/admin/views/acf-ui-options-page/basic-settings.php:37,
#: pro/admin/views/acf-ui-options-page/create-options-page-modal.php:33
msgid "Menu Slug"
msgstr ""

#: pro/admin/views/acf-ui-options-page/basic-settings.php:52,
#: pro/admin/views/acf-ui-options-page/create-options-page-modal.php:47
msgid "Parent Page"
msgstr ""

#: pro/admin/views/acf-ui-options-page/list-empty.php:30
msgid "Add Your First Options Page"
msgstr ""
