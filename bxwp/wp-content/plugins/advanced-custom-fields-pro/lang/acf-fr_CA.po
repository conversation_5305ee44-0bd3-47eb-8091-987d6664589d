# Advanced Custom Fields Translations are a combination of translate.wordpress.org contributions,
# combined with user contributed strings for the PRO version.
# Translations from translate.wordpress.org take priority over translations in this file.
# translate.wordpress.org contributions are synced at the time of each release.
#
# If you would like to contribute translations, please visit
# https://translate.wordpress.org/projects/wp-plugins/advanced-custom-fields/stable/
#
# For additional ACF PRO strings, please submit a pull request over on the ACF GitHub repo at
# http://github.com/advancedcustomfields/acf using the .pot (and any existing .po) files in /lang/pro/
#
# This file is distributed under the same license as Advanced Custom Fields.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-08-12T11:23:47+00:00\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"Language: fr_CA\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: gettext\n"
"Project-Id-Version: Advanced Custom Fields\n"

# @ acf
#: pro/acf-pro.php:27
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

# @ default
#: pro/blocks.php:170
msgid "Block type name is required."
msgstr "Le nom de type de bloc est requis."

#. translators: The name of the block type
#: pro/blocks.php:178
msgid "Block type \"%s\" is already registered."
msgstr "Le type de bloc \"%s\" est déjà enregistré."

#: pro/blocks.php:726
msgid "Switch to Edit"
msgstr "Passer en Édition"

#: pro/blocks.php:727
msgid "Switch to Preview"
msgstr "Passer en Prévisualisation"

#: pro/blocks.php:728
msgid "Change content alignment"
msgstr ""

#. translators: %s: Block type title
#: pro/blocks.php:731
msgid "%s settings"
msgstr "Réglages de %s"

#: pro/blocks.php:936
msgid "This block contains no editable fields."
msgstr ""

#. translators: %s: an admin URL to the field group edit screen
#: pro/blocks.php:942
msgid ""
"Assign a <a href=\"%s\" target=\"_blank\">field group</a> to add fields to "
"this block."
msgstr ""

# @ acf
#: pro/options-page.php:47
msgid "Options"
msgstr "Options"

# @ acf
#: pro/options-page.php:77, pro/fields/class-acf-field-gallery.php:527
msgid "Update"
msgstr "Mise à jour"

# @ acf
#: pro/options-page.php:78
msgid "Options Updated"
msgstr "Options mises à jours"

#: pro/updates.php:99
#, fuzzy
#| msgid ""
#| "To enable updates, please enter your license key on the <a "
#| "href=\"%s\">Updates</a> page. If you don't have a licence key, please see "
#| "<a href=\"%s\">details & pricing</a>."
msgid ""
"To enable updates, please enter your license key on the <a "
"href=\"%1$s\">Updates</a> page. If you don't have a licence key, please see "
"<a href=\"%2$s\" target=\"_blank\">details & pricing</a>."
msgstr ""
"Pour activer les mises à jour, veuillez entrer votre clé de licence sur la "
"page <a href=\"%s\">Mises à jour</a>. Si vous n’en avez pas, rendez-vous sur "
"nos <a href=\"%s\">détails & tarifs</a>."

#: pro/updates.php:159
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when deactivating your old licence"
msgstr ""

#: pro/updates.php:154
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when connecting to activation server"
msgstr ""

#: pro/updates.php:192
msgid "<b>ACF Activation Error</b>"
msgstr ""

#: pro/updates.php:187
#, fuzzy
#| msgid "<b>Error</b>. Could not connect to update server"
msgid ""
"<b>ACF Activation Error</b>. An error occurred when connecting to activation "
"server"
msgstr "<b>Erreur</b>. Impossible de joindre le serveur"

#: pro/updates.php:279
msgid "Check Again"
msgstr "Vérifier à nouveau"

#: pro/updates.php:593
#, fuzzy
#| msgid "<b>Error</b>. Could not connect to update server"
msgid "<b>ACF Activation Error</b>. Could not connect to activation server"
msgstr "<b>Erreur</b>. Impossible de joindre le serveur"

#: pro/admin/admin-options-page.php:195
msgid "Publish"
msgstr "Publier"

# @ default
#: pro/admin/admin-options-page.php:199
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"Aucun groupe de champs trouvé pour cette page d’options. <a "
"href=\"%s\">Créer un groupe de champs</a>"

# @ acf
#: pro/admin/admin-options-page.php:309
msgid "Edit field group"
msgstr "Modifier le groupe de champs"

#: pro/admin/admin-updates.php:52
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>Erreur</b>. Impossible de joindre le serveur"

# @ acf
#: pro/admin/admin-updates.php:122,
#: pro/admin/views/html-settings-updates.php:12
msgid "Updates"
msgstr "Mises à jour"

#: pro/admin/admin-updates.php:212
msgid ""
"<b>Error</b>. Could not authenticate update package. Please check again or "
"deactivate and reactivate your ACF PRO license."
msgstr ""
"<b>Erreur</b>. Impossible d’authentifier la mise à jour. Merci d’essayer à "
"nouveau et si le problème persiste, désactivez et réactivez votre licence "
"ACF PRO."

#: pro/admin/admin-updates.php:199
#, fuzzy
#| msgid ""
#| "<b>Error</b>. Could not authenticate update package. Please check again "
#| "or deactivate and reactivate your ACF PRO license."
msgid ""
"<b>Error</b>. Your license for this site has expired or been deactivated. "
"Please reactivate your ACF PRO license."
msgstr ""
"<b>Erreur</b>. Impossible d’authentifier la mise à jour. Merci d’essayer à "
"nouveau et si le problème persiste, désactivez et réactivez votre licence "
"ACF PRO."

#: pro/fields/class-acf-field-clone.php:25
msgctxt "noun"
msgid "Clone"
msgstr "Clone"

#: pro/fields/class-acf-field-clone.php:27,
#: pro/fields/class-acf-field-repeater.php:31
msgid ""
"Allows you to select and display existing fields. It does not duplicate any "
"fields in the database, but loads and displays the selected fields at run-"
"time. The Clone field can either replace itself with the selected fields or "
"display the selected fields as a group of subfields."
msgstr ""

# @ acf
#: pro/fields/class-acf-field-clone.php:818,
#: pro/fields/class-acf-field-flexible-content.php:78
msgid "Fields"
msgstr "Champs"

#: pro/fields/class-acf-field-clone.php:819
msgid "Select one or more fields you wish to clone"
msgstr "Sélectionnez un ou plusieurs champs à cloner"

# @ acf
#: pro/fields/class-acf-field-clone.php:838
msgid "Display"
msgstr "Format d’affichage"

#: pro/fields/class-acf-field-clone.php:839
msgid "Specify the style used to render the clone field"
msgstr "Définit le style utilisé pour générer le champ dupliqué"

#: pro/fields/class-acf-field-clone.php:844
msgid "Group (displays selected fields in a group within this field)"
msgstr ""
"Groupe (affiche les champs sélectionnés dans un groupe à l’intérieur de ce "
"champ)"

#: pro/fields/class-acf-field-clone.php:845
msgid "Seamless (replaces this field with selected fields)"
msgstr "Remplace ce champ par les champs sélectionnés"

# @ acf
#: pro/fields/class-acf-field-clone.php:854,
#: pro/fields/class-acf-field-flexible-content.php:558,
#: pro/fields/class-acf-field-flexible-content.php:616,
#: pro/fields/class-acf-field-repeater.php:177
msgid "Layout"
msgstr "Mise en page"

#: pro/fields/class-acf-field-clone.php:855
msgid "Specify the style used to render the selected fields"
msgstr "Style utilisé pour générer les champs sélectionnés"

#: pro/fields/class-acf-field-clone.php:860,
#: pro/fields/class-acf-field-flexible-content.php:629,
#: pro/fields/class-acf-field-repeater.php:185,
#: pro/locations/class-acf-location-block.php:22
msgid "Block"
msgstr "Bloc"

#: pro/fields/class-acf-field-clone.php:861,
#: pro/fields/class-acf-field-flexible-content.php:628,
#: pro/fields/class-acf-field-repeater.php:184
msgid "Table"
msgstr "Tableau"

#: pro/fields/class-acf-field-clone.php:862,
#: pro/fields/class-acf-field-flexible-content.php:630,
#: pro/fields/class-acf-field-repeater.php:186
msgid "Row"
msgstr "Rangée"

#: pro/fields/class-acf-field-clone.php:868
msgid "Labels will be displayed as %s"
msgstr "Les labels seront affichés en tant que %s"

#: pro/fields/class-acf-field-clone.php:873
msgid "Prefix Field Labels"
msgstr "Préfixer les labels de champs"

#: pro/fields/class-acf-field-clone.php:883
msgid "Values will be saved as %s"
msgstr "Les valeurs seront enregistrées en tant que %s"

#: pro/fields/class-acf-field-clone.php:888
msgid "Prefix Field Names"
msgstr "Préfixer les noms de champs"

#: pro/fields/class-acf-field-clone.php:1005
msgid "Unknown field"
msgstr "Champ inconnu"

# @ acf
#: pro/fields/class-acf-field-clone.php:1009
msgid "(no title)"
msgstr "(sans titre)"

#: pro/fields/class-acf-field-clone.php:1042
msgid "Unknown field group"
msgstr "Groupe de champ inconnu"

#: pro/fields/class-acf-field-clone.php:1046
msgid "All fields from %s field group"
msgstr "Tous les champs du groupe %s"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:25
msgid "Flexible Content"
msgstr "Contenu flexible"

#: pro/fields/class-acf-field-flexible-content.php:27
msgid ""
"Allows you to define, create and manage content with total control by "
"creating layouts that contain subfields that content editors can choose from."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:27
msgid "We do not recommend using this field in ACF Blocks."
msgstr ""

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:36,
#: pro/fields/class-acf-field-repeater.php:103,
#: pro/fields/class-acf-field-repeater.php:297
msgid "Add Row"
msgstr "Ajouter un élément"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:76,
#: pro/fields/class-acf-field-flexible-content.php:943,
#: pro/fields/class-acf-field-flexible-content.php:1022
msgid "layout"
msgid_plural "layouts"
msgstr[0] "mise-en-forme"
msgstr[1] "mises-en-forme"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:77
msgid "layouts"
msgstr "mises-en-forme"

#: pro/fields/class-acf-field-flexible-content.php:81,
#: pro/fields/class-acf-field-flexible-content.php:942,
#: pro/fields/class-acf-field-flexible-content.php:1021
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Ce champ requiert au moins {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:82
msgid "This field has a limit of {max} {label} {identifier}"
msgstr "Ce champ a une limite de {max} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:85
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} disponible (max {max})"

#: pro/fields/class-acf-field-flexible-content.php:86
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} requis (min {min})"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:89
msgid "Flexible Content requires at least 1 layout"
msgstr "Le contenu flexible nécessite au moins une mise-en-forme"

#: pro/fields/class-acf-field-flexible-content.php:282
msgid "Click the \"%s\" button below to start creating your layout"
msgstr ""
"Cliquez sur le bouton « %s » ci-dessous pour créer votre première mise-en-"
"forme"

#: pro/fields/class-acf-field-flexible-content.php:420,
#: pro/fields/class-acf-repeater-table.php:366
msgid "Drag to reorder"
msgstr "Faites glisser pour réorganiser"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:423
msgid "Add layout"
msgstr "Ajouter une mise-en-forme"

#: pro/fields/class-acf-field-flexible-content.php:424
#, fuzzy
#| msgid "Duplicate Layout"
msgid "Duplicate layout"
msgstr "Dupliquer la mise-en-forme"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:425
msgid "Remove layout"
msgstr "Retirer la mise-en-forme"

#: pro/fields/class-acf-field-flexible-content.php:426,
#: pro/fields/class-acf-repeater-table.php:382
msgid "Click to toggle"
msgstr "Cliquer pour intervertir"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:562
msgid "Delete Layout"
msgstr "Supprimer la mise-en-forme"

#: pro/fields/class-acf-field-flexible-content.php:563
msgid "Duplicate Layout"
msgstr "Dupliquer la mise-en-forme"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:564
msgid "Add New Layout"
msgstr "Ajouter une nouvelle mise-en-forme"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:564
#, fuzzy
#| msgid "Add layout"
msgid "Add Layout"
msgstr "Ajouter une mise-en-forme"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:593
msgid "Label"
msgstr "Intitulé"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:609
msgid "Name"
msgstr "Nom"

#: pro/fields/class-acf-field-flexible-content.php:647
msgid "Min"
msgstr "Min"

#: pro/fields/class-acf-field-flexible-content.php:662
msgid "Max"
msgstr "Max"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:705
msgid "Minimum Layouts"
msgstr "Nombre minimum de mises-en-forme"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:716
msgid "Maximum Layouts"
msgstr "Nombre maximum de mises-en-forme"

#: pro/fields/class-acf-field-flexible-content.php:727,
#: pro/fields/class-acf-field-repeater.php:293
msgid "Button Label"
msgstr "Intitulé du bouton"

#: pro/fields/class-acf-field-flexible-content.php:1710,
#: pro/fields/class-acf-field-repeater.php:918
msgid "%s must be of type array or null."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:1721
msgid "%1$s must contain at least %2$s %3$s layout."
msgid_plural "%1$s must contain at least %2$s %3$s layouts."
msgstr[0] ""
msgstr[1] ""

#: pro/fields/class-acf-field-flexible-content.php:1737
msgid "%1$s must contain at most %2$s %3$s layout."
msgid_plural "%1$s must contain at most %2$s %3$s layouts."
msgstr[0] ""
msgstr[1] ""

# @ acf
#: pro/fields/class-acf-field-gallery.php:25
msgid "Gallery"
msgstr "Galerie"

#: pro/fields/class-acf-field-gallery.php:27
msgid ""
"An interactive interface for managing a collection of attachments, such as "
"images."
msgstr ""

# @ acf
#: pro/fields/class-acf-field-gallery.php:77
msgid "Add Image to Gallery"
msgstr "Ajouter l’image à la galerie"

#: pro/fields/class-acf-field-gallery.php:78
msgid "Maximum selection reached"
msgstr "Nombre de sélections maximales atteint"

#: pro/fields/class-acf-field-gallery.php:324
msgid "Length"
msgstr "Longueur"

# @ acf
#: pro/fields/class-acf-field-gallery.php:339
msgid "Edit"
msgstr "Modifier"

# @ acf
#: pro/fields/class-acf-field-gallery.php:340,
#: pro/fields/class-acf-field-gallery.php:495
msgid "Remove"
msgstr "Enlever"

#: pro/fields/class-acf-field-gallery.php:356
msgid "Title"
msgstr "Titre"

#: pro/fields/class-acf-field-gallery.php:368
msgid "Caption"
msgstr "Légende"

#: pro/fields/class-acf-field-gallery.php:380
msgid "Alt Text"
msgstr "Texte alternatif"

# @ acf
#: pro/fields/class-acf-field-gallery.php:392
msgid "Description"
msgstr "Description"

#: pro/fields/class-acf-field-gallery.php:504
msgid "Add to gallery"
msgstr "Ajouter à la galerie"

# @ acf
#: pro/fields/class-acf-field-gallery.php:508
msgid "Bulk actions"
msgstr "Actions de groupe"

#: pro/fields/class-acf-field-gallery.php:509
msgid "Sort by date uploaded"
msgstr "Ordonner par date d’import"

#: pro/fields/class-acf-field-gallery.php:510
msgid "Sort by date modified"
msgstr "Ranger par date de modification"

# @ acf
#: pro/fields/class-acf-field-gallery.php:511
msgid "Sort by title"
msgstr "Ranger par titre"

#: pro/fields/class-acf-field-gallery.php:512
msgid "Reverse current order"
msgstr "Inverser l’ordre actuel"

# @ acf
#: pro/fields/class-acf-field-gallery.php:524
msgid "Close"
msgstr "Fermer"

# @ acf
#: pro/fields/class-acf-field-gallery.php:556
msgid "Return Format"
msgstr "Format de la valeur retournée"

# @ acf
#: pro/fields/class-acf-field-gallery.php:562
msgid "Image Array"
msgstr "Données de l’image (tableau/Array)"

# @ acf
#: pro/fields/class-acf-field-gallery.php:563
msgid "Image URL"
msgstr "URL de l‘image"

# @ acf
#: pro/fields/class-acf-field-gallery.php:564
msgid "Image ID"
msgstr "ID de l‘image"

#: pro/fields/class-acf-field-gallery.php:572
msgid "Library"
msgstr "Média"

#: pro/fields/class-acf-field-gallery.php:573
msgid "Limit the media library choice"
msgstr "Limiter le choix dans la médiathèque"

#: pro/fields/class-acf-field-gallery.php:578,
#: pro/locations/class-acf-location-block.php:66
msgid "All"
msgstr "Tous"

#: pro/fields/class-acf-field-gallery.php:579
msgid "Uploaded to post"
msgstr "Liés à cet article"

# @ acf
#: pro/fields/class-acf-field-gallery.php:615
msgid "Minimum Selection"
msgstr "Nombre minimum"

# @ acf
#: pro/fields/class-acf-field-gallery.php:625
msgid "Maximum Selection"
msgstr "Nombre maximum"

# @ acf
#: pro/fields/class-acf-field-gallery.php:635
msgid "Minimum"
msgstr "Minimum"

#: pro/fields/class-acf-field-gallery.php:636,
#: pro/fields/class-acf-field-gallery.php:672
msgid "Restrict which images can be uploaded"
msgstr "Restreindre les images envoyées"

#: pro/fields/class-acf-field-gallery.php:639,
#: pro/fields/class-acf-field-gallery.php:675
msgid "Width"
msgstr "Largeur"

#: pro/fields/class-acf-field-gallery.php:650,
#: pro/fields/class-acf-field-gallery.php:686
msgid "Height"
msgstr "Hauteur"

# @ acf
#: pro/fields/class-acf-field-gallery.php:662,
#: pro/fields/class-acf-field-gallery.php:698
msgid "File size"
msgstr "Taille du fichier"

# @ acf
#: pro/fields/class-acf-field-gallery.php:671
msgid "Maximum"
msgstr "Maximum"

#: pro/fields/class-acf-field-gallery.php:707
msgid "Allowed file types"
msgstr "Types de fichiers autorisés"

#: pro/fields/class-acf-field-gallery.php:708
msgid "Comma separated list. Leave blank for all types"
msgstr ""
"Extensions autorisées séparées par une virgule. Laissez vide pour autoriser "
"toutes les extensions"

#: pro/fields/class-acf-field-gallery.php:727
msgid "Insert"
msgstr "Insérer"

#: pro/fields/class-acf-field-gallery.php:728
msgid "Specify where new attachments are added"
msgstr "Définir où les nouveaux fichiers attachés sont ajoutés"

#: pro/fields/class-acf-field-gallery.php:732
msgid "Append to the end"
msgstr "Ajouter à la fin"

#: pro/fields/class-acf-field-gallery.php:733
msgid "Prepend to the beginning"
msgstr "Insérer au début"

# @ acf
#: pro/fields/class-acf-field-gallery.php:741
msgid "Preview Size"
msgstr "Taille de prévisualisation"

#: pro/fields/class-acf-field-gallery.php:844
#, fuzzy
#| msgid "%s requires at least %s selection"
#| msgid_plural "%s requires at least %s selections"
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] "%s requiert au moins %s sélection"
msgstr[1] "%s requiert au moins %s sélections"

# @ acf
#: pro/fields/class-acf-field-repeater.php:29
msgid "Repeater"
msgstr "Répéteur"

#: pro/fields/class-acf-field-repeater.php:66,
#: pro/fields/class-acf-field-repeater.php:463
#, fuzzy
#| msgid "Minimum rows reached ({min} rows)"
msgid "Minimum rows not reached ({min} rows)"
msgstr "Nombre minimum d’éléments atteint ({min} éléments)"

#: pro/fields/class-acf-field-repeater.php:67
msgid "Maximum rows reached ({max} rows)"
msgstr "Nombre maximum d’éléments atteint ({max} éléments)"

#: pro/fields/class-acf-field-repeater.php:68
#, fuzzy
#| msgid "Error loading field."
msgid "Error loading page"
msgstr "Échec du chargement du champ."

#: pro/fields/class-acf-field-repeater.php:69
msgid "Order will be assigned upon save"
msgstr ""

# @ acf
#: pro/fields/class-acf-field-repeater.php:162
msgid "Sub Fields"
msgstr "Sous-champs"

# @ acf
#: pro/fields/class-acf-field-repeater.php:195
#, fuzzy
#| msgid "Position"
msgid "Pagination"
msgstr "Position"

#: pro/fields/class-acf-field-repeater.php:196
msgid "Useful for fields with a large number of rows."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:207
#, fuzzy
#| msgid "Posts Page"
msgid "Rows Per Page"
msgstr "Page des articles"

# @ acf
#: pro/fields/class-acf-field-repeater.php:208
#, fuzzy
#| msgid "Select the taxonomy to be displayed"
msgid "Set the number of rows to be displayed on a page."
msgstr "Choisissez la taxonomie à afficher"

# @ acf
#: pro/fields/class-acf-field-repeater.php:240
msgid "Minimum Rows"
msgstr "Nombre minimum d’éléments"

# @ acf
#: pro/fields/class-acf-field-repeater.php:251
msgid "Maximum Rows"
msgstr "Nombre maximum d’éléments"

#: pro/fields/class-acf-field-repeater.php:281
msgid "Collapsed"
msgstr "Replié"

#: pro/fields/class-acf-field-repeater.php:282
msgid "Select a sub field to show when row is collapsed"
msgstr "Choisir un sous champ à afficher lorsque l’élément est replié"

#: pro/fields/class-acf-field-repeater.php:1045
msgid "Invalid nonce."
msgstr "Nonce invalide."

#: pro/fields/class-acf-field-repeater.php:1060
#, fuzzy
#| msgid "Invalid nonce."
msgid "Invalid field key or name."
msgstr "Nonce invalide."

#: pro/fields/class-acf-field-repeater.php:1069
msgid "There was an error retrieving the field."
msgstr ""

#: pro/fields/class-acf-repeater-table.php:369
#, fuzzy
#| msgid "Drag to reorder"
msgid "Click to reorder"
msgstr "Faites glisser pour réorganiser"

# @ acf
#: pro/fields/class-acf-repeater-table.php:402
msgid "Add row"
msgstr "Ajouter un élément"

#: pro/fields/class-acf-repeater-table.php:403
#, fuzzy
#| msgid "Duplicate"
msgid "Duplicate row"
msgstr "Dupliquer"

# @ acf
#: pro/fields/class-acf-repeater-table.php:404
msgid "Remove row"
msgstr "Retirer l’élément"

#: pro/fields/class-acf-repeater-table.php:448,
#: pro/fields/class-acf-repeater-table.php:465,
#: pro/fields/class-acf-repeater-table.php:466
#, fuzzy
#| msgid "Current User"
msgid "Current Page"
msgstr "Utilisateur courant"

#: pro/fields/class-acf-repeater-table.php:456,
#: pro/fields/class-acf-repeater-table.php:457
#, fuzzy
#| msgid "Front Page"
msgid "First Page"
msgstr "Page d’accueil"

#: pro/fields/class-acf-repeater-table.php:460,
#: pro/fields/class-acf-repeater-table.php:461
#, fuzzy
#| msgid "Posts Page"
msgid "Previous Page"
msgstr "Page des articles"

#. translators: 1: Current page, 2: Total pages.
#: pro/fields/class-acf-repeater-table.php:470
msgctxt "paging"
msgid "%1$s of %2$s"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:477,
#: pro/fields/class-acf-repeater-table.php:478
#, fuzzy
#| msgid "Front Page"
msgid "Next Page"
msgstr "Page d’accueil"

#: pro/fields/class-acf-repeater-table.php:481,
#: pro/fields/class-acf-repeater-table.php:482
#, fuzzy
#| msgid "Posts Page"
msgid "Last Page"
msgstr "Page des articles"

#: pro/locations/class-acf-location-block.php:71
#, fuzzy
#| msgid "No options pages exist"
msgid "No block types exist"
msgstr "Aucune page d’option n’existe"

# @ acf
#: pro/locations/class-acf-location-options-page.php:22
msgid "Options Page"
msgstr "Page d‘options"

#: pro/locations/class-acf-location-options-page.php:70
msgid "No options pages exist"
msgstr "Aucune page d’option n’existe"

#: pro/admin/views/html-settings-updates.php:6
msgid "Deactivate License"
msgstr "Désactiver la licence"

# @ acf
#: pro/admin/views/html-settings-updates.php:6
msgid "Activate License"
msgstr "Activer votre licence"

# @ acf
#: pro/admin/views/html-settings-updates.php:16
msgid "License Information"
msgstr "Informations sur la licence"

#: pro/admin/views/html-settings-updates.php:34
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"Pour débloquer les mises à jour, veuillez entrer votre clé de licence ci-"
"dessous. Si vous n’en avez pas, rendez-vous sur nos <a href=\"%s\" "
"target=\"_blank\">détails & tarifs</a>."

# @ acf
#: pro/admin/views/html-settings-updates.php:37
msgid "License Key"
msgstr "Code de licence"

#: pro/admin/views/html-settings-updates.php:22
msgid "Your license key is defined in wp-config.php."
msgstr ""

#: pro/admin/views/html-settings-updates.php:29
#, fuzzy
#| msgid "Better Validation"
msgid "Retry Activation"
msgstr "Meilleure validation"

# @ acf
#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr "Informations concernant les mises à jour"

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr "Version installée"

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr "Version disponible"

# @ acf
#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr "Mise à jour disponible"

#: pro/admin/views/html-settings-updates.php:91
msgid "No"
msgstr "Non"

#: pro/admin/views/html-settings-updates.php:89
msgid "Yes"
msgstr "Oui"

# @ wp3i
#: pro/admin/views/html-settings-updates.php:98
msgid "Upgrade Notice"
msgstr "Informations de mise à niveau"

#: pro/admin/views/html-settings-updates.php:126
msgid "Check For Updates"
msgstr ""

#: pro/admin/views/html-settings-updates.php:121
#, fuzzy
#| msgid "Please enter your license key above to unlock updates"
msgid "Enter your license key to unlock updates"
msgstr "Entrez votre clé de licence ci-dessus pour activer les mises à jour"

# @ acf
#: pro/admin/views/html-settings-updates.php:119
msgid "Update Plugin"
msgstr "Mettre à jour l’extension"

#: pro/admin/views/html-settings-updates.php:117
#, fuzzy
#| msgid "Please enter your license key above to unlock updates"
msgid "Please reactivate your license to unlock updates"
msgstr "Entrez votre clé de licence ci-dessus pour activer les mises à jour"
