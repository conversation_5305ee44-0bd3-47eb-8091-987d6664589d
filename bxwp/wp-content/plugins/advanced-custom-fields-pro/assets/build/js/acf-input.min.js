(()=>{var e={774:()=>{var e,t;e=jQuery,t=acf.Field.extend({type:"checkbox",events:{"change input":"onChange","click .acf-add-checkbox":"onClickAdd","click .acf-checkbox-toggle":"onClickToggle","click .acf-checkbox-custom":"onClickCustom"},$control:function(){return this.$(".acf-checkbox-list")},$toggle:function(){return this.$(".acf-checkbox-toggle")},$input:function(){return this.$('input[type="hidden"]')},$inputs:function(){return this.$('input[type="checkbox"]').not(".acf-checkbox-toggle")},getValue:function(){var t=[];return this.$(":checked").each(function(){t.push(e(this).val())}),!!t.length&&t},onChange:function(e,t){var i=t.prop("checked"),a=t.parent("label"),n=this.$toggle();i?a.addClass("selected"):a.removeClass("selected"),n.length&&(0==this.$inputs().not(":checked").length?n.prop("checked",!0):n.prop("checked",!1))},onClickAdd:function(e,t){var i='<li><input class="acf-checkbox-custom" type="checkbox" checked="checked" /><input type="text" name="'+this.getInputName()+'[]" /></li>';t.parent("li").before(i),t.parent("li").parent().find('input[type="text"]').last().focus()},onClickToggle:function(e,t){var i=t.prop("checked"),a=this.$('input[type="checkbox"]'),n=this.$("label");a.prop("checked",i),i?n.addClass("selected"):n.removeClass("selected")},onClickCustom:function(e,t){var i=t.prop("checked"),a=t.next('input[type="text"]');i?a.prop("disabled",!1):(a.prop("disabled",!0),""==a.val()&&t.parent("li").remove())}}),acf.registerFieldType(t)},960:()=>{var e,t;e=jQuery,t=acf.models.DatePickerField.extend({type:"date_time_picker",$control:function(){return this.$(".acf-date-time-picker")},initialize:function(){var t=this.$input(),i=this.$inputText(),a={dateFormat:this.get("date_format"),timeFormat:this.get("time_format"),altField:t,altFieldTimeOnly:!1,altFormat:"yy-mm-dd",altTimeFormat:"HH:mm:ss",changeYear:!0,yearRange:"-100:+100",changeMonth:!0,showButtonPanel:!0,firstDay:this.get("first_day"),controlType:"select",oneLine:!0};if(a=acf.applyFilters("date_time_picker_args",a,this),acf.newDateTimePicker(i,a),1===i.data("default-to-today")&&!t.val()){const n=new Date,s=e.datepicker.formatDate(a.dateFormat,n),o=e.datepicker.formatTime(a.timeFormat,{hour:n.getHours(),minute:n.getMinutes(),second:n.getSeconds()});i.val(`${s} ${o}`);const r=e.datepicker.formatDate("yy-mm-dd",n),c=e.datepicker.formatTime("hh:mm:ss",{hour:n.getHours(),minute:n.getMinutes(),second:n.getSeconds()});t.val(`${r} ${c}`)}acf.doAction("date_time_picker_init",i,a,this)}}),acf.registerFieldType(t),new acf.Model({priority:5,wait:"ready",initialize:function(){var t=acf.get("locale"),i=acf.get("rtl"),a=acf.get("dateTimePickerL10n");return!!a&&void 0!==e.timepicker&&(a.isRTL=i,e.timepicker.regional[t]=a,void e.timepicker.setDefaults(a))}}),acf.newDateTimePicker=function(t,i){if(void 0===e.timepicker)return!1;i=i||{},t.datetimepicker(i),e("body > #ui-datepicker-div").exists()&&e("body > #ui-datepicker-div").wrap('<div class="acf-ui-datepicker" />')}},963:()=>{var e;e=jQuery,acf.unload=new acf.Model({wait:"load",active:!0,changed:!1,actions:{validation_failure:"startListening",validation_success:"stopListening"},events:{"change form .acf-field":"startListening","submit form":"stopListening"},enable:function(){this.active=!0},disable:function(){this.active=!1},reset:function(){this.stopListening()},startListening:function(){!this.changed&&this.active&&(this.changed=!0,e(window).on("beforeunload",this.onUnload))},stopListening:function(){this.changed=!1,e(window).off("beforeunload",this.onUnload)},onUnload:function(){return acf.__("The changes you made will be lost if you navigate away from this page")}})},993:()=>{var e;e=jQuery,new acf.Model({wait:"prepare",priority:1,initialize:function(){(acf.get("postboxes")||[]).map(acf.newPostbox)}}),acf.getPostbox=function(t){return"string"==typeof arguments[0]&&(t=e("#"+arguments[0])),acf.getInstance(t)},acf.getPostboxes=function(){return acf.getInstances(e(".acf-postbox"))},acf.newPostbox=function(e){return new acf.models.Postbox(e)},acf.models.Postbox=acf.Model.extend({data:{id:"",key:"",style:"default",label:"top",edit:""},setup:function(t){t.editLink&&(t.edit=t.editLink),e.extend(this.data,t),this.$el=this.$postbox()},$postbox:function(){return e("#"+this.get("id"))},$hide:function(){return e("#"+this.get("id")+"-hide")},$hideLabel:function(){return this.$hide().parent()},$hndle:function(){return this.$("> .hndle")},$handleActions:function(){return this.$("> .postbox-header .handle-actions")},$inside:function(){return this.$("> .inside")},isVisible:function(){return this.$el.hasClass("acf-hidden")},isHiddenByScreenOptions:function(){return this.$el.hasClass("hide-if-js")||"none"==this.$el.css("display")},initialize:function(){if(this.$el.addClass("acf-postbox"),"block"!==acf.get("editor")){var e=this.get("style");"default"!==e&&this.$el.addClass(e)}this.$inside().addClass("acf-fields").addClass("-"+this.get("label"));var t=this.get("edit");if(t){var i='<a href="'+t+'" class="dashicons dashicons-admin-generic acf-hndle-cog acf-js-tooltip" title="'+acf.__("Edit field group")+'"></a>',a=this.$handleActions();a.length?a.prepend(i):this.$hndle().append(i)}this.show()},show:function(){this.$el.hasClass("hide-if-js")?this.$hide().prop("checked",!1):(this.$hideLabel().show(),this.$hide().prop("checked",!0),this.$el.show().removeClass("acf-hidden"),acf.doAction("show_postbox",this))},enable:function(){acf.enable(this.$el,"postbox")},showEnable:function(){this.enable(),this.show()},hide:function(){this.$hideLabel().hide(),this.$el.hide().addClass("acf-hidden"),acf.doAction("hide_postbox",this)},disable:function(){acf.disable(this.$el,"postbox")},hideDisable:function(){this.disable(),this.hide()},html:function(e){this.$inside().html(e),acf.doAction("append",this.$el)}})},1087:()=>{var e;e=jQuery,acf.tinymce={defaults:function(){return"undefined"!=typeof tinyMCEPreInit&&{tinymce:tinyMCEPreInit.mceInit.acf_content,quicktags:tinyMCEPreInit.qtInit.acf_content}},initialize:function(e,t){(t=acf.parseArgs(t,{tinymce:!0,quicktags:!0,toolbar:"full",mode:"visual",field:!1})).tinymce&&this.initializeTinymce(e,t),t.quicktags&&this.initializeQuicktags(e,t)},initializeTinymce:function(t,i){var a=e("#"+t),n=this.defaults(),s=acf.get("toolbars"),o=i.field||!1;if(o.$el,"undefined"==typeof tinymce)return!1;if(!n)return!1;if(tinymce.get(t))return this.enable(t);var r=e.extend({},n.tinymce,i.tinymce);r.id=t,r.selector="#"+t;var c=i.toolbar;if(c&&s&&s[c])for(var l=1;l<=4;l++)r["toolbar"+l]=s[c][l]||"";if(r.setup=function(e){e.on("change",function(t){e.save(),a.trigger("change")}),e.on("mouseup",function(e){var t=new MouseEvent("mouseup");window.dispatchEvent(t)})},r.wp_autoresize_on=!1,r.tadv_noautop||(r.wpautop=!0),r=acf.applyFilters("wysiwyg_tinymce_settings",r,t,o),tinyMCEPreInit.mceInit[t]=r,"visual"==i.mode){tinymce.init(r);var d=tinymce.get(t);if(!d)return!1;d.acf=i.field,acf.doAction("wysiwyg_tinymce_init",d,d.id,r,o)}},initializeQuicktags:function(t,i){var a=this.defaults();if("undefined"==typeof quicktags)return!1;if(!a)return!1;var n=e.extend({},a.quicktags,i.quicktags);n.id=t;var s=i.field||!1;s.$el,n=acf.applyFilters("wysiwyg_quicktags_settings",n,n.id,s),tinyMCEPreInit.qtInit[t]=n;var o=quicktags(n);if(!o)return!1;this.buildQuicktags(o),acf.doAction("wysiwyg_quicktags_init",o,o.id,n,s)},buildQuicktags:function(e){var t,i,a,n,s,o,r,c;for(o in e.canvas,t=e.name,i=e.settings,n="",a={},r="",c=e.id,i.buttons&&(r=","+i.buttons+","),edButtons)edButtons[o]&&(s=edButtons[o].id,r&&-1!==",strong,em,link,block,del,ins,img,ul,ol,li,code,more,close,".indexOf(","+s+",")&&-1===r.indexOf(","+s+",")||edButtons[o].instance&&edButtons[o].instance!==c||(a[s]=edButtons[o],edButtons[o].html&&(n+=edButtons[o].html(t+"_"))));r&&-1!==r.indexOf(",dfw,")&&(a.dfw=new QTags.DFWButton,n+=a.dfw.html(t+"_")),"rtl"===document.getElementsByTagName("html")[0].dir&&(a.textdirection=new QTags.TextDirectionButton,n+=a.textdirection.html(t+"_")),e.toolbar.innerHTML=n,e.theButtons=a,"undefined"!=typeof jQuery&&jQuery(document).triggerHandler("quicktags-init",[e])},disable:function(e){this.destroyTinymce(e)},remove:function(e){this.destroyTinymce(e)},destroy:function(e){this.destroyTinymce(e)},destroyTinymce:function(e){if("undefined"==typeof tinymce)return!1;var t=tinymce.get(e);return!!t&&(t.save(),t.destroy(),!0)},enable:function(e){this.enableTinymce(e)},enableTinymce:function(t){return"undefined"!=typeof switchEditors&&void 0!==tinyMCEPreInit.mceInit[t]&&(e("#"+t).show(),switchEditors.go(t,"tmce"),!0)}},new acf.Model({priority:5,actions:{prepare:"onPrepare",ready:"onReady"},onPrepare:function(){var t=e("#acf-hidden-wp-editor");t.exists()&&t.appendTo("body")},onReady:function(){acf.isset(window,"wp","oldEditor")&&(wp.editor.autop=wp.oldEditor.autop,wp.editor.removep=wp.oldEditor.removep),acf.isset(window,"tinymce","on")&&tinymce.on("AddEditor",function(e){var t=e.editor;"acf"===t.id.substr(0,3)&&(t=tinymce.editors.content||t,tinymce.activeEditor=t,wpActiveEditor=t.id)})}})},1163:()=>{!function(e){var t=acf.Field.extend({type:"google_map",map:!1,wait:"load",events:{'click a[data-name="clear"]':"onClickClear",'click a[data-name="locate"]':"onClickLocate",'click a[data-name="search"]':"onClickSearch","keydown .search":"onKeydownSearch","keyup .search":"onKeyupSearch","focus .search":"onFocusSearch","blur .search":"onBlurSearch",showField:"onShow"},$control:function(){return this.$(".acf-google-map")},$search:function(){return this.$(".search")},$canvas:function(){return this.$(".canvas")},setState:function(e){this.$control().removeClass("-value -loading -searching"),"default"===e&&(e=this.val()?"value":""),e&&this.$control().addClass("-"+e)},getValue:function(){var e=this.$input().val();return!!e&&JSON.parse(e)},setValue:function(e,t){var i="";e&&(i=JSON.stringify(e)),acf.val(this.$input(),i),t||(this.renderVal(e),acf.doAction("google_map_change",e,this.map,this))},renderVal:function(e){e?(this.setState("value"),this.$search().val(e.address),this.setPosition(e.lat,e.lng)):(this.setState(""),this.$search().val(""),this.map.marker.setVisible(!1))},newLatLng:function(e,t){return new google.maps.LatLng(parseFloat(e),parseFloat(t))},setPosition:function(e,t){this.map.marker.setPosition({lat:parseFloat(e),lng:parseFloat(t)}),this.map.marker.setVisible(!0),this.center()},center:function(){var e=this.map.marker.getPosition();if(e)var t=e.lat(),i=e.lng();else t=this.get("lat"),i=this.get("lng");this.map.setCenter({lat:parseFloat(t),lng:parseFloat(i)})},initialize:function(){!function(t){if(a)return t();if(acf.isset(window,"google","maps","Geocoder"))return a=new google.maps.Geocoder,t();if(acf.addAction("google_map_api_loaded",t),!i){var n=acf.get("google_map_api");n&&(i=!0,e.ajax({url:n,dataType:"script",cache:!0,success:function(){a=new google.maps.Geocoder,acf.doAction("google_map_api_loaded")}}))}}(this.initializeMap.bind(this))},initializeMap:function(){var e=this.getValue(),t=acf.parseArgs(e,{zoom:this.get("zoom"),lat:this.get("lat"),lng:this.get("lng")}),i={scrollwheel:!1,zoom:parseInt(t.zoom),center:{lat:parseFloat(t.lat),lng:parseFloat(t.lng)},mapTypeId:google.maps.MapTypeId.ROADMAP,marker:{draggable:!0,raiseOnDrag:!0},autocomplete:{}};i=acf.applyFilters("google_map_args",i,this);var a=new google.maps.Map(this.$canvas()[0],i),n=acf.parseArgs(i.marker,{draggable:!0,raiseOnDrag:!0,map:a});n=acf.applyFilters("google_map_marker_args",n,this);var s=new google.maps.Marker(n),o=!1;if(acf.isset(google,"maps","places","Autocomplete")){var r=i.autocomplete||{};r=acf.applyFilters("google_map_autocomplete_args",r,this),(o=new google.maps.places.Autocomplete(this.$search()[0],r)).bindTo("bounds",a)}this.addMapEvents(this,a,s,o),a.acf=this,a.marker=s,a.autocomplete=o,this.map=a,e&&this.setPosition(e.lat,e.lng),acf.doAction("google_map_init",a,s,this)},addMapEvents:function(e,t,i,a){google.maps.event.addListener(t,"click",function(t){var i=t.latLng.lat(),a=t.latLng.lng();e.searchPosition(i,a)}),google.maps.event.addListener(i,"dragend",function(){var t=this.getPosition().lat(),i=this.getPosition().lng();e.searchPosition(t,i)}),a&&google.maps.event.addListener(a,"place_changed",function(){var t=this.getPlace();e.searchPlace(t)}),google.maps.event.addListener(t,"zoom_changed",function(){var i=e.val();i&&(i.zoom=t.getZoom(),e.setValue(i,!0))})},searchPosition:function(e,t){this.setState("loading");var i={lat:e,lng:t};a.geocode({location:i},function(i,a){if(this.setState(""),"OK"!==a)this.showNotice({text:acf.__("Location not found: %s").replace("%s",a),type:"warning"});else{var n=this.parseResult(i[0]);n.lat=e,n.lng=t,this.val(n)}}.bind(this))},searchPlace:function(e){if(e)if(e.geometry){e.formatted_address=this.$search().val();var t=this.parseResult(e);this.val(t)}else e.name&&this.searchAddress(e.name)},searchAddress:function(e){if(e){var t=e.split(",");if(2==t.length){var i=parseFloat(t[0]),n=parseFloat(t[1]);if(i&&n)return this.searchPosition(i,n)}this.setState("loading"),a.geocode({address:e},function(t,i){if(this.setState(""),"OK"!==i)this.showNotice({text:acf.__("Location not found: %s").replace("%s",i),type:"warning"});else{var a=this.parseResult(t[0]);a.address=e,this.val(a)}}.bind(this))}},searchLocation:function(){if(!navigator.geolocation)return alert(acf.__("Sorry, this browser does not support geolocation"));this.setState("loading"),navigator.geolocation.getCurrentPosition(function(e){this.setState("");var t=e.coords.latitude,i=e.coords.longitude;this.searchPosition(t,i)}.bind(this),function(e){this.setState("")}.bind(this))},parseResult:function(e){var t={address:e.formatted_address,lat:e.geometry.location.lat(),lng:e.geometry.location.lng()};t.zoom=this.map.getZoom(),e.place_id&&(t.place_id=e.place_id),e.name&&(t.name=e.name);var i={street_number:["street_number"],street_name:["street_address","route"],city:["locality","postal_town"],state:["administrative_area_level_1","administrative_area_level_2","administrative_area_level_3","administrative_area_level_4","administrative_area_level_5"],post_code:["postal_code"],country:["country"]};for(var a in i)for(var n=i[a],s=0;s<e.address_components.length;s++){var o=e.address_components[s],r=o.types[0];-1!==n.indexOf(r)&&(t[a]=o.long_name,o.long_name!==o.short_name&&(t[a+"_short"]=o.short_name))}return acf.applyFilters("google_map_result",t,e,this.map,this)},onClickClear:function(){this.val(!1)},onClickLocate:function(){this.searchLocation()},onClickSearch:function(){this.searchAddress(this.$search().val())},onFocusSearch:function(e,t){this.setState("searching")},onBlurSearch:function(e,t){var i=this.val(),a=i?i.address:"";t.val()===a&&this.setState("default")},onKeyupSearch:function(e,t){t.val()||this.val(!1)},onKeydownSearch:function(e,t){13==e.which&&(e.preventDefault(),t.blur())},onShow:function(){this.map&&this.setTimeout(this.center)}});acf.registerFieldType(t);var i=!1,a=!1}(jQuery)},1218:()=>{!function(e){acf.newMediaPopup=function(e){var t=null;return e=acf.parseArgs(e,{mode:"select",title:"",button:"",type:"",field:!1,allowedTypes:"",library:"all",multiple:!1,attachment:0,autoOpen:!0,open:function(){},select:function(){},close:function(){}}),t="edit"==e.mode?new acf.models.EditMediaPopup(e):new acf.models.SelectMediaPopup(e),e.autoOpen&&setTimeout(function(){t.open()},1),acf.doAction("new_media_popup",t),t};var t=function(){var e=acf.get("post_id");return acf.isNumeric(e)?e:0};acf.getMimeTypes=function(){return this.get("mimeTypes")},acf.getMimeType=function(e){var t=acf.getMimeTypes();if(void 0!==t[e])return t[e];for(var i in t)if(-1!==i.indexOf(e))return t[i];return!1};var i=acf.Model.extend({id:"MediaPopup",data:{},defaults:{},frame:!1,setup:function(t){e.extend(this.data,t)},initialize:function(){var e=this.getFrameOptions();this.addFrameStates(e);var t=wp.media(e);t.acf=this,this.addFrameEvents(t,e),this.frame=t},open:function(){this.frame.open()},close:function(){this.frame.close()},remove:function(){this.frame.detach(),this.frame.remove()},getFrameOptions:function(){var e={title:this.get("title"),multiple:this.get("multiple"),library:{},states:[]};return this.get("type")&&(e.library.type=this.get("type")),"uploadedTo"===this.get("library")&&(e.library.uploadedTo=t()),this.get("attachment")&&(e.library.post__in=[this.get("attachment")]),this.get("button")&&(e.button={text:this.get("button")}),e},addFrameStates:function(e){var t=wp.media.query(e.library);this.get("field")&&acf.isset(t,"mirroring","args")&&(t.mirroring.args._acfuploader=this.get("field")),e.states.push(new wp.media.controller.Library({library:t,multiple:this.get("multiple"),title:this.get("title"),priority:20,filterable:"all",editable:!0,allowLocalEdits:!0})),acf.isset(wp,"media","controller","EditImage")&&e.states.push(new wp.media.controller.EditImage)},addFrameEvents:function(e,t){e.on("open",function(){this.$el.closest(".media-modal").addClass("acf-media-modal -"+this.acf.get("mode"))},e),e.on("content:render:edit-image",function(){var e=this.state().get("image"),t=new wp.media.view.EditImage({model:e,controller:this}).render();this.content.set(t),t.loadEditor()},e),e.on("select",function(){var t=e.state().get("selection");t&&t.each(function(t,i){e.acf.get("select").apply(e.acf,[t,i])})}),e.on("close",function(){setTimeout(function(){e.acf.get("close").apply(e.acf),e.acf.remove()},1)})}});acf.models.SelectMediaPopup=i.extend({id:"SelectMediaPopup",setup:function(e){e.button||(e.button=acf._x("Select","verb")),i.prototype.setup.apply(this,arguments)},addFrameEvents:function(e,t){acf.isset(_wpPluploadSettings,"defaults","multipart_params")&&(_wpPluploadSettings.defaults.multipart_params._acfuploader=this.get("field"),e.on("open",function(){delete _wpPluploadSettings.defaults.multipart_params._acfuploader})),e.on("content:activate:browse",function(){var t=!1;try{t=e.content.get().toolbar}catch(e){return void console.log(e)}e.acf.customizeFilters.apply(e.acf,[t])}),i.prototype.addFrameEvents.apply(this,arguments)},customizeFilters:function(t){var i=t.get("filters");if("image"==this.get("type")&&(i.filters.all.text=acf.__("All images"),delete i.filters.audio,delete i.filters.video,delete i.filters.image,e.each(i.filters,function(e,t){t.props.type=t.props.type||"image"})),this.get("allowedTypes")&&this.get("allowedTypes").split(" ").join("").split(".").join("").split(",").map(function(e){var t=acf.getMimeType(e);if(t){var a={text:t,props:{status:null,type:t,uploadedTo:null,orderby:"date",order:"DESC"},priority:20};i.filters[t]=a}}),"uploadedTo"===this.get("library")){var a=this.frame.options.library.uploadedTo;delete i.filters.unattached,delete i.filters.uploaded,e.each(i.filters,function(e,t){t.text+=" ("+acf.__("Uploaded to this post")+")",t.props.uploadedTo=a})}var n=this.get("field");e.each(i.filters,function(e,t){t.props._acfuploader=n}),t.get("search").model.attributes._acfuploader=n,i.renderFilters&&i.renderFilters()}}),acf.models.EditMediaPopup=i.extend({id:"SelectMediaPopup",setup:function(e){e.button||(e.button=acf._x("Update","verb")),i.prototype.setup.apply(this,arguments)},addFrameEvents:function(e,t){e.on("open",function(){this.$el.closest(".media-modal").addClass("acf-expanded"),"browse"!=this.content.mode()&&this.content.mode("browse");var t=this.state().get("selection"),i=wp.media.attachment(e.acf.get("attachment"));t.add(i)},e),i.prototype.addFrameEvents.apply(this,arguments)}}),new acf.Model({id:"customizePrototypes",wait:"ready",initialize:function(){if(acf.isset(window,"wp","media","view")){var e=t();e&&acf.isset(wp,"media","view","settings","post")&&(wp.media.view.settings.post.id=e),this.customizeAttachmentsButton(),this.customizeAttachmentsRouter(),this.customizeAttachmentFilters(),this.customizeAttachmentCompat(),this.customizeAttachmentLibrary()}},customizeAttachmentsButton:function(){if(acf.isset(wp,"media","view","Button")){var e=wp.media.view.Button;wp.media.view.Button=e.extend({initialize:function(){var e=_.defaults(this.options,this.defaults);this.model=new Backbone.Model(e),this.listenTo(this.model,"change",this.render)}})}},customizeAttachmentsRouter:function(){if(acf.isset(wp,"media","view","Router")){var t=wp.media.view.Router;wp.media.view.Router=t.extend({addExpand:function(){var t=e(['<a href="#" class="acf-expand-details">','<span class="is-closed"><i class="acf-icon -left -small"></i>'+acf.__("Expand Details")+"</span>",'<span class="is-open"><i class="acf-icon -right -small"></i>'+acf.__("Collapse Details")+"</span>","</a>"].join(""));t.on("click",function(t){t.preventDefault();var i=e(this).closest(".media-modal");i.hasClass("acf-expanded")?i.removeClass("acf-expanded"):i.addClass("acf-expanded")}),this.$el.append(t)},initialize:function(){return t.prototype.initialize.apply(this,arguments),this.addExpand(),this}})}},customizeAttachmentFilters:function(){acf.isset(wp,"media","view","AttachmentFilters","All")&&(wp.media.view.AttachmentFilters.All.prototype.renderFilters=function(){this.$el.html(_.chain(this.filters).map(function(t,i){return{el:e("<option></option>").val(i).html(t.text)[0],priority:t.priority||50}},this).sortBy("priority").pluck("el").value())})},customizeAttachmentCompat:function(){if(acf.isset(wp,"media","view","AttachmentCompat")){var t=wp.media.view.AttachmentCompat,i=!1;wp.media.view.AttachmentCompat=t.extend({render:function(){return this.rendered?this:(t.prototype.render.apply(this,arguments),this.$("#acf-form-data").length?(clearTimeout(i),i=setTimeout(e.proxy(function(){this.rendered=!0,acf.doAction("append",this.$el)},this),50),this):this)},save:function(e){var t;e&&e.preventDefault(),t=acf.serializeForAjax(this.$el),this.controller.trigger("attachment:compat:waiting",["waiting"]),this.model.saveCompat(t).always(_.bind(this.postSave,this))}})}},customizeAttachmentLibrary:function(){if(acf.isset(wp,"media","view","Attachment","Library")){var e=wp.media.view.Attachment.Library;wp.media.view.Attachment.Library=e.extend({render:function(){var t=acf.isget(this,"controller","acf"),i=acf.isget(this,"model","attributes");if(t&&i){i.acf_errors&&this.$el.addClass("acf-disabled");var a=t.get("selected");a&&a.indexOf(i.id)>-1&&this.$el.addClass("acf-selected")}return e.prototype.render.apply(this,arguments)},toggleSelection:function(t){this.collection;var i=this.options.selection,a=this.model,n=(i.single(),this.controller),s=acf.isget(this,"model","attributes","acf_errors"),o=n.$el.find(".media-frame-content .media-sidebar");if(o.children(".acf-selection-error").remove(),o.children().removeClass("acf-hidden"),n&&s){var r=acf.isget(this,"model","attributes","filename");return o.children().addClass("acf-hidden"),o.prepend(['<div class="acf-selection-error">','<span class="selection-error-label">'+acf.__("Restricted")+"</span>",'<span class="selection-error-filename">'+r+"</span>",'<span class="selection-error-message">'+s+"</span>","</div>"].join("")),i.reset(),void i.single(a)}return e.prototype.toggleSelection.apply(this,arguments)}})}}})}(jQuery)},1525:()=>{var e;jQuery,e=acf.Field.extend({type:"true_false",events:{"change .acf-switch-input":"onChange","focus .acf-switch-input":"onFocus","blur .acf-switch-input":"onBlur","keypress .acf-switch-input":"onKeypress"},$input:function(){return this.$('input[type="checkbox"]')},$switch:function(){return this.$(".acf-switch")},getValue:function(){return this.$input().prop("checked")?1:0},initialize:function(){this.render()},render:function(){var e=this.$switch();if(e.length){var t=e.children(".acf-switch-on"),i=e.children(".acf-switch-off"),a=Math.max(t.width(),i.width());a&&(t.css("min-width",a),i.css("min-width",a))}},switchOn:function(){this.$input().prop("checked",!0),this.$switch().addClass("-on")},switchOff:function(){this.$input().prop("checked",!1),this.$switch().removeClass("-on")},onChange:function(e,t){t.prop("checked")?this.switchOn():this.switchOff()},onFocus:function(e,t){this.$switch().addClass("-focus")},onBlur:function(e,t){this.$switch().removeClass("-focus")},onKeypress:function(e,t){return 37===e.keyCode?this.switchOff():39===e.keyCode?this.switchOn():void 0}}),acf.registerFieldType(e)},2093:()=>{var e,t;e=jQuery,t=acf.models.ImageField.extend({type:"file",$control:function(){return this.$(".acf-file-uploader")},$input:function(){return this.$('input[type="hidden"]:first')},validateAttachment:function(e){return void 0!==(e=e||{}).id&&(e=e.attributes),acf.parseArgs(e,{url:"",alt:"",title:"",filename:"",filesizeHumanReadable:"",icon:"/wp-includes/images/media/default.png"})},render:function(e){e=this.validateAttachment(e),this.$("img").attr({src:e.icon,alt:e.alt,title:e.title}),this.$('[data-name="title"]').text(e.title),this.$('[data-name="filename"]').text(e.filename).attr("href",e.url),this.$('[data-name="filesize"]').text(e.filesizeHumanReadable);var t=e.id||"";acf.val(this.$input(),t),t?this.$control().addClass("has-value"):this.$control().removeClass("has-value")},selectAttachment:function(){var t=this.parent(),i=t&&"repeater"===t.get("type");acf.newMediaPopup({mode:"select",title:acf.__("Select File"),field:this.get("key"),multiple:i,library:this.get("library"),allowedTypes:this.get("mime_types"),select:e.proxy(function(e,i){i>0?this.append(e,t):this.render(e)},this)})},editAttachment:function(){var t=this.val();if(!t)return!1;acf.newMediaPopup({mode:"edit",title:acf.__("Edit File"),button:acf.__("Update File"),attachment:t,field:this.get("key"),select:e.proxy(function(e,t){this.render(e)},this)})}}),acf.registerFieldType(t)},2237:()=>{var e,t;e=jQuery,t=acf.Field.extend({type:"oembed",events:{'click [data-name="clear-button"]':"onClickClear","keypress .input-search":"onKeypressSearch","keyup .input-search":"onKeyupSearch","change .input-search":"onChangeSearch"},$control:function(){return this.$(".acf-oembed")},$input:function(){return this.$(".input-value")},$search:function(){return this.$(".input-search")},getValue:function(){return this.$input().val()},getSearchVal:function(){return this.$search().val()},setValue:function(e){e?this.$control().addClass("has-value"):this.$control().removeClass("has-value"),acf.val(this.$input(),e)},showLoading:function(e){acf.showLoading(this.$(".canvas"))},hideLoading:function(){acf.hideLoading(this.$(".canvas"))},maybeSearch:function(){var t=this.val(),i=this.getSearchVal();if(!i)return this.clear();if("http"!=i.substr(0,4)&&(i="http://"+i),i!==t){var a=this.get("timeout");a&&clearTimeout(a);var n=e.proxy(this.search,this,i);this.set("timeout",setTimeout(n,300))}},search:function(t){const i={action:"acf/fields/oembed/search",s:t,field_key:this.get("key"),nonce:this.get("nonce")};let a=this.get("xhr");a&&a.abort(),this.showLoading(),a=e.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(i),type:"post",dataType:"json",context:this,success:function(e){e&&e.html||(e={url:!1,html:""}),this.val(e.url),this.$(".canvas-media").html(e.html)},complete:function(){this.hideLoading()}}),this.set("xhr",a)},clear:function(){this.val(""),this.$search().val(""),this.$(".canvas-media").html("")},onClickClear:function(e,t){this.clear()},onKeypressSearch:function(e,t){13==e.which&&(e.preventDefault(),this.maybeSearch())},onKeyupSearch:function(e,t){t.val()&&this.maybeSearch()},onChangeSearch:function(e,t){this.maybeSearch()}}),acf.registerFieldType(t)},2410:()=>{var e,t;e=jQuery,t=acf.Field.extend({type:"image",$control:function(){return this.$(".acf-image-uploader")},$input:function(){return this.$('input[type="hidden"]:first')},events:{'click a[data-name="add"]':"onClickAdd",'click a[data-name="edit"]':"onClickEdit",'click a[data-name="remove"]':"onClickRemove",'change input[type="file"]':"onChange"},initialize:function(){"basic"===this.get("uploader")&&this.$el.closest("form").attr("enctype","multipart/form-data")},validateAttachment:function(e){e&&e.attributes&&(e=e.attributes),e=acf.parseArgs(e,{id:0,url:"",alt:"",title:"",caption:"",description:"",width:0,height:0});var t=acf.isget(e,"sizes",this.get("preview_size"));return t&&(e.url=t.url,e.width=t.width,e.height=t.height),e},render:function(e){e=this.validateAttachment(e),this.$("img").attr({src:e.url,alt:e.alt}),e.id?(this.val(e.id),this.$control().addClass("has-value")):(this.val(""),this.$control().removeClass("has-value"))},append:function(e,t){var i=function(e,t){for(var i=acf.getFields({key:e.get("key"),parent:t.$el}),a=0;a<i.length;a++)if(!i[a].val())return i[a];return!1},a=i(this,t);a||(t.$(".acf-button:last").trigger("click"),a=i(this,t)),a&&a.render(e)},selectAttachment:function(){var t=this.parent(),i=t&&"repeater"===t.get("type");acf.newMediaPopup({mode:"select",type:"image",title:acf.__("Select Image"),field:this.get("key"),multiple:i,library:this.get("library"),allowedTypes:this.get("mime_types"),select:e.proxy(function(e,i){i>0?this.append(e,t):this.render(e)},this)})},editAttachment:function(){var t=this.val();t&&acf.newMediaPopup({mode:"edit",title:acf.__("Edit Image"),button:acf.__("Update Image"),attachment:t,field:this.get("key"),select:e.proxy(function(e,t){this.render(e)},this)})},removeAttachment:function(){this.render(!1)},onClickAdd:function(e,t){this.selectAttachment()},onClickEdit:function(e,t){this.editAttachment()},onClickRemove:function(e,t){this.removeAttachment()},onChange:function(t,i){var a=this.$input();i.val()||a.val(""),acf.getFileInputData(i,function(t){a.val(e.param(t))})}}),acf.registerFieldType(t)},2457:()=>{!function(e){acf.findFields=function(t){var i=".acf-field",a=!1;return(t=acf.parseArgs(t,{key:"",name:"",type:"",is:"",parent:!1,sibling:!1,limit:!1,visible:!1,suppressFilters:!1,excludeSubFields:!1})).suppressFilters||(t=acf.applyFilters("find_fields_args",t)),t.key&&(i+='[data-key="'+t.key+'"]'),t.type&&(i+='[data-type="'+t.type+'"]'),t.name&&(i+='[data-name="'+t.name+'"]'),t.is&&(i+=t.is),t.visible&&(i+=":visible"),t.suppressFilters||(i=acf.applyFilters("find_fields_selector",i,t)),t?.parent&&t?.parent?.find?(a=t.parent.find(i),t.excludeSubFields&&(a=a.not(t.parent.find(".acf-is-subfields .acf-field")))):a=t.sibling?t.sibling.siblings(i):e(i),t.suppressFilters||(a=a.not(".acf-clone .acf-field"),a=acf.applyFilters("find_fields",a)),t.limit&&(a=a.slice(0,t.limit)),a},acf.findField=function(e,t){return acf.findFields({key:e,limit:1,parent:t,suppressFilters:!0})},acf.getField=function(e){e instanceof jQuery||(e=acf.findField(e));var t=e.data("acf");return t||(t=acf.newField(e)),t},acf.getFields=function(t){t instanceof jQuery||(t=acf.findFields(t));var i=[];return t.each(function(){var t=acf.getField(e(this));i.push(t)}),i},acf.findClosestField=function(e){return e.closest(".acf-field")},acf.getClosestField=function(e){var t=acf.findClosestField(e);return this.getField(t)};var t=function(e){var t=e+"_field",a=e+"Field";acf.addAction(t,function(n){var s=acf.arrayArgs(arguments),o=s.slice(1);["type","name","key"].map(function(e){var i="/"+e+"="+n.get(e);s=[t+i,n].concat(o),acf.doAction.apply(null,s)}),i.indexOf(e)>-1&&n.trigger(a,o)})},i=["remove","unmount","remount","sortstart","sortstop","show","hide","unload","valid","invalid","enable","disable","duplicate"];["prepare","ready","load","append","remove","unmount","remount","sortstart","sortstop","show","hide","unload"].map(function(e){var i=e,a=e+"_fields",n=e+"_field";acf.addAction(i,function(e){var t=acf.arrayArgs(arguments).slice(1),i=acf.getFields({parent:e});if(i.length){var n=[a,i].concat(t);acf.doAction.apply(null,n)}}),acf.addAction(a,function(e){var t=acf.arrayArgs(arguments).slice(1);e.map(function(e,i){var a=[n,e].concat(t);acf.doAction.apply(null,a)})}),t(e)}),["valid","invalid","enable","disable","new","duplicate"].map(t),new acf.Model({id:"fieldsEventManager",events:{'click .acf-field a[href="#"]':"onClick","change .acf-field":"onChange"},onClick:function(e){e.preventDefault()},onChange:function(){if(e("#_acf_changed").val(1),acf.isGutenbergPostEditor())try{wp.data.dispatch("core/editor").editPost({meta:{_acf_changed:1}})}catch(e){console.log("ACF: Failed to update _acf_changed meta",e)}}}),new acf.Model({id:"duplicateFieldsManager",actions:{duplicate:"onDuplicate",duplicate_fields:"onDuplicateFields"},onDuplicate:function(e,t){var i=acf.getFields({parent:e});if(i.length){var a=acf.findFields({parent:t});acf.doAction("duplicate_fields",i,a)}},onDuplicateFields:function(t,i){t.map(function(t,a){acf.doAction("duplicate_field",t,e(i[a]))})}})}(jQuery)},2553:()=>{var e;jQuery,e=acf.models.SelectField.extend({type:"post_object"}),acf.registerFieldType(e)},2631:()=>{!function(e){var t=acf.Model.extend({id:"Validator",data:{errors:[],notice:null,status:""},events:{"changed:status":"onChangeStatus"},addErrors:function(e){e.map(this.addError,this)},addError:function(e){this.data.errors.push(e)},hasErrors:function(){return this.data.errors.length},clearErrors:function(){return this.data.errors=[]},getErrors:function(){return this.data.errors},getFieldErrors:function(){var e=[],t=[];return this.getErrors().map(function(i){if(i.input){var a=t.indexOf(i.input);a>-1?e[a]=i:(e.push(i),t.push(i.input))}}),e},getGlobalErrors:function(){return this.getErrors().filter(function(e){return!e.input})},showErrors:function(t="before"){if(this.hasErrors()){var i=this.getFieldErrors(),a=this.getGlobalErrors(),n=0,o=!1;i.map(function(e){var i=this.$('[name="'+e.input+'"]').first();if(i.length||(i=this.$('[name^="'+e.input+'"]').first()),i.length){n++;var a=acf.getClosestField(i);s(a.$el),a.showError(e.message,t),o||(o=a.$el)}},this);var r=acf.__("Validation failed"),c={};if(a.map(function(e){r+=". "+e.message,e.action&&"object"==typeof e.action&&(c=e.action)}),1==n?r+=". "+acf.__("1 field requires attention"):n>1&&(r+=". "+acf.__("%d fields require attention").replace("%d",n)),this.has("notice"))this.get("notice").update({type:"error",text:r,action:c});else{var l=acf.newNotice({type:"error",text:r,target:this.$el,action:c});this.set("notice",l)}this.$el.parents(".acf-popup-box").length||(o||(o=this.get("notice").$el),setTimeout(function(){e("html, body").animate({scrollTop:o.offset().top-e(window).height()/2},500)},10))}},onChangeStatus:function(e,t,i,a){this.$el.removeClass("is-"+a).addClass("is-"+i)},validate:function(t){if(t=acf.parseArgs(t,{event:!1,reset:!1,loading:function(){},complete:function(){},failure:function(){},success:function(e){e.submit()}}),"valid"==this.get("status"))return!0;if("validating"==this.get("status"))return!1;if(!this.$(".acf-field").length)return!0;if(t.event){var i=e.Event(null,t.event);t.success=function(){acf.enableSubmit(e(i.target)).trigger(i)}}acf.doAction("validation_begin",this.$el),acf.lockForm(this.$el),t.loading(this.$el,this),this.set("status","validating");var a=acf.serialize(this.$el);return a.action="acf/validate_save_post",e.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(a,!0),type:"post",dataType:"json",context:this,success:function(e){if(acf.isAjaxSuccess(e)){var t=acf.applyFilters("validation_complete",e.data,this.$el,this);t.valid||this.addErrors(t.errors)}},complete:function(){acf.unlockForm(this.$el),this.hasErrors()?(this.set("status","invalid"),acf.doAction("validation_failure",this.$el,this),this.showErrors(),t.failure(this.$el,this)):(this.set("status","valid"),this.has("notice")&&this.get("notice").update({type:"success",text:acf.__("Validation successful"),timeout:1e3}),acf.doAction("validation_success",this.$el,this),acf.doAction("submit",this.$el),t.success(this.$el,this),acf.lockForm(this.$el),t.reset&&this.reset()),t.complete(this.$el,this),this.clearErrors()}}),!1},setup:function(e){this.$el=e},reset:function(){this.set("errors",[]),this.set("notice",null),this.set("status",""),acf.unlockForm(this.$el)}}),i=function(e){var i=e.data("acf");return i||(i=new t(e)),i};acf.getBlockFormValidator=function(e){return i(e)},acf.validateForm=function(e){return i(e.form).validate(e)},acf.enableSubmit=function(e){return e.removeClass("disabled").removeAttr("disabled")},acf.disableSubmit=function(e){return e.addClass("disabled").attr("disabled",!0)},acf.showSpinner=function(e){return e.addClass("is-active"),e.css("display","inline-block"),e},acf.hideSpinner=function(e){return e.removeClass("is-active"),e.css("display","none"),e},acf.lockForm=function(e){var t=a(e),i=t.find('.button, [type="submit"]').not(".acf-nav, .acf-repeater-add-row"),n=t.find(".spinner, .acf-spinner");return acf.hideSpinner(n),acf.disableSubmit(i),acf.showSpinner(n.last()),e},acf.unlockForm=function(e){var t=a(e),i=t.find('.button, [type="submit"]').not(".acf-nav, .acf-repeater-add-row"),n=t.find(".spinner, .acf-spinner");return acf.enableSubmit(i),acf.hideSpinner(n),e};var a=function(t){var i;return(i=t.find("#submitdiv")).length||(i=t.find("#submitpost")).length||(i=t.find("p.submit").last()).length||(i=t.find(".acf-form-submit")).length||(i=e("#acf-create-options-page-form .acf-actions")).length||(i=e(".acf-headerbar-actions")).length?i:t},n=acf.debounce(function(e){e.submit()}),s=function(e){var t=e.parents(".acf-postbox");if(t.length){var i=acf.getPostbox(t);i&&i.isHiddenByScreenOptions()&&(i.$el.removeClass("hide-if-js"),i.$el.css("display",""))}};acf.validation=new acf.Model({id:"validation",active:!0,wait:"prepare",actions:{ready:"addInputEvents",append:"addInputEvents"},events:{'click input[type="submit"]':"onClickSubmit",'click button[type="submit"]':"onClickSubmit","click #save-post":"onClickSave","submit form#post":"onSubmitPost","submit form":"onSubmit"},initialize:function(){acf.get("validation")||(this.active=!1,this.actions={},this.events={})},enable:function(){this.active=!0},disable:function(){this.active=!1},reset:function(e){i(e).reset()},addInputEvents:function(t){if("safari"!==acf.get("browser")){var i=e(".acf-field [name]",t);i.length&&this.on(i,"invalid","onInvalid")}},onInvalid:function(e,t){e.preventDefault();var a=t.closest("form");a.length&&(i(a).addError({input:t.attr("name"),message:acf.strEscape(e.target.validationMessage)}),n(a))},onClickSubmit:function(t,i){e(".acf-field input").each(function(){this.checkValidity()||s(e(this))}),this.set("originalEvent",t)},onClickSave:function(e,t){this.set("ignore",!0)},onSubmitPost:function(t,i){"dopreview"===e("input#wp-preview").val()&&(this.set("ignore",!0),acf.unlockForm(i))},onSubmit:function(e,t){if(!this.active||this.get("ignore")||e.isDefaultPrevented())return this.allowSubmit();acf.validateForm({form:t,event:this.get("originalEvent")})||e.preventDefault()},allowSubmit:function(){return this.set("ignore",!1),this.set("originalEvent",!1),!0}}),new acf.Model({wait:"prepare",initialize:function(){acf.isGutenberg()&&this.customizeEditor()},customizeEditor:function(){var t=wp.data.dispatch("core/editor"),i=wp.data.select("core/editor"),a=wp.data.dispatch("core/notices"),n=t.savePost,s=!1,o="";wp.data.subscribe(function(){var e=i.getEditedPostAttribute("status");s="publish"===e||"future"===e,o="publish"!==e?e:o}),t.savePost=function(i){i=i||{};var r=this,c=arguments;return new Promise(function(n,r){if(i.isAutosave||i.isPreview)return n("Validation ignored (autosave).");if(!s)return n("Validation ignored (draft).");if(void 0!==acf.blockInstances){const e=wp.data.select("core/block-editor").getSelectedBlockClientId();if(e&&e in acf.blockInstances&&acf.blockInstances[e].validation_errors)return acf.debug("Rejecting save because the block editor has a invalid ACF block selected."),a.createErrorNotice(acf.__("An ACF Block on this page requires attention before you can save."),{id:"acf-validation",isDismissible:!0}),wp.data.dispatch("core/editor").lockPostSaving("acf/block/"+e),wp.data.dispatch("core/block-editor").selectBlock(!1),r("ACF Validation failed for selected block.")}acf.validateForm({form:e("#wpbody-content > .block-editor"),reset:!0,complete:function(e,i){t.unlockPostSaving("acf")},failure:function(e,i){var n=i.get("notice"),s=n.get("action");s&&"object"==typeof s&&s.label&&s.url?a.createErrorNotice(n.get("text"),{id:"acf-validation",isDismissible:!0,actions:[{label:s.label,url:s.url}]}):a.createErrorNotice(n.get("text"),{id:"acf-validation",isDismissible:!0}),n.remove(),o&&t.editPost({status:o}),r("Validation failed.")},success:function(){a.removeNotice("acf-validation"),n("Validation success.")}})?n("Validation bypassed."):t.lockPostSaving("acf")}).then(function(){return n.apply(r,c)},e=>{})}}})}(jQuery)},2747:()=>{!function(e){var __=acf.__,t=function(e){return e?""+e:""},i=function(e,i){return t(e).toLowerCase()===t(i).toLowerCase()},a=function(e,t){return t instanceof Array?1===t.length&&a(e,t[0]):parseFloat(e)===parseFloat(t)};const n=function(t,i){const a=e("<select></select>");let n=`acf/fields/${i}/query`;"user"===i&&(n="acf/ajax/query_users");const s={action:n,field_key:t.data.key,s:"",type:t.data.key},o=acf.escAttr(i),r={field:!1,ajax:!0,ajaxAction:n,ajaxData:function(t){return s.paged=t.paged,s.s=t.s,s.conditional_logic=!0,s.include=e.isNumeric(t.s)?Number(t.s):"",acf.prepareForAjax(s)},escapeMarkup:function(e){return acf.escHtml(e)},templateSelection:function(e){return`<span class="acf-${o}-select-name acf-conditional-select-name">`+acf.strEscape(e.text)+"</span>"},templateResult:function(e){return'<span class="'+(e.text.startsWith("- ")?`acf-${o}-select-name acf-${o}-select-sub-item`:`acf-${o}-select-name`)+'">'+acf.strEscape(e.text)+"</span>"+`<span class="acf-${o}-select-id acf-conditional-select-id">`+(e.id?e.id:"")+"</span>"}};return a.data("acfSelect2Props",r),a};var s=acf.Condition.extend({type:"hasPageLink",operator:"==",label:__("Page is equal to"),fieldTypes:["page_link"],match:function(e,t){return i(e.value,t.val())},choices:function(e){return n(e,"page_link")}});acf.registerConditionType(s);var o=acf.Condition.extend({type:"hasPageLinkNotEqual",operator:"!==",label:__("Page is not equal to"),fieldTypes:["page_link"],match:function(e,t){return!i(e.value,t.val())},choices:function(e){return n(e,"page_link")}});acf.registerConditionType(o);var r=acf.Condition.extend({type:"containsPageLink",operator:"==contains",label:__("Pages contain"),fieldTypes:["page_link"],match:function(e,t){const i=t.val(),a=e.value;let n=!1;return n=i instanceof Array?i.includes(a):i===a,n},choices:function(e){return n(e,"page_link")}});acf.registerConditionType(r);var c=acf.Condition.extend({type:"containsNotPageLink",operator:"!=contains",label:__("Pages do not contain"),fieldTypes:["page_link"],match:function(e,t){const i=t.val(),a=e.value;let n=!0;return n=i instanceof Array?!i.includes(a):i!==a,n},choices:function(e){return n(e,"page_link")}});acf.registerConditionType(c);var l=acf.Condition.extend({type:"hasAnyPageLink",operator:"!=empty",label:__("Has any page selected"),fieldTypes:["page_link"],match:function(e,t){let i=t.val();return i instanceof Array&&(i=i.length),!!i},choices:function(){return'<input type="text" disabled />'}});acf.registerConditionType(l);var d=acf.Condition.extend({type:"hasNoPageLink",operator:"==empty",label:__("Has no page selected"),fieldTypes:["page_link"],match:function(e,t){let i=t.val();return i instanceof Array&&(i=i.length),!i},choices:function(){return'<input type="text" disabled />'}});acf.registerConditionType(d);var u=acf.Condition.extend({type:"hasUser",operator:"==",label:__("User is equal to"),fieldTypes:["user"],match:function(e,t){return a(e.value,t.val())},choices:function(e){return n(e,"user")}});acf.registerConditionType(u);var f=acf.Condition.extend({type:"hasUserNotEqual",operator:"!==",label:__("User is not equal to"),fieldTypes:["user"],match:function(e,t){return!a(e.value,t.val())},choices:function(e){return n(e,"user")}});acf.registerConditionType(f);var p=acf.Condition.extend({type:"containsUser",operator:"==contains",label:__("Users contain"),fieldTypes:["user"],match:function(e,t){const i=t.val(),a=e.value;let n=!1;return n=i instanceof Array?i.includes(a):i===a,n},choices:function(e){return n(e,"user")}});acf.registerConditionType(p);var h=acf.Condition.extend({type:"containsNotUser",operator:"!=contains",label:__("Users do not contain"),fieldTypes:["user"],match:function(e,t){const i=t.val(),a=e.value;let n=!0;n=i instanceof Array?!i.includes(a):!i===a},choices:function(e){return n(e,"user")}});acf.registerConditionType(h);var g=acf.Condition.extend({type:"hasAnyUser",operator:"!=empty",label:__("Has any user selected"),fieldTypes:["user"],match:function(e,t){let i=t.val();return i instanceof Array&&(i=i.length),!!i},choices:function(){return'<input type="text" disabled />'}});acf.registerConditionType(g);var m=acf.Condition.extend({type:"hasNoUser",operator:"==empty",label:__("Has no user selected"),fieldTypes:["user"],match:function(e,t){let i=t.val();return i instanceof Array&&(i=i.length),!i},choices:function(){return'<input type="text" disabled />'}});acf.registerConditionType(m);var v=acf.Condition.extend({type:"hasRelationship",operator:"==",label:__("Relationship is equal to"),fieldTypes:["relationship"],match:function(e,t){return a(e.value,t.val())},choices:function(e){return n(e,"relationship")}});acf.registerConditionType(v);var y=acf.Condition.extend({type:"hasRelationshipNotEqual",operator:"!==",label:__("Relationship is not equal to"),fieldTypes:["relationship"],match:function(e,t){return!a(e.value,t.val())},choices:function(e){return n(e,"relationship")}});acf.registerConditionType(y);var b=acf.Condition.extend({type:"containsRelationship",operator:"==contains",label:__("Relationships contain"),fieldTypes:["relationship"],match:function(e,t){const i=t.val(),a=parseInt(e.value);let n=!1;return i instanceof Array&&(n=i.includes(a)),n},choices:function(e){return n(e,"relationship")}});acf.registerConditionType(b);var _=acf.Condition.extend({type:"containsNotRelationship",operator:"!=contains",label:__("Relationships do not contain"),fieldTypes:["relationship"],match:function(e,t){const i=t.val(),a=parseInt(e.value);let n=!0;return i instanceof Array&&(n=!i.includes(a)),n},choices:function(e){return n(e,"relationship")}});acf.registerConditionType(_);var w=acf.Condition.extend({type:"hasAnyRelation",operator:"!=empty",label:__("Has any relationship selected"),fieldTypes:["relationship"],match:function(e,t){let i=t.val();return i instanceof Array&&(i=i.length),!!i},choices:function(){return'<input type="text" disabled />'}});acf.registerConditionType(w);var x=acf.Condition.extend({type:"hasNoRelation",operator:"==empty",label:__("Has no relationship selected"),fieldTypes:["relationship"],match:function(e,t){let i=t.val();return i instanceof Array&&(i=i.length),!i},choices:function(){return'<input type="text" disabled />'}});acf.registerConditionType(x);var k=acf.Condition.extend({type:"hasPostObject",operator:"==",label:__("Post is equal to"),fieldTypes:["post_object"],match:function(e,t){return a(e.value,t.val())},choices:function(e){return n(e,"post_object")}});acf.registerConditionType(k);var $=acf.Condition.extend({type:"hasPostObjectNotEqual",operator:"!==",label:__("Post is not equal to"),fieldTypes:["post_object"],match:function(e,t){return!a(e.value,t.val())},choices:function(e){return n(e,"post_object")}});acf.registerConditionType($);var T=acf.Condition.extend({type:"containsPostObject",operator:"==contains",label:__("Posts contain"),fieldTypes:["post_object"],match:function(e,t){const i=t.val(),a=e.value;let n=!1;return n=i instanceof Array?i.includes(a):i===a,n},choices:function(e){return n(e,"post_object")}});acf.registerConditionType(T);var C=acf.Condition.extend({type:"containsNotPostObject",operator:"!=contains",label:__("Posts do not contain"),fieldTypes:["post_object"],match:function(e,t){const i=t.val(),a=e.value;let n=!0;return n=i instanceof Array?!i.includes(a):i!==a,n},choices:function(e){return n(e,"post_object")}});acf.registerConditionType(C);var F=acf.Condition.extend({type:"hasAnyPostObject",operator:"!=empty",label:__("Has any post selected"),fieldTypes:["post_object"],match:function(e,t){let i=t.val();return i instanceof Array&&(i=i.length),!!i},choices:function(){return'<input type="text" disabled />'}});acf.registerConditionType(F);var A=acf.Condition.extend({type:"hasNoPostObject",operator:"==empty",label:__("Has no post selected"),fieldTypes:["post_object"],match:function(e,t){let i=t.val();return i instanceof Array&&(i=i.length),!i},choices:function(){return'<input type="text" disabled />'}});acf.registerConditionType(A);var P=acf.Condition.extend({type:"hasTerm",operator:"==",label:__("Term is equal to"),fieldTypes:["taxonomy"],match:function(e,t){return a(e.value,t.val())},choices:function(e){return n(e,"taxonomy")}});acf.registerConditionType(P);var j=acf.Condition.extend({type:"hasTermNotEqual",operator:"!==",label:__("Term is not equal to"),fieldTypes:["taxonomy"],match:function(e,t){return!a(e.value,t.val())},choices:function(e){return n(e,"taxonomy")}});acf.registerConditionType(j);var S=acf.Condition.extend({type:"containsTerm",operator:"==contains",label:__("Terms contain"),fieldTypes:["taxonomy"],match:function(e,t){const i=t.val(),a=e.value;let n=!1;return i instanceof Array&&(n=i.includes(a)),n},choices:function(e){return n(e,"taxonomy")}});acf.registerConditionType(S);var E=acf.Condition.extend({type:"containsNotTerm",operator:"!=contains",label:__("Terms do not contain"),fieldTypes:["taxonomy"],match:function(e,t){const i=t.val(),a=e.value;let n=!0;return i instanceof Array&&(n=!i.includes(a)),n},choices:function(e){return n(e,"taxonomy")}});acf.registerConditionType(E);var M=acf.Condition.extend({type:"hasAnyTerm",operator:"!=empty",label:__("Has any term selected"),fieldTypes:["taxonomy"],match:function(e,t){let i=t.val();return i instanceof Array&&(i=i.length),!!i},choices:function(){return'<input type="text" disabled />'}});acf.registerConditionType(M);var L=acf.Condition.extend({type:"hasNoTerm",operator:"==empty",label:__("Has no term selected"),fieldTypes:["taxonomy"],match:function(e,t){let i=t.val();return i instanceof Array&&(i=i.length),!i},choices:function(){return'<input type="text" disabled />'}});acf.registerConditionType(L);var I=acf.Condition.extend({type:"hasValue",operator:"!=empty",label:__("Has any value"),fieldTypes:["text","textarea","number","range","email","url","password","image","file","wysiwyg","oembed","select","checkbox","radio","button_group","link","google_map","date_picker","date_time_picker","time_picker","color_picker","icon_picker"],match:function(e,t){let i=t.val();return i instanceof Array&&(i=i.length),!!i},choices:function(e){return'<input type="text" disabled />'}});acf.registerConditionType(I);var V=I.extend({type:"hasNoValue",operator:"==empty",label:__("Has no value"),match:function(e,t){return!I.prototype.match.apply(this,arguments)}});acf.registerConditionType(V);var R=acf.Condition.extend({type:"equalTo",operator:"==",label:__("Value is equal to"),fieldTypes:["text","textarea","number","range","email","url","password"],match:function(e,t){return acf.isNumeric(e.value)?a(e.value,t.val()):i(e.value,t.val())},choices:function(e){return'<input type="text" />'}});acf.registerConditionType(R);var z=R.extend({type:"notEqualTo",operator:"!=",label:__("Value is not equal to"),match:function(e,t){return!R.prototype.match.apply(this,arguments)}});acf.registerConditionType(z);var D=acf.Condition.extend({type:"patternMatch",operator:"==pattern",label:__("Value matches pattern"),fieldTypes:["text","textarea","email","url","password","wysiwyg"],match:function(e,i){return a=i.val(),n=e.value,s=new RegExp(t(n),"gi"),t(a).match(s);var a,n,s},choices:function(e){return'<input type="text" placeholder="[a-z0-9]" />'}});acf.registerConditionType(D);var O=acf.Condition.extend({type:"contains",operator:"==contains",label:__("Value contains"),fieldTypes:["text","textarea","number","email","url","password","wysiwyg","oembed","select"],match:function(e,i){return a=i.val(),n=e.value,t(a).indexOf(t(n))>-1;var a,n},choices:function(e){return'<input type="text" />'}});acf.registerConditionType(O);var N=R.extend({type:"trueFalseEqualTo",choiceType:"select",fieldTypes:["true_false"],choices:function(e){return[{id:1,text:__("Checked")}]}});acf.registerConditionType(N);var B=z.extend({type:"trueFalseNotEqualTo",choiceType:"select",fieldTypes:["true_false"],choices:function(e){return[{id:1,text:__("Checked")}]}});acf.registerConditionType(B);var Q=acf.Condition.extend({type:"selectEqualTo",operator:"==",label:__("Value is equal to"),fieldTypes:["select","checkbox","radio","button_group"],match:function(e,a){var n,s=a.val();return s instanceof Array?(n=e.value,s.map(function(e){return t(e)}).indexOf(n)>-1):i(e.value,s)},choices:function(e){var t=[],i=e.$setting("choices textarea").val().split("\n");return e.$input("allow_null").prop("checked")&&t.push({id:"",text:__("Null")}),i.map(function(e){(e=e.split(":"))[1]=e[1]||e[0],t.push({id:e[0].trim(),text:e[1].trim()})}),t}});acf.registerConditionType(Q);var q=Q.extend({type:"selectNotEqualTo",operator:"!=",label:__("Value is not equal to"),match:function(e,t){return!Q.prototype.match.apply(this,arguments)}});acf.registerConditionType(q);var H=acf.Condition.extend({type:"greaterThan",operator:">",label:__("Value is greater than"),fieldTypes:["number","range"],match:function(e,t){var i,a,n=t.val();return n instanceof Array&&(n=n.length),i=n,a=e.value,parseFloat(i)>parseFloat(a)},choices:function(e){return'<input type="number" />'}});acf.registerConditionType(H);var U=H.extend({type:"lessThan",operator:"<",label:__("Value is less than"),match:function(e,t){var i,a,n=t.val();return n instanceof Array&&(n=n.length),null==n||!1===n||(i=n,a=e.value,parseFloat(i)<parseFloat(a))},choices:function(e){return'<input type="number" />'}});acf.registerConditionType(U);var G=H.extend({type:"selectionGreaterThan",label:__("Selection is greater than"),fieldTypes:["checkbox","select","post_object","page_link","relationship","taxonomy","user"]});acf.registerConditionType(G);var K=U.extend({type:"selectionLessThan",label:__("Selection is less than"),fieldTypes:["checkbox","select","post_object","page_link","relationship","taxonomy","user"]});acf.registerConditionType(K)}(jQuery)},2900:()=>{!function(e,t){function i(){return acf.isset(window,"jQuery","fn","select2","amd")?4:!!acf.isset(window,"Select2")&&3}acf.newSelect2=function(e,t){if(t=acf.parseArgs(t,{allowNull:!1,placeholder:"",multiple:!1,field:!1,ajax:!1,tags:!1,ajaxAction:"",ajaxData:function(e){return e},ajaxResults:function(e){return e},escapeMarkup:!1,templateSelection:!1,templateResult:!1,dropdownCssClass:"",suppressFilters:!1}),4==i())var a=new n(e,t);else a=new s(e,t);return acf.doAction("new_select2",a),a};var a=acf.Model.extend({setup:function(t,i){e.extend(this.data,i),this.$el=t},initialize:function(){},selectOption:function(e){var t=this.getOption(e);t.prop("selected")||t.prop("selected",!0).trigger("change")},unselectOption:function(e){var t=this.getOption(e);t.prop("selected")&&t.prop("selected",!1).trigger("change")},getOption:function(e){return this.$('option[value="'+e+'"]')},addOption:function(t){t=acf.parseArgs(t,{id:"",text:"",selected:!1});var i=this.getOption(t.id);return i.length||((i=e("<option></option>")).html(t.text),i.attr("value",t.id),i.prop("selected",t.selected),this.$el.append(i)),i},getValue:function(){var t=[],i=this.$el.find("option:selected");return i.exists()?((i=i.sort(function(e,t){return+e.getAttribute("data-i")-+t.getAttribute("data-i")})).each(function(){var i=e(this);t.push({$el:i,id:i.attr("value"),text:i.text()})}),t):t},mergeOptions:function(){},getChoices:function(){var t=function(i){var a=[];return i.children().each(function(){var i=e(this);i.is("optgroup")?a.push({text:i.attr("label"),children:t(i)}):a.push({id:i.attr("value"),text:i.text()})}),a};return t(this.$el)},getAjaxData:function(e){var t={action:this.get("ajaxAction"),s:e.term||"",paged:e.page||1},i=this.get("field");i&&(t.field_key=i.get("key"),i.get("nonce")&&(t.nonce=i.get("nonce")));var a=this.get("ajaxData");return a&&(t=a.apply(this,[t,e])),t=acf.applyFilters("select2_ajax_data",t,this.data,this.$el,i||!1,this),acf.prepareForAjax(t)},getAjaxResults:function(e,t){e=acf.parseArgs(e,{results:!1,more:!1});var i=this.get("ajaxResults");return i&&(e=i.apply(this,[e,t])),acf.applyFilters("select2_ajax_results",e,t,this)},processAjaxResults:function(t,i){return(t=this.getAjaxResults(t,i)).more&&(t.pagination={more:!0}),setTimeout(e.proxy(this.mergeOptions,this),1),t},destroy:function(){this.$el.data("select2")&&this.$el.select2("destroy"),this.$el.siblings(".select2-container").remove()}}),n=a.extend({initialize:function(){var i=this.$el,a={width:"100%",allowClear:this.get("allowNull"),placeholder:this.get("placeholder"),multiple:this.get("multiple"),escapeMarkup:this.get("escapeMarkup"),templateSelection:this.get("templateSelection"),templateResult:this.get("templateResult"),dropdownCssClass:this.get("dropdownCssClass"),suppressFilters:this.get("suppressFilters"),data:[]};this.get("tags")&&(a.tags=!0,a.tokenSeparators=[","]),a.templateSelection||delete a.templateSelection,a.templateResult||delete a.templateResult,a.dropdownCssClass||delete a.dropdownCssClass,acf.isset(window,"jQuery","fn","selectWoo")?(delete a.templateSelection,delete a.templateResult):a.templateSelection||(a.templateSelection=function(t){var i=e('<span class="acf-selection"></span>');return i.html(a.escapeMarkup(t.text)),i.data("element",t.element),i}),a.escapeMarkup||(a.escapeMarkup=function(e){return"string"!=typeof e?e:this.suppressFilters?acf.strEscape(e):acf.applyFilters("select2_escape_markup",acf.strEscape(e),e,i,this.data,s||!1,this)}),a.multiple&&this.getValue().map(function(e){e.$el.detach().appendTo(i)});var n=i.attr("data-ajax");if(n!==t&&(i.removeData("ajax"),i.removeAttr("data-ajax")),this.get("ajax")&&(a.ajax={url:acf.get("ajaxurl"),delay:250,dataType:"json",type:"post",cache:!1,data:e.proxy(this.getAjaxData,this),processResults:e.proxy(this.processAjaxResults,this)}),!a.suppressFilters){var s=this.get("field");a=acf.applyFilters("select2_args",a,i,this.data,s||!1,this)}i.select2(a);var o=i.next(".select2-container");if(a.multiple){var r=o.find("ul");r.sortable({stop:function(t){r.find(".select2-selection__choice").each(function(){if(e(this).data("data"))var t=e(e(this).data("data").element);else t=e(e(this).find("span.acf-selection").data("element"));t.detach().appendTo(i)}),i.trigger("change")}}),i.on("select2:select",this.proxy(function(e){this.getOption(e.params.data.id).detach().appendTo(this.$el)}))}i.on("select2:open",()=>{e(".select2-container--open .select2-search__field").get(-1).focus()}),o.addClass("-acf"),n!==t&&i.attr("data-ajax",n),a.suppressFilters||acf.doAction("select2_init",i,a,this.data,s||!1,this)},mergeOptions:function(){var t=!1,i=!1;e('.select2-results__option[role="group"]').each(function(){var a=e(this).children("ul"),n=e(this).children("strong");if(i&&i.text()===n.text())return t.append(a.children()),void e(this).remove();t=a,i=n})}}),s=a.extend({initialize:function(){var t=this.$el,i=this.getValue(),a=this.get("multiple"),n={width:"100%",allowClear:this.get("allowNull"),placeholder:this.get("placeholder"),separator:"||",multiple:this.get("multiple"),data:this.getChoices(),escapeMarkup:function(e){return acf.escHtml(e)},dropdownCss:{"z-index":"999999999"},initSelection:function(e,t){t(a?i:i.shift())}},s=t.siblings("input");s.length||(s=e('<input type="hidden" />'),t.before(s)),inputValue=i.map(function(e){return e.id}).join("||"),s.val(inputValue),n.multiple&&i.map(function(e){e.$el.detach().appendTo(t)}),n.allowClear&&(n.data=n.data.filter(function(e){return""!==e.id})),t.removeData("ajax"),t.removeAttr("data-ajax"),this.get("ajax")&&(n.ajax={url:acf.get("ajaxurl"),quietMillis:250,dataType:"json",type:"post",cache:!1,data:e.proxy(this.getAjaxData,this),results:e.proxy(this.processAjaxResults,this)});var o=this.get("field");n=acf.applyFilters("select2_args",n,t,this.data,o||!1,this),s.select2(n);var r=s.select2("container"),c=e.proxy(this.getOption,this);if(n.multiple){var l=r.find("ul");l.sortable({stop:function(){l.find(".select2-search-choice").each(function(){var i=e(this).data("select2Data");c(i.id).detach().appendTo(t)}),t.trigger("change")}})}s.on("select2-selecting",function(i){var a=i.choice,n=c(a.id);n.length||(n=e('<option value="'+a.id+'">'+a.text+"</option>")),n.detach().appendTo(t)}),r.addClass("-acf"),acf.doAction("select2_init",t,n,this.data,o||!1,this),s.on("change",function(){var e=s.val();e.indexOf("||")&&(e=e.split("||")),t.val(e).trigger("change")}),t.hide()},mergeOptions:function(){var t=!1;e("#select2-drop .select2-result-with-children").each(function(){var i=e(this).children("ul"),a=e(this).children(".select2-result-label");if(t&&t.text()===a.text())return t.append(i.children()),void e(this).remove();t=a})},getAjaxData:function(e,t){var i={term:e,page:t},n=this.get("field");return i=acf.applyFilters("select2_ajax_data",i,this.data,this.$el,n||!1,this),a.prototype.getAjaxData.apply(this,[i])}});new acf.Model({priority:5,wait:"prepare",actions:{duplicate:"onDuplicate"},initialize:function(){var e=acf.get("locale"),t=(acf.get("rtl"),acf.get("select2L10n")),a=i();return!!t&&0!==e.indexOf("en")&&void(4==a?this.addTranslations4():3==a&&this.addTranslations3())},addTranslations4:function(){var e=acf.get("select2L10n"),t=acf.get("locale");t=t.replace("_","-");var i={errorLoading:function(){return e.load_fail},inputTooLong:function(t){var i=t.input.length-t.maximum;return i>1?e.input_too_long_n.replace("%d",i):e.input_too_long_1},inputTooShort:function(t){var i=t.minimum-t.input.length;return i>1?e.input_too_short_n.replace("%d",i):e.input_too_short_1},loadingMore:function(){return e.load_more},maximumSelected:function(t){var i=t.maximum;return i>1?e.selection_too_long_n.replace("%d",i):e.selection_too_long_1},noResults:function(){return e.matches_0},searching:function(){return e.searching}};jQuery.fn.select2.amd.define("select2/i18n/"+t,[],function(){return i})},addTranslations3:function(){var t=acf.get("select2L10n"),i=acf.get("locale");i=i.replace("_","-");var a={formatMatches:function(e){return e>1?t.matches_n.replace("%d",e):t.matches_1},formatNoMatches:function(){return t.matches_0},formatAjaxError:function(){return t.load_fail},formatInputTooShort:function(e,i){var a=i-e.length;return a>1?t.input_too_short_n.replace("%d",a):t.input_too_short_1},formatInputTooLong:function(e,i){var a=e.length-i;return a>1?t.input_too_long_n.replace("%d",a):t.input_too_long_1},formatSelectionTooBig:function(e){return e>1?t.selection_too_long_n.replace("%d",e):t.selection_too_long_1},formatLoadMore:function(){return t.load_more},formatSearching:function(){return t.searching}};e.fn.select2.locales=e.fn.select2.locales||{},e.fn.select2.locales[i]=a,e.extend(e.fn.select2.defaults,a)},onDuplicate:function(e,t){t.find(".select2-container").remove()}})}(jQuery)},3045:()=>{!function(e){const t=acf.Field.extend({type:"icon_picker",wait:"load",events:{showField:"scrollToSelectedIcon","input .acf-icon_url":"onUrlChange","click .acf-icon-picker-list-icon":"onIconClick","focus .acf-icon-picker-list-icon-radio":"onIconRadioFocus","blur .acf-icon-picker-list-icon-radio":"onIconRadioBlur","keydown .acf-icon-picker-list-icon-radio":"onIconKeyDown","input .acf-icon-list-search-input":"onIconSearch","keydown .acf-icon-list-search-input":"onIconSearchKeyDown","click .acf-icon-picker-media-library-button":"onMediaLibraryButtonClick","click .acf-icon-picker-media-library-preview":"onMediaLibraryButtonClick"},$typeInput(){return this.$('input[type="hidden"][data-hidden-type="type"]:first')},$valueInput(){return this.$('input[type="hidden"][data-hidden-type="value"]:first')},$tabButton(){return this.$(".acf-tab-button")},$selectedIcon(){return this.$(".acf-icon-picker-list-icon.active")},$selectedRadio(){return this.$(".acf-icon-picker-list-icon.active input")},$iconsList(){return this.$(".acf-icon-list:visible")},$mediaLibraryButton(){return this.$(".acf-icon-picker-media-library-button")},initialize(){this.addActions();let t={type:this.$typeInput().val(),value:this.$valueInput().val()};this.set("typeAndValue",t),e(".acf-tab-button").on("click",()=>{this.initializeIconLists(this.get("typeAndValue"))}),acf.doAction(this.get("name")+"/type_and_value_change",t),this.initializeIconLists(t),this.alignMediaLibraryTabToCurrentValue(t)},addActions(){acf.addAction(this.get("name")+"/type_and_value_change",e=>{this.alignIconListTabsToCurrentValue(e),this.alignMediaLibraryTabToCurrentValue(e),this.alignUrlTabToCurrentValue(e)})},updateTypeAndValue(e,t){const i={type:e,value:t};acf.val(this.$typeInput(),e),acf.val(this.$valueInput(),t),acf.doAction(this.get("name")+"/type_and_value_change",i),this.set("typeAndValue",i)},scrollToSelectedIcon(){const e=this.$selectedIcon();if(0===e.length)return;const t=e.closest(".acf-icon-list");t.scrollTop(0);const i=e.position().top-50;0!==i&&t.scrollTop(i)},initializeIconLists(t){const i=this.$(".acf-icon-list"),a=this;i.each(function(i){const n=e(this),s=n.data("parent-tab"),o=a.getIconsList(s)||[];a.set(s,o),a.renderIconList(n),t.type===s&&a.selectIcon(n,t.value,!1).then(()=>{a.scrollToSelectedIcon()})})},alignIconListTabsToCurrentValue(t){const i=this.$(".acf-icon-list").filter(function(){return e(this).data("parent-tab")!==t.type}),a=this;i.each(function(){a.unselectIcon(e(this))})},renderIconHTML(e,t){const i=acf.strEscape(`${this.get("name")}-${t.key}`),a=acf.strEscape(t.key),n=acf.strEscape(t.label),s=acf.strEscape(t.url);let o=`dashicons ${a}`,r="";return"dashicons"!==e&&(o=`${e} ${a}`,r=`background: center / contain url( ${s} ) no-repeat;`),`<div class="${o} acf-icon-picker-list-icon" role="radio" data-icon="${a}" style="${r}" title="${n}">\n\t\t\t\t<label for="${i}">${n}</label>\n\t\t\t\t<input id="${i}" type="radio" class="acf-icon-picker-list-icon-radio" name="acf-icon-picker-list-icon-radio" value="${a}">\n\t\t\t</div>`},renderIconList(e){const t=e.data("parent-tab"),i=this.get(t);e.empty(),i&&i.forEach(i=>{const a=this.renderIconHTML(t,i);e.append(a)})},getIconsList(e){let t;if("dashicons"===e){const e=acf.get("iconPickeri10n")||[];t=Object.entries(e).map(([e,t])=>({key:e,label:t}))}else{const i=this.$(`.acf-icon-list[data-parent-tab="${e}"]`);if(0!==i.length){const e=i.data("icons");t=Array.isArray(e)?e:[]}}return t},getIconsBySearch(e,t){const i=e.toLowerCase();return this.getIconsList(t).filter(function(e){return e.label.toLowerCase().indexOf(i)>-1})},selectIcon(e,t,i=!0){this.set("selectedIcon",t);const a=e.find(`.acf-icon-picker-list-icon[data-icon="${t}"]`);a.addClass("active");const n=a.find("input"),s=n.prop("checked",!0).promise();return i&&n.trigger("focus"),this.updateTypeAndValue(e.data("parent-tab"),t),s},unselectIcon(e){e.find(".acf-icon-picker-list-icon").removeClass("active"),this.set("selectedIcon",!1)},onIconRadioFocus(e){const t=e.target.value,i=this.$(e.target).closest(".acf-icon-picker-tabs"),a=i.find(".acf-icon-list");i.find('.acf-icon-picker-list-icon[data-icon="'+t+'"]').addClass("focus"),this.get("selectedIcon")!==t&&(this.unselectIcon(a),this.selectIcon(a,t))},onIconRadioBlur(e){this.$(e.target).parent().removeClass("focus")},onIconClick(e){e.preventDefault();const t=this.$(e.target).closest(".acf-icon-list"),i=this.$(e.target).find("input").val();t.find('.acf-icon-picker-list-icon[data-icon="'+i+'"]').find("input").prop("checked",!0).trigger("focus")},onIconSearch(e){const t=this.$(e.target).closest(".acf-icon-picker-tabs"),i=t.find(".acf-icon-list"),a=t.data("tab"),n=e.target.value,s=this.getIconsBySearch(n,a);if(s.length>0||!n)this.set(a,s),t.find(".acf-icon-list-empty").hide(),t.find(".acf-icon-list ").show(),this.renderIconList(i),wp.a11y.speak(acf.get("iconPickerA11yStrings").newResultsFoundForSearchTerm,"polite");else{const e=n.length>30?n.substring(0,30)+"&hellip;":n;t.find(".acf-icon-list ").hide(),t.find(".acf-icon-list-empty").find(".acf-invalid-icon-list-search-term").text(e),t.find(".acf-icon-list-empty").css("display","flex"),t.find(".acf-icon-list-empty").show(),wp.a11y.speak(acf.get("iconPickerA11yStrings").noResultsForSearchTerm,"polite")}},onIconSearchKeyDown(e){13===e.which&&e.preventDefault()},onIconKeyDown(e){13===e.which&&e.preventDefault()},alignMediaLibraryTabToCurrentValue(e){const t=e.type,i=e.value;if("media_library"!==t&&"dashicons"!==t&&this.$(".acf-icon-picker-media-library-preview").hide(),"media_library"===t){const e=this.get("mediaLibraryPreviewUrl");this.$(".acf-icon-picker-media-library-preview-img img").attr("src",e),this.$(".acf-icon-picker-media-library-preview-dashicon").hide(),this.$(".acf-icon-picker-media-library-preview-img").show(),this.$(".acf-icon-picker-media-library-preview").show()}"dashicons"===t&&(this.$(".acf-icon-picker-media-library-preview-dashicon .dashicons").attr("class","dashicons "+i),this.$(".acf-icon-picker-media-library-preview-img").hide(),this.$(".acf-icon-picker-media-library-preview-dashicon").show(),this.$(".acf-icon-picker-media-library-preview").show())},async onMediaLibraryButtonClick(e){e.preventDefault(),await this.selectAndReturnAttachment().then(e=>{this.set("mediaLibraryPreviewUrl",e.attributes.url),this.updateTypeAndValue("media_library",e.id)})},selectAndReturnAttachment(){return new Promise(e=>{acf.newMediaPopup({mode:"select",type:"image",title:acf.__("Select Image"),field:this.get("key"),multiple:!1,library:"all",allowedTypes:"image",select:e})})},alignUrlTabToCurrentValue(e){"url"!==e.type&&this.$(".acf-icon_url").val("")},onUrlChange(e){const t=e.target.value;this.updateTypeAndValue("url",t)}});acf.registerFieldType(t)}(jQuery)},3284:()=>{var e,t;e=jQuery,t=acf.Field.extend({type:"taxonomy",data:{ftype:"select"},select2:!1,wait:"load",events:{'click a[data-name="add"]':"onClickAdd",'click input[type="radio"]':"onClickRadio",removeField:"onRemove"},$control:function(){return this.$(".acf-taxonomy-field")},$input:function(){return this.getRelatedPrototype().$input.apply(this,arguments)},getRelatedType:function(){var e=this.get("ftype");return"multi_select"==e&&(e="select"),e},getRelatedPrototype:function(){return acf.getFieldType(this.getRelatedType()).prototype},getValue:function(){return this.getRelatedPrototype().getValue.apply(this,arguments)},setValue:function(){return this.getRelatedPrototype().setValue.apply(this,arguments)},initialize:function(){this.getRelatedPrototype().initialize.apply(this,arguments)},onRemove:function(){var e=this.getRelatedPrototype();e.onRemove&&e.onRemove.apply(this,arguments)},onClickAdd:function(t,i){var a=this,n=!1,s=!1,o=!1,r=!1,c=!1,l=!1,d=function(e){n.loading(!1),n.content(e),s=n.$("form"),o=n.$('input[name="term_name"]'),r=n.$('select[name="term_parent"]'),c=n.$(".acf-submit-button"),o.trigger("focus"),n.on("submit","form",u)},u=function(t,i){if(t.preventDefault(),t.stopImmediatePropagation(),""===o.val())return o.trigger("focus"),!1;acf.startButtonLoading(c);var n={action:"acf/fields/taxonomy/add_term",field_key:a.get("key"),nonce:a.get("nonce"),term_name:o.val(),term_parent:r.length?r.val():0};e.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(n),type:"post",dataType:"json",success:f})},f=function(e){acf.stopButtonLoading(c),l&&l.remove(),acf.isAjaxSuccess(e)?(o.val(""),p(e.data),l=acf.newNotice({type:"success",text:acf.getAjaxMessage(e),target:s,timeout:2e3,dismiss:!1})):l=acf.newNotice({type:"error",text:acf.getAjaxError(e),target:s,timeout:2e3,dismiss:!1}),o.trigger("focus")},p=function(t){var i=e('<option value="'+t.term_id+'">'+t.term_label+"</option>");t.term_parent?r.children('option[value="'+t.term_parent+'"]').after(i):r.append(i),acf.getFields({type:"taxonomy"}).map(function(e){e.get("taxonomy")==a.get("taxonomy")&&e.appendTerm(t)}),a.selectTerm(t.term_id)};!function(){n=acf.newPopup({title:i.attr("title"),loading:!0,width:"300px"});var t={action:"acf/fields/taxonomy/add_term",field_key:a.get("key"),nonce:a.get("nonce")};e.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(t),type:"post",dataType:"html",success:d})}()},appendTerm:function(e){"select"==this.getRelatedType()?this.appendTermSelect(e):this.appendTermCheckbox(e)},appendTermSelect:function(e){this.select2.addOption({id:e.term_id,text:e.term_label})},appendTermCheckbox:function(t){var i=this.$("[name]:first").attr("name"),a=this.$("ul:first");"checkbox"==this.getRelatedType()&&(i+="[]");var n=e(['<li data-id="'+t.term_id+'">',"<label>",'<input type="'+this.get("ftype")+'" value="'+t.term_id+'" name="'+i+'" /> ',"<span>"+t.term_name+"</span>","</label>","</li>"].join(""));if(t.term_parent){var s=a.find('li[data-id="'+t.term_parent+'"]');(a=s.children("ul")).exists()||(a=e('<ul class="children acf-bl"></ul>'),s.append(a))}a.append(n)},selectTerm:function(e){"select"==this.getRelatedType()?this.select2.selectOption(e):this.$('input[value="'+e+'"]').prop("checked",!0).trigger("change")},onClickRadio:function(e,t){var i=t.parent("label"),a=i.hasClass("selected");this.$(".selected").removeClass("selected"),i.addClass("selected"),this.get("allow_null")&&a&&(i.removeClass("selected"),t.prop("checked",!1).trigger("change"))}}),acf.registerFieldType(t)},3623:()=>{var e;jQuery,e=acf.Field.extend({type:"color_picker",wait:"load",events:{duplicateField:"onDuplicate"},$control:function(){return this.$(".acf-color-picker")},$input:function(){return this.$('input[type="hidden"]')},$inputText:function(){return this.$('input[type="text"]')},setValue:function(e){acf.val(this.$input(),e),this.$inputText().iris("color",e)},initialize:function(){var e=this.$input(),t=this.$inputText(),i=function(i){setTimeout(function(){acf.val(e,t.val())},1)},a={defaultColor:!1,palettes:!0,hide:!0,change:i,clear:i};a=acf.applyFilters("color_picker_args",a,this),t.wpColorPicker(a)},onDuplicate:function(e,t,i){$colorPicker=i.find(".wp-picker-container"),$inputText=i.find('input[type="text"]'),$colorPicker.replaceWith($inputText)}}),acf.registerFieldType(e)},3858:()=>{!function(e){var t="conditional_logic",i=(new acf.Model({id:"conditionsManager",priority:20,actions:{new_field:"onNewField"},onNewField:function(e){e.has("conditions")&&e.getConditions().render()}}),function(t,i){var a=acf.getFields({key:i,sibling:t.$el,suppressFilters:!0});return a.length||(a=acf.getFields({key:i,parent:t.$el.parent(),suppressFilters:!0})),!a.length&&e(".acf-field-settings").length&&(a=acf.getFields({key:i,parent:t.$el.parents(".acf-field-settings:first"),suppressFilters:!0})),!a.length&&e("#acf-basic-settings").length&&(a=acf.getFields({key:i,parent:e("#acf-basic-settings"),suppressFilters:!0})),!!a.length&&a[0]});acf.Field.prototype.getField=function(e){var t=i(this,e);if(t)return t;for(var a=this.parents(),n=0;n<a.length;n++)if(t=i(a[n],e))return t;return!1},acf.Field.prototype.getConditions=function(){return this.conditions||(this.conditions=new a(this)),this.conditions};var a=acf.Model.extend({id:"Conditions",data:{field:!1,timeStamp:!1,groups:[]},setup:function(e){this.data.field=e;var t=e.get("conditions");t instanceof Array?t[0]instanceof Array?t.map(function(e,t){this.addRules(e,t)},this):this.addRules(t):this.addRule(t)},change:function(e){if(this.get("timeStamp")===e.timeStamp)return!1;this.set("timeStamp",e.timeStamp,!0),this.render()},render:function(){return this.calculate()?this.show():this.hide()},show:function(){return this.get("field").showEnable(this.cid,t)},hide:function(){return this.get("field").hideDisable(this.cid,t)},calculate:function(){var e=!1;return this.getGroups().map(function(t){e||t.filter(function(e){return e.calculate()}).length==t.length&&(e=!0)}),e},hasGroups:function(){return null!=this.data.groups},getGroups:function(){return this.data.groups},addGroup:function(){var e=[];return this.data.groups.push(e),e},hasGroup:function(e){return null!=this.data.groups[e]},getGroup:function(e){return this.data.groups[e]},removeGroup:function(e){return this.data.groups[e].delete,this},addRules:function(e,t){e.map(function(e){this.addRule(e,t)},this)},addRule:function(e,t){var i;t=t||0,i=this.hasGroup(t)?this.getGroup(t):this.addGroup();var a=acf.newCondition(e,this);if(!a)return!1;i.push(a)},hasRule:function(){},getRule:function(e,t){return e=e||0,t=t||0,this.data.groups[t][e]},removeRule:function(){}})}(jQuery)},4750:()=>{!function(e){acf.newCompatibility=function(e,t){return(t=t||{}).__proto__=e.__proto__,e.__proto__=t,e.compatibility=t,t},acf.getCompatibility=function(e){return e.compatibility||null};var t=acf.newCompatibility(acf,{l10n:{},o:{},fields:{},update:acf.set,add_action:acf.addAction,remove_action:acf.removeAction,do_action:acf.doAction,add_filter:acf.addFilter,remove_filter:acf.removeFilter,apply_filters:acf.applyFilters,parse_args:acf.parseArgs,disable_el:acf.disable,disable_form:acf.disable,enable_el:acf.enable,enable_form:acf.enable,update_user_setting:acf.updateUserSetting,prepare_for_ajax:acf.prepareForAjax,is_ajax_success:acf.isAjaxSuccess,remove_el:acf.remove,remove_tr:acf.remove,str_replace:acf.strReplace,render_select:acf.renderSelect,get_uniqid:acf.uniqid,serialize_form:acf.serialize,esc_html:acf.strEscape,str_sanitize:acf.strSanitize});t._e=function(e,t){e=e||"";var i=(t=t||"")?e+"."+t:e,a={"image.select":"Select Image","image.edit":"Edit Image","image.update":"Update Image"};if(a[i])return acf.__(a[i]);var n=this.l10n[e]||"";return t&&(n=n[t]||""),n},t.get_selector=function(t){var i=".acf-field";if(!t)return i;if(e.isPlainObject(t)){if(e.isEmptyObject(t))return i;for(var a in t){t=t[a];break}}return i+="-"+t,i=acf.strReplace("_","-",i),acf.strReplace("field-field-","field-",i)},t.get_fields=function(e,t,i){var a={is:e||"",parent:t||!1,suppressFilters:i||!1};return a.is&&(a.is=this.get_selector(a.is)),acf.findFields(a)},t.get_field=function(e,t){var i=this.get_fields.apply(this,arguments);return!!i.length&&i.first()},t.get_closest_field=function(e,t){return e.closest(this.get_selector(t))},t.get_field_wrap=function(e){return e.closest(this.get_selector())},t.get_field_key=function(e){return e.data("key")},t.get_field_type=function(e){return e.data("type")},t.get_data=function(e,t){return acf.parseArgs(e.data(),t)},t.maybe_get=function(e,t,i){void 0===i&&(i=null),keys=String(t).split(".");for(var a=0;a<keys.length;a++){if(!e.hasOwnProperty(keys[a]))return i;e=e[keys[a]]}return e};var i=function(e){return e instanceof acf.Field?e.$el:e},a=function(t){return function(){if(arguments.length)var a=function(e){return acf.arrayArgs(e).map(i)}(arguments);else a=[e(document)];return t.apply(this,a)}};t.add_action=function(e,i,n,s){var o=e.split(" "),r=o.length;if(r>1){for(var c=0;c<r;c++)e=o[c],t.add_action.apply(this,arguments);return this}return i=a(i),acf.addAction.apply(this,arguments)},t.add_filter=function(e,t,i,n){return t=a(t),acf.addFilter.apply(this,arguments)},t.model={actions:{},filters:{},events:{},extend:function(t){var i=e.extend({},this,t);return e.each(i.actions,function(e,t){i._add_action(e,t)}),e.each(i.filters,function(e,t){i._add_filter(e,t)}),e.each(i.events,function(e,t){i._add_event(e,t)}),i},_add_action:function(e,t){var i=e.split(" "),a=(e=i[0]||"",i[1]||10);acf.add_action(e,this[t],a,this)},_add_filter:function(e,t){var i=e.split(" "),a=(e=i[0]||"",i[1]||10);acf.add_filter(e,this[t],a,this)},_add_event:function(t,i){var a=this,n=t.indexOf(" "),s=n>0?t.substr(0,n):t,o=n>0?t.substr(n+1):"",r=function(t){t.$el=e(this),acf.field_group&&(t.$field=t.$el.closest(".acf-field-object")),"function"==typeof a.event&&(t=a.event(t)),a[i].apply(a,arguments)};o?e(document).on(s,o,r):e(document).on(s,r)},get:function(e,t){return t=t||null,void 0!==this[e]&&(t=this[e]),t},set:function(e,t){return this[e]=t,"function"==typeof this["_set_"+e]&&this["_set_"+e].apply(this),this}},t.field=acf.model.extend({type:"",o:{},$field:null,_add_action:function(e,t){var i=this;e=e+"_field/type="+i.type,acf.add_action(e,function(e){i.set("$field",e),i[t].apply(i,arguments)})},_add_filter:function(e,t){var i=this;e=e+"_field/type="+i.type,acf.add_filter(e,function(e){i.set("$field",e),i[t].apply(i,arguments)})},_add_event:function(t,i){var a=this,n=t.substr(0,t.indexOf(" ")),s=t.substr(t.indexOf(" ")+1),o=acf.get_selector(a.type);e(document).on(n,o+" "+s,function(t){var n=e(this),s=acf.get_closest_field(n,a.type);s.length&&(s.is(a.$field)||a.set("$field",s),t.$el=n,t.$field=s,a[i].apply(a,[t]))})},_set_$field:function(){"function"==typeof this.focus&&this.focus()},doFocus:function(e){return this.set("$field",e)}}),acf.newCompatibility(acf.validation,{remove_error:function(e){acf.getField(e).removeError()},add_warning:function(e,t){acf.getField(e).showNotice({text:t,type:"warning",timeout:1e3})},fetch:acf.validateForm,enableSubmit:acf.enableSubmit,disableSubmit:acf.disableSubmit,showSpinner:acf.showSpinner,hideSpinner:acf.hideSpinner,unlockForm:acf.unlockForm,lockForm:acf.lockForm}),t.tooltip={tooltip:function(e,t){return acf.newTooltip({text:e,target:t}).$el},temp:function(e,t){acf.newTooltip({text:e,target:t,timeout:250})},confirm:function(e,t,i,a,n){acf.newTooltip({confirm:!0,text:i,target:e,confirm:function(){t(!0)},cancel:function(){t(!1)}})},confirm_remove:function(e,t){acf.newTooltip({confirmRemove:!0,target:e,confirm:function(){t(!0)},cancel:function(){t(!1)}})}},t.media=new acf.Model({activeFrame:!1,actions:{new_media_popup:"onNewMediaPopup"},frame:function(){return this.activeFrame},onNewMediaPopup:function(e){this.activeFrame=e.frame},popup:function(e){return e.mime_types&&(e.allowedTypes=e.mime_types),e.id&&(e.attachment=e.id),acf.newMediaPopup(e).frame}}),t.select2={init:function(e,t,i){return t.allow_null&&(t.allowNull=t.allow_null),t.ajax_action&&(t.ajaxAction=t.ajax_action),i&&(t.field=acf.getField(i)),acf.newSelect2(e,t)},destroy:function(e){return acf.getInstance(e).destroy()}},t.postbox={render:function(e){return e.edit_url&&(e.editLink=e.edit_url),e.edit_title&&(e.editTitle=e.edit_title),acf.newPostbox(e)}},acf.newCompatibility(acf.screen,{update:function(){return this.set.apply(this,arguments)},fetch:acf.screen.check}),t.ajax=acf.screen}(jQuery)},5338:()=>{!function(e,t){var i=[];acf.Field=acf.Model.extend({type:"",eventScope:".acf-field",wait:"ready",setup:function(e){this.$el=e,this.inherit(e),this.inherit(this.$control())},val:function(e){return e!==t?this.setValue(e):this.prop("disabled")?null:this.getValue()},getValue:function(){return this.$input().val()},setValue:function(e){return acf.val(this.$input(),e)},__:function(e){return acf._e(this.type,e)},$control:function(){return!1},$input:function(){return this.$("[name]:first")},$inputWrap:function(){return this.$(".acf-input:first")},$labelWrap:function(){return this.$(".acf-label:first")},getInputName:function(){return this.$input().attr("name")||""},parent:function(){var e=this.parents();return!!e.length&&e[0]},parents:function(){var e=this.$el.parents(".acf-field");return acf.getFields(e)},show:function(e,t){var i=acf.show(this.$el,e);return i&&(this.prop("hidden",!1),acf.doAction("show_field",this,t),"conditional_logic"===t&&this.setFieldSettingsLastVisible()),i},hide:function(e,t){var i=acf.hide(this.$el,e);return i&&(this.prop("hidden",!0),acf.doAction("hide_field",this,t),"conditional_logic"===t&&this.setFieldSettingsLastVisible()),i},setFieldSettingsLastVisible:function(){var e=this.$el.parents(".acf-field-settings-main");if(e.length){var t=e.find(".acf-field");t.removeClass("acf-last-visible"),t.not(".acf-hidden").last().addClass("acf-last-visible")}},enable:function(e,t){var i=acf.enable(this.$el,e);return i&&(this.prop("disabled",!1),acf.doAction("enable_field",this,t)),i},disable:function(e,t){var i=acf.disable(this.$el,e);return i&&(this.prop("disabled",!0),acf.doAction("disable_field",this,t)),i},showEnable:function(e,t){return this.enable.apply(this,arguments),this.show.apply(this,arguments)},hideDisable:function(e,t){return this.disable.apply(this,arguments),this.hide.apply(this,arguments)},showNotice:function(e){"object"!=typeof e&&(e={text:e}),this.notice&&this.notice.remove(),e.target=this.$inputWrap(),this.notice=acf.newNotice(e)},removeNotice:function(e){this.notice&&(this.notice.away(e||0),this.notice=!1)},showError:function(i,a="before"){this.$el.addClass("acf-error"),i!==t&&this.showNotice({text:i,type:"error",dismiss:!1,location:a}),acf.doAction("invalid_field",this),this.$el.one("focus change","input, select, textarea",e.proxy(this.removeError,this))},removeError:function(){this.$el.removeClass("acf-error"),this.removeNotice(250),acf.doAction("valid_field",this)},trigger:function(e,t,i){return"invalidField"==e&&(i=!0),acf.Model.prototype.trigger.apply(this,[e,t,i])}}),acf.newField=function(e){var t=e.data("type"),i=a(t),n=new(acf.models[i]||acf.Field)(e);return acf.doAction("new_field",n),n};var a=function(e){return acf.strPascalCase(e||"")+"Field"};acf.registerFieldType=function(e){var t=e.prototype.type,n=a(t);acf.models[n]=e,i.push(t)},acf.getFieldType=function(e){var t=a(e);return acf.models[t]||!1},acf.getFieldTypes=function(e){e=acf.parseArgs(e,{category:""});var t=[];return i.map(function(i){var a=acf.getFieldType(i),n=a.prototype;e.category&&n.category!==e.category||t.push(a)}),t}}(jQuery)},5593:()=>{!function(e){var t=0,i=acf.Field.extend({type:"accordion",wait:"",$control:function(){return this.$(".acf-fields:first")},initialize:function(){if(!this.$el.hasClass("acf-accordion")&&!this.$el.is("td")){if(this.get("endpoint"))return this.remove();var i=this.$el,n=this.$labelWrap(),s=this.$inputWrap(),o=this.$control(),r=s.children(".description");if(r.length&&n.append(r),this.$el.is("tr")){var c=this.$el.closest("table"),l=e('<div class="acf-accordion-title"/>'),d=e('<div class="acf-accordion-content"/>'),u=e('<table class="'+c.attr("class")+'"/>'),f=e("<tbody/>");l.append(n.html()),u.append(f),d.append(u),s.append(l),s.append(d),n.remove(),o.remove(),s.attr("colspan",2),n=l,s=d,o=f}i.addClass("acf-accordion"),n.addClass("acf-accordion-title"),s.addClass("acf-accordion-content"),t++,this.get("multi_expand")&&i.attr("multi-expand",1);var p=acf.getPreference("this.accordions")||[];void 0!==p[t-1]&&this.set("open",p[t-1]),this.get("open")&&(i.addClass("-open"),s.css("display","block")),n.prepend(a.iconHtml({open:this.get("open")}));var h=i.parent();o.addClass(h.hasClass("-left")?"-left":""),o.addClass(h.hasClass("-clear")?"-clear":""),o.append(i.nextUntil(".acf-field-accordion",".acf-field")),o.removeAttr("data-open data-multi_expand data-endpoint")}}});acf.registerFieldType(i);var a=new acf.Model({actions:{unload:"onUnload"},events:{"click .acf-accordion-title":"onClick","invalidField .acf-accordion":"onInvalidField"},isOpen:function(e){return e.hasClass("-open")},toggle:function(e){this.isOpen(e)?this.close(e):this.open(e)},iconHtml:function(e){return acf.isGutenberg()?e.open?'<svg width="24px" height="24px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" class="acf-accordion-icon components-panel__arrow" aria-hidden="true" focusable="false"><path d="M6.5 12.4L12 8l5.5 4.4-.9 1.2L12 10l-4.5 3.6-1-1.2z"></path></svg>':'<svg width="24px" height="24px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" class=" acf-accordion-icon components-panel__arrow" aria-hidden="true" focusable="false"><path d="M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"></path></svg>':e.open?'<i class="acf-accordion-icon dashicons dashicons-arrow-down"></i>':'<i class="acf-accordion-icon dashicons dashicons-arrow-right"></i>'},open:function(t){var i=acf.isGutenberg()?0:300;t.find(".acf-accordion-content:first").slideDown(i).css("display","block"),t.find(".acf-accordion-icon:first").replaceWith(this.iconHtml({open:!0})),t.addClass("-open"),acf.doAction("show",t),t.attr("multi-expand")||t.siblings(".acf-accordion.-open").each(function(){a.close(e(this))})},close:function(e){var t=acf.isGutenberg()?0:300;e.find(".acf-accordion-content:first").slideUp(t),e.find(".acf-accordion-icon:first").replaceWith(this.iconHtml({open:!1})),e.removeClass("-open"),acf.doAction("hide",e)},onClick:function(e,t){e.preventDefault(),this.toggle(t.parent())},onInvalidField:function(e,t){this.busy||(this.busy=!0,this.setTimeout(function(){this.busy=!1},1e3),this.open(t))},onUnload:function(t){var i=[];e(".acf-accordion").each(function(){var t=e(this).hasClass("-open")?1:0;i.push(t)}),i.length&&acf.setPreference("this.accordions",i)}})}(jQuery)},5848:()=>{!function(e){var t="tab",i=acf.Field.extend({type:"tab",wait:"",tabs:!1,tab:!1,events:{duplicateField:"onDuplicate"},findFields:function(){let e;switch(this.get("key")){case"acf_field_settings_tabs":e=".acf-field-settings-main";break;case"acf_field_group_settings_tabs":e=".field-group-settings-tab";break;case"acf_browse_fields_tabs":e=".acf-field-types-tab";break;case"acf_icon_picker_tabs":e=".acf-icon-picker-tabs";break;case"acf_post_type_tabs":e=".acf-post-type-advanced-settings";break;case"acf_taxonomy_tabs":e=".acf-taxonomy-advanced-settings";break;case"acf_ui_options_page_tabs":e=".acf-ui-options-page-advanced-settings";break;default:e=".acf-field"}return this.$el.nextUntil(".acf-field-tab",e)},getFields:function(){return acf.getFields(this.findFields())},findTabs:function(){return this.$el.prevAll(".acf-tab-wrap:first")},findTab:function(){return this.$(".acf-tab-button")},initialize:function(){if(this.$el.is("td"))return this.events={},!1;var e=this.findTabs(),t=this.findTab(),i=acf.parseArgs(t.data(),{endpoint:!1,placement:"",before:this.$el});!e.length||i.endpoint?this.tabs=new n(i):this.tabs=e.data("acf"),this.tab=this.tabs.addTab(t,this)},isActive:function(){return this.tab.isActive()},showFields:function(){this.getFields().map(function(e){e.show(this.cid,t),e.hiddenByTab=!1},this)},hideFields:function(){this.getFields().map(function(e){e.hide(this.cid,t),e.hiddenByTab=this.tab},this)},show:function(e){var t=acf.Field.prototype.show.apply(this,arguments);return t&&(this.tab.show(),this.tabs.refresh()),t},hide:function(e){var t=acf.Field.prototype.hide.apply(this,arguments);return t&&(this.tab.hide(),this.isActive()&&this.tabs.reset()),t},enable:function(e){this.getFields().map(function(e){e.enable(t)})},disable:function(e){this.getFields().map(function(e){e.disable(t)})},onDuplicate:function(e,t,i){this.isActive()&&i.prevAll(".acf-tab-wrap:first").remove()}});acf.registerFieldType(i);var a=0,n=acf.Model.extend({tabs:[],active:!1,actions:{refresh:"onRefresh",close_field_object:"onCloseFieldObject"},data:{before:!1,placement:"top",index:0,initialized:!1},setup:function(t){e.extend(this.data,t),this.tabs=[],this.active=!1;var i=this.get("placement"),n=this.get("before"),s=n.parent();if("left"==i&&s.hasClass("acf-fields")&&s.addClass("-sidebar"),n.is("tr"))this.$el=e('<tr class="acf-tab-wrap"><td colspan="2"><ul class="acf-hl acf-tab-group"></ul></td></tr>');else{let t="acf-hl acf-tab-group";"acf_field_settings_tabs"===this.get("key")&&(t="acf-field-settings-tab-bar"),this.$el=e('<div class="acf-tab-wrap -'+i+'"><ul class="'+t+'"></ul></div>')}n.before(this.$el),this.set("index",a,!0),a++},initializeTabs:function(){if("acf_field_settings_tabs"!==this.get("key")||!e("#acf-field-group-fields").hasClass("hide-tabs")){var t=!1,i=acf.getPreference("this.tabs")||!1;if(i){var a=i[this.get("index")];this.tabs[a]&&this.tabs[a].isVisible()&&(t=this.tabs[a])}!t&&this.data.defaultTab&&this.data.defaultTab.isVisible()&&(t=this.data.defaultTab),t||(t=this.getVisible().shift()),t?this.selectTab(t):this.closeTabs(),this.set("initialized",!0)}},getVisible:function(){return this.tabs.filter(function(e){return e.isVisible()})},getActive:function(){return this.active},setActive:function(e){return this.active=e},hasActive:function(){return!1!==this.active},isActive:function(e){var t=this.getActive();return t&&t.cid===e.cid},closeActive:function(){this.hasActive()&&this.closeTab(this.getActive())},openTab:function(e){this.closeActive(),e.open(),this.setActive(e)},closeTab:function(e){e.close(),this.setActive(!1)},closeTabs:function(){this.tabs.map(this.closeTab,this)},selectTab:function(e){this.tabs.map(function(t){e.cid!==t.cid&&this.closeTab(t)},this),this.openTab(e)},addTab:function(t,i){var a=e("<li>"+t.outerHTML()+"</li>"),n=t.data("settings-type");n&&a.addClass("acf-settings-type-"+n),this.$("ul").append(a);var o=new s({$el:a,field:i,group:this});return this.tabs.push(o),t.data("selected")&&(this.data.defaultTab=o),o},reset:function(){return this.closeActive(),this.refresh()},refresh:function(){if(this.hasActive())return!1;var e=this.getVisible().shift();return e&&this.openTab(e),e},onRefresh:function(){if("left"===this.get("placement")){var e=this.$el.parent(),t=this.$el.children("ul"),i=e.is("td")?"height":"min-height",a=t.position().top+t.outerHeight(!0)-1;e.css(i,a)}},onCloseFieldObject:function(e){const t=this.getVisible().find(t=>{const i=t.$el.closest("div[data-id]").data("id");if(e.data.id===i)return t});t&&setTimeout(()=>{this.openTab(t)},300)}}),s=acf.Model.extend({group:!1,field:!1,events:{"click a":"onClick"},index:function(){return this.$el.index()},isVisible:function(){return acf.isVisible(this.$el)},isActive:function(){return this.$el.hasClass("active")},open:function(){this.$el.addClass("active"),this.field.showFields()},close:function(){this.$el.removeClass("active"),this.field.hideFields()},onClick:function(e,t){e.preventDefault(),this.toggle()},toggle:function(){this.isActive()||this.group.openTab(this)}});new acf.Model({priority:50,actions:{refresh:"render",prepare:"render",append:"render",unload:"onUnload",show:"render",invalid_field:"onInvalidField"},findTabs:function(){return e(".acf-tab-wrap")},getTabs:function(){return acf.getInstances(this.findTabs())},render:function(e){this.getTabs().map(function(e){e.get("initialized")||e.initializeTabs()})},onInvalidField:function(e){this.busy||e.hiddenByTab&&(e.hiddenByTab.toggle(),this.busy=!0,this.setTimeout(function(){this.busy=!1},100))},onUnload:function(){var e=[];this.getTabs().map(function(t){if(t.$el.children(".acf-field-settings-tab-bar").length||t.$el.parents("#acf-advanced-settings.postbox").length)return!0;var i=t.hasActive()?t.getActive().index():0;e.push(i)}),e.length&&acf.setPreference("this.tabs",e)}})}(jQuery)},5915:()=>{var e,t;e=jQuery,t=acf.Field.extend({type:"link",events:{'click a[data-name="add"]':"onClickEdit",'click a[data-name="edit"]':"onClickEdit",'click a[data-name="remove"]':"onClickRemove","change .link-node":"onChange"},$control:function(){return this.$(".acf-link")},$node:function(){return this.$(".link-node")},getValue:function(){var e=this.$node();return!!e.attr("href")&&{title:e.html(),url:e.attr("href"),target:e.attr("target")}},setValue:function(e){e=acf.parseArgs(e,{title:"",url:"",target:""});var t=this.$control(),i=this.$node();t.removeClass("-value -external"),e.url&&t.addClass("-value"),"_blank"===e.target&&t.addClass("-external"),this.$(".link-title").html(e.title),this.$(".link-url").attr("href",e.url).text(e.url),i.html(e.title),i.attr("href",e.url),i.attr("target",e.target),this.$(".input-title").val(e.title),this.$(".input-target").val(e.target),this.$(".input-url").val(e.url).trigger("change")},onClickEdit:function(e,t){acf.wpLink.open(this.$node())},onClickRemove:function(e,t){this.setValue(!1)},onChange:function(e,t){var i=this.getValue();this.setValue(i)}}),acf.registerFieldType(t),acf.wpLink=new acf.Model({getNodeValue:function(){var e=this.get("node");return{title:acf.decode(e.html()),url:e.attr("href"),target:e.attr("target")}},setNodeValue:function(e){var t=this.get("node");t.text(e.title),t.attr("href",e.url),t.attr("target",e.target),t.trigger("change")},getInputValue:function(){return{title:e("#wp-link-text").val(),url:e("#wp-link-url").val(),target:e("#wp-link-target").prop("checked")?"_blank":""}},setInputValue:function(t){e("#wp-link-text").val(t.title),e("#wp-link-url").val(t.url),e("#wp-link-target").prop("checked","_blank"===t.target)},open:function(t){this.on("wplink-open","onOpen"),this.on("wplink-close","onClose"),this.set("node",t);var i=e('<textarea id="acf-link-textarea" style="display:none;"></textarea>');e("body").append(i);var a=this.getNodeValue();wpLink.open("acf-link-textarea",a.url,a.title,null)},onOpen:function(){e("#wp-link-wrap").addClass("has-text-field");var t=this.getNodeValue();this.setInputValue(t),t.url&&wpLinkL10n&&e("#wp-link-submit").val(wpLinkL10n.update)},close:function(){wpLink.close()},onClose:function(){if(!this.has("node"))return!1;var t=e("#wp-link-submit");if(t.is(":hover")||t.is(":focus")){var i=this.getInputValue();this.setNodeValue(i)}this.off("wplink-open"),this.off("wplink-close"),e("#acf-link-textarea").remove(),this.set("node",null)}})},5942:()=>{var e;jQuery,e=acf.Field.extend({type:"url",events:{'keyup input[type="url"]':"onkeyup"},$control:function(){return this.$(".acf-input-wrap")},$input:function(){return this.$('input[type="url"]')},initialize:function(){this.render()},isValid:function(){var e=this.val();return!!e&&(-1!==e.indexOf("://")||0===e.indexOf("//"))},render:function(){this.isValid()?this.$control().addClass("-valid"):this.$control().removeClass("-valid")},onkeyup:function(e,t){this.render()}}),acf.registerFieldType(e)},6289:()=>{var e;jQuery,e=acf.Field.extend({type:"button_group",events:{'click input[type="radio"]':"onClick"},$control:function(){return this.$(".acf-button-group")},$input:function(){return this.$("input:checked")},setValue:function(e){this.$('input[value="'+e+'"]').prop("checked",!0).trigger("change")},onClick:function(e,t){var i=t.parent("label"),a=i.hasClass("selected");this.$(".selected").removeClass("selected"),i.addClass("selected"),this.get("allow_null")&&a&&(i.removeClass("selected"),t.prop("checked",!1).trigger("change"))}}),acf.registerFieldType(e)},6290:()=>{var e;jQuery,e=acf.Field.extend({type:"range",events:{'input input[type="range"]':"onChange","change input":"onChange"},$input:function(){return this.$('input[type="range"]')},$inputAlt:function(){return this.$('input[type="number"]')},setValue:function(e){this.busy=!0,acf.val(this.$input(),e),acf.val(this.$inputAlt(),this.$input().val(),!0),this.busy=!1},onChange:function(e,t){this.busy||this.setValue(t.val())}}),acf.registerFieldType(e)},6403:()=>{var e;jQuery,e=acf.Field.extend({type:"select",select2:!1,wait:"load",events:{removeField:"onRemove",duplicateField:"onDuplicate"},$input:function(){return this.$("select")},initialize:function(){var e=this.$input();if(this.inherit(e),this.get("ui")){var t=this.get("ajax_action");t||(t="acf/fields/"+this.get("type")+"/query"),this.select2=acf.newSelect2(e,{field:this,ajax:this.get("ajax"),multiple:this.get("multiple"),placeholder:this.get("placeholder"),allowNull:this.get("allow_null"),tags:this.get("create_options"),ajaxAction:t})}},onRemove:function(){this.select2&&this.select2.destroy()},onDuplicate:function(e,t,i){this.select2&&(i.find(".select2-container").remove(),i.find("select").removeClass("select2-hidden-accessible"))}}),acf.registerFieldType(e)},7509:()=>{var e,t;e=jQuery,t=acf.Field.extend({type:"relationship",events:{"keypress [data-filter]":"onKeypressFilter","change [data-filter]":"onChangeFilter","keyup [data-filter]":"onChangeFilter","click .choices-list .acf-rel-item":"onClickAdd","keypress .choices-list .acf-rel-item":"onKeypressFilter","keypress .values-list .acf-rel-item":"onKeypressFilter",'click [data-name="remove_item"]':"onClickRemove","touchstart .values-list .acf-rel-item":"onTouchStartValues"},$control:function(){return this.$(".acf-relationship")},$list:function(e){return this.$("."+e+"-list")},$listItems:function(e){return this.$list(e).find(".acf-rel-item")},$listItem:function(e,t){return this.$list(e).find('.acf-rel-item[data-id="'+t+'"]')},getValue:function(){var t=[];return this.$listItems("values").each(function(){t.push(e(this).data("id"))}),!!t.length&&t},newChoice:function(e){return["<li>",'<span tabindex="0" data-id="'+e.id+'" class="acf-rel-item">'+e.text+"</span>","</li>"].join("")},newValue:function(e){return["<li>",'<input type="hidden" name="'+this.getInputName()+'[]" value="'+e.id+'" />','<span tabindex="0" data-id="'+e.id+'" class="acf-rel-item acf-rel-item-remove">'+e.text,'<a href="#" class="acf-icon -minus small dark" data-name="remove_item"></a>',"</span>","</li>"].join("")},initialize:function(){var e=this.proxy(acf.once(function(){this.$list("values").sortable({items:"li",zIndex:9999,forceHelperSize:!0,forcePlaceholderSize:!0,scroll:!0,update:this.proxy(function(){this.$input().trigger("change")})}),this.$list("choices").scrollTop(0).on("scroll",this.proxy(this.onScrollChoices)),this.fetch()}));this.$el.one("mouseover",e),this.$el.one("focus","input",e),acf.onceInView(this.$el,e)},onScrollChoices:function(e){if(!this.get("loading")&&this.get("more")){var t=this.$list("choices"),i=Math.ceil(t.scrollTop()),a=Math.ceil(t[0].scrollHeight),n=Math.ceil(t.innerHeight()),s=this.get("paged")||1;i+n>=a&&(this.set("paged",s+1),this.fetch())}},onKeypressFilter:function(e,t){t.hasClass("acf-rel-item-add")&&13==e.which&&this.onClickAdd(e,t),t.hasClass("acf-rel-item-remove")&&13==e.which&&this.onClickRemove(e,t),13==e.which&&e.preventDefault()},onChangeFilter:function(e,t){var i=t.val(),a=t.data("filter");this.get(a)!==i&&(this.set(a,i),"s"===a&&parseInt(i)&&this.set("include",i),this.set("paged",1),t.is("select")?this.fetch():this.maybeFetch())},onClickAdd:function(e,t){var i=this.val(),a=parseInt(this.get("max"));if(t.hasClass("disabled"))return!1;if(a>0&&i&&i.length>=a)return this.showNotice({text:acf.__("Maximum values reached ( {max} values )").replace("{max}",a),type:"warning"}),!1;t.addClass("disabled");var n=this.newValue({id:t.data("id"),text:t.html()});this.$list("values").append(n),this.$input().trigger("change")},onClickRemove:function(e,t){let i;e.preventDefault(),i=t.hasClass("acf-rel-item-remove")?t:t.parent();const a=i.parent(),n=i.data("id");a.remove(),this.$listItem("choices",n).removeClass("disabled"),this.$input().trigger("change")},onTouchStartValues:function(t,i){e(this.$listItems("values")).removeClass("relationship-hover"),i.addClass("relationship-hover")},maybeFetch:function(){var e=this.get("timeout");e&&clearTimeout(e),e=this.setTimeout(this.fetch,300),this.set("timeout",e)},getAjaxData:function(){var e=this.$control().data();for(var t in e)e[t]=this.get(t);return e.action="acf/fields/relationship/query",e.field_key=this.get("key"),e.nonce=this.get("nonce"),acf.applyFilters("relationship_ajax_data",e,this)},fetch:function(){(n=this.get("xhr"))&&n.abort();var t=this.getAjaxData(),i=this.$list("choices");1==t.paged&&i.html("");var a=e('<li><i class="acf-loading"></i> '+acf.__("Loading")+"</li>");i.append(a),this.set("loading",!0);var n=e.ajax({url:acf.get("ajaxurl"),dataType:"json",type:"post",data:acf.prepareForAjax(t),context:this,success:function(t){if(!t||!t.results||!t.results.length)return this.set("more",!1),void(1==this.get("paged")&&this.$list("choices").append("<li>"+acf.__("No matches found")+"</li>"));this.set("more",t.more);var a=this.walkChoices(t.results),n=e(a),s=this.val();s&&s.length&&s.map(function(e){n.find('.acf-rel-item[data-id="'+e+'"]').addClass("disabled")}),i.append(n);var o=!1,r=!1;i.find(".acf-rel-label").each(function(){var t=e(this),i=t.siblings("ul");if(o&&o.text()==t.text())return r.append(i.children()),void e(this).parent().remove();o=t,r=i})},complete:function(){this.set("loading",!1),a.remove()}});this.set("xhr",n)},walkChoices:function(t){var i=function(t){var a="";return e.isArray(t)?t.map(function(e){a+=i(e)}):e.isPlainObject(t)&&(void 0!==t.children?(a+='<li><span class="acf-rel-label">'+acf.escHtml(t.text)+'</span><ul class="acf-bl">',a+=i(t.children),a+="</ul></li>"):a+='<li><span tabindex="0" class="acf-rel-item acf-rel-item-add" data-id="'+acf.escAttr(t.id)+'">'+acf.escHtml(t.text)+"</span></li>"),a};return i(t)}}),acf.registerFieldType(t)},7513:()=>{var e;jQuery,e=acf.models.SelectField.extend({type:"page_link"}),acf.registerFieldType(e)},8223:()=>{var e;e=jQuery,new acf.Model({priority:90,actions:{new_field:"refresh",show_field:"refresh",hide_field:"refresh",remove_field:"refresh",unmount_field:"refresh",remount_field:"refresh"},refresh:function(){acf.refresh()}}),new acf.Model({priority:1,actions:{sortstart:"onSortstart",sortstop:"onSortstop"},onSortstart:function(e){acf.doAction("unmount",e)},onSortstop:function(e){acf.doAction("remount",e)}}),new acf.Model({actions:{sortstart:"onSortstart"},onSortstart:function(t,i){t.is("tr")&&(i.html('<td style="padding:0;" colspan="'+i.children().length+'"></td>'),t.addClass("acf-sortable-tr-helper"),t.children().each(function(){e(this).width(e(this).width())}),i.height(t.height()+"px"),t.removeClass("acf-sortable-tr-helper"))}}),new acf.Model({actions:{after_duplicate:"onAfterDuplicate"},onAfterDuplicate:function(t,i){var a=[];t.find("select").each(function(t){a.push(e(this).val())}),i.find("select").each(function(t){e(this).val(a[t])})}}),new acf.Model({id:"tableHelper",priority:20,wait:"load",actions:{refresh:"renderTables"},initialize:function(){this.renderTables()},renderTables:function(t){var i=this;e(".acf-table:visible").each(function(){i.renderTable(e(this))})},renderTable:function(t){var i=t.find("> thead > tr:visible > th[data-key]"),a=t.find("> tbody > tr:visible > td[data-key]");if(!i.length||!a.length)return!1;i.each(function(t){var i=e(this),n=i.data("key"),s=a.filter('[data-key="'+n+'"]'),o=s.filter(".acf-hidden");s.removeClass("acf-empty"),s.length===o.length?acf.hide(i):(acf.show(i),o.addClass("acf-empty"))}),i.css("width","auto"),i=i.not(".acf-hidden");var n=100;i.length,i.filter("[data-width]").each(function(){var t=e(this).data("width");e(this).css("width",t+"%"),n-=t});var s=i.not("[data-width]");if(s.length){var o=n/s.length;s.css("width",o+"%"),n=0}n>0&&i.last().css("width","auto"),a.filter(".-collapsed-target").each(function(){var t=e(this);t.parent().hasClass("-collapsed")?t.attr("colspan",i.length):t.removeAttr("colspan")})}}),new acf.Model({id:"fieldsHelper",priority:30,actions:{refresh:"renderGroups"},renderGroups:function(){var t=this;e(".acf-fields:visible").each(function(){t.renderGroup(e(this))})},renderGroup:function(t){var i=0,a=0,n=e(),s=t.children(".acf-field[data-width]:visible");return!!s.length&&(t.hasClass("-left")?(s.removeAttr("data-width"),s.css("width","auto"),!1):(s.removeClass("-r0 -c0").css({"min-height":0}),s.each(function(t){var s=e(this),o=s.position(),r=Math.ceil(o.top),c=Math.ceil(o.left);n.length&&r>i&&(n.css({"min-height":a+"px"}),o=s.position(),r=Math.ceil(o.top),c=Math.ceil(o.left),i=0,a=0,n=e()),acf.get("rtl")&&(c=Math.ceil(s.parent().width()-(o.left+s.outerWidth()))),0==r?s.addClass("-r0"):0==c&&s.addClass("-c0");var l=Math.ceil(s.outerHeight())+1;a=Math.max(a,l),i=Math.max(i,r),n=n.add(s)}),void(n.length&&n.css({"min-height":a+"px"}))))}}),new acf.Model({id:"bodyClassShiftHelper",events:{keydown:"onKeyDown",keyup:"onKeyUp"},isShiftKey:function(e){return 16===e.keyCode},onKeyDown:function(t){this.isShiftKey(t)&&e("body").addClass("acf-keydown-shift")},onKeyUp:function(t){this.isShiftKey(t)&&e("body").removeClass("acf-keydown-shift")}})},8903:()=>{!function(e){var t=[];acf.Condition=acf.Model.extend({type:"",operator:"==",label:"",choiceType:"input",fieldTypes:[],data:{conditions:!1,field:!1,rule:{}},events:{change:"change",keyup:"change",enableField:"change",disableField:"change"},setup:function(t){e.extend(this.data,t)},getEventTarget:function(e,t){return e||this.get("field").$el},change:function(e,t){this.get("conditions").change(e)},match:function(e,t){return!1},calculate:function(){return this.match(this.get("rule"),this.get("field"))},choices:function(e){return'<input type="text" />'}}),acf.newCondition=function(e,t){var i=t.get("field"),a=i.getField(e.field);if(!i||!a)return!1;var n={rule:e,target:i,conditions:t,field:a},s=a.get("type"),o=e.operator;return new(acf.getConditionTypes({fieldType:s,operator:o})[0]||acf.Condition)(n)};var i=function(e){return acf.strPascalCase(e||"")+"Condition"};acf.registerConditionType=function(e){var a=e.prototype.type,n=i(a);acf.models[n]=e,t.push(a)},acf.getConditionType=function(e){var t=i(e);return acf.models[t]||!1},acf.registerConditionForFieldType=function(e,t){var i=acf.getConditionType(e);i&&i.prototype.fieldTypes.push(t)},acf.getConditionTypes=function(e){e=acf.parseArgs(e,{fieldType:"",operator:""});var i=[];return t.map(function(t){var a=acf.getConditionType(t),n=a.prototype.fieldTypes,s=a.prototype.operator;e.fieldType&&-1===n.indexOf(e.fieldType)||e.operator&&s!==e.operator||i.push(a)}),i}}(jQuery)},9213:()=>{var e,t;e=jQuery,t=acf.models.DatePickerField.extend({type:"time_picker",$control:function(){return this.$(".acf-time-picker")},initialize:function(){var e=this.$input(),t=this.$inputText(),i={timeFormat:this.get("time_format"),altField:e,altFieldTimeOnly:!1,altTimeFormat:"HH:mm:ss",showButtonPanel:!0,controlType:"select",oneLine:!0,closeText:acf.get("dateTimePickerL10n").selectText,timeOnly:!0,onClose:function(e,t,i){var a=t.dpDiv.find(".ui-datepicker-close");!e&&a.is(":hover")&&i._updateDateTime()}};i=acf.applyFilters("time_picker_args",i,this),acf.newTimePicker(t,i),acf.doAction("time_picker_init",t,i,this)}}),acf.registerFieldType(t),acf.newTimePicker=function(t,i){if(void 0===e.timepicker)return!1;i=i||{},t.timepicker(i),e("body > #ui-datepicker-div").exists()&&e("body > #ui-datepicker-div").wrap('<div class="acf-ui-datepicker" />')}},9252:()=>{var e;jQuery,e=acf.Field.extend({type:"radio",events:{'click input[type="radio"]':"onClick"},$control:function(){return this.$(".acf-radio-list")},$input:function(){return this.$("input:checked")},$inputText:function(){return this.$('input[type="text"]')},getValue:function(){var e=this.$input().val();return"other"===e&&this.get("other_choice")&&(e=this.$inputText().val()),e},onClick:function(e,t){var i=t.parent("label"),a=i.hasClass("selected"),n=t.val();this.$(".selected").removeClass("selected"),i.addClass("selected"),this.get("allow_null")&&a&&(i.removeClass("selected"),t.prop("checked",!1).trigger("change"),n=!1),this.get("other_choice")&&("other"===n?this.$inputText().prop("disabled",!1):this.$inputText().prop("disabled",!0))}}),acf.registerFieldType(e)},9400:()=>{var e;e=jQuery,acf.screen=new acf.Model({active:!0,xhr:!1,timeout:!1,wait:"load",events:{"change #page_template":"onChange","change #parent_id":"onChange","change #post-formats-select":"onChange","change .categorychecklist":"onChange","change .tagsdiv":"onChange",'change .acf-taxonomy-field[data-save="1"]':"onChange","change #product-type":"onChange"},isPost:function(){return"post"===acf.get("screen")},isUser:function(){return"user"===acf.get("screen")},isTaxonomy:function(){return"taxonomy"===acf.get("screen")},isAttachment:function(){return"attachment"===acf.get("screen")},isNavMenu:function(){return"nav_menu"===acf.get("screen")},isWidget:function(){return"widget"===acf.get("screen")},isComment:function(){return"comment"===acf.get("screen")},getPageTemplate:function(){var t=e("#page_template");return t.length?t.val():null},getPageParent:function(t,i){return(i=e("#parent_id")).length?i.val():null},getPageType:function(e,t){return this.getPageParent()?"child":"parent"},getPostType:function(){return e("#post_type").val()},getPostFormat:function(t,i){if((i=e("#post-formats-select input:checked")).length){var a=i.val();return"0"==a?"standard":a}return null},getPostCoreTerms:function(){var t={},i=acf.serialize(e(".categorydiv, .tagsdiv"));for(var a in i.tax_input&&(t=i.tax_input),i.post_category&&(t.category=i.post_category),t)acf.isArray(t[a])||(t[a]=t[a].split(/,[\s]?/));return t},getPostTerms:function(){var e=this.getPostCoreTerms();for(var t in acf.getFields({type:"taxonomy"}).map(function(t){if(t.get("save")){var i=t.val(),a=t.get("taxonomy");i&&(e[a]=e[a]||[],i=acf.isArray(i)?i:[i],e[a]=e[a].concat(i))}}),null!==(productType=this.getProductType())&&(e.product_type=[productType]),e)e[t]=acf.uniqueArray(e[t]);return e},getProductType:function(){var t=e("#product-type");return t.length?t.val():null},check:function(){if("post"===acf.get("screen")){this.xhr&&this.xhr.abort();var t=acf.parseArgs(this.data,{action:"acf/ajax/check_screen",screen:acf.get("screen"),exists:[]});this.isPost()&&(t.post_id=acf.get("post_id")),null!==(postType=this.getPostType())&&(t.post_type=postType),null!==(pageTemplate=this.getPageTemplate())&&(t.page_template=pageTemplate),null!==(pageParent=this.getPageParent())&&(t.page_parent=pageParent),null!==(pageType=this.getPageType())&&(t.page_type=pageType),null!==(postFormat=this.getPostFormat())&&(t.post_format=postFormat),null!==(postTerms=this.getPostTerms())&&(t.post_terms=postTerms),acf.getPostboxes().map(function(e){t.exists.push(e.get("key"))}),t=acf.applyFilters("check_screen_args",t),this.xhr=e.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(t),type:"post",dataType:"json",context:this,success:function(e){"post"==acf.get("screen")?this.renderPostScreen(e):"user"==acf.get("screen")&&this.renderUserScreen(e),acf.doAction("check_screen_complete",e,t)}})}},onChange:function(e,t){this.setTimeout(this.check,1)},renderPostScreen:function(t){var i=function(t,i){var a=e._data(t[0]).events;for(var n in a)for(var s=0;s<a[n].length;s++)i.on(n,a[n][s].handler)},a=function(t,i){var a=i.indexOf(t);if(-1==a)return!1;for(var n=a-1;n>=0;n--)if(e("#"+i[n]).length)return e("#"+i[n]).after(e("#"+t));for(n=a+1;n<i.length;n++)if(e("#"+i[n]).length)return e("#"+i[n]).before(e("#"+t));return!1};t.visible=[],t.hidden=[],t.results=t.results.map(function(n,s){var o=acf.getPostbox(n.id);if(acf.isGutenberg()&&"acf_after_title"==n.position&&(n.position="normal"),!o){var r=['<div class="postbox-header">','<h2 class="hndle ui-sortable-handle">',"<span>"+n.title+"</span>","</h2>",'<div class="handle-actions hide-if-no-js">','<button type="button" class="handlediv" aria-expanded="true">','<span class="screen-reader-text">'+acf.__("Toggle panel")+": "+n.title+"</span>",'<span class="toggle-indicator" aria-hidden="true"></span>',"</button>","</div>","</div>"].join("");n.classes||(n.classes="");var c=e(['<div id="'+n.id+'" class="postbox '+n.classes+'">',r,'<div class="inside">',n.html,"</div>","</div>"].join(""));if(e("#adv-settings").length){var l=e("#adv-settings .metabox-prefs"),d=e(['<label for="'+n.id+'-hide">','<input class="hide-postbox-tog" name="'+n.id+'-hide" type="checkbox" id="'+n.id+'-hide" value="'+n.id+'" checked="checked">'," "+n.title,"</label>"].join(""));i(l.find("input").first(),d.find("input")),l.append(d)}e(".postbox").length&&(i(e(".postbox .handlediv").first(),c.children(".handlediv")),i(e(".postbox .hndle").first(),c.children(".hndle"))),"side"===n.position?e("#"+n.position+"-sortables").append(c):e("#"+n.position+"-sortables").prepend(c);var u=[];if(t.results.map(function(t){n.position===t.position&&e("#"+n.position+"-sortables #"+t.id).length&&u.push(t.id)}),a(n.id,u),t.sorted)for(var f in t.sorted){let e=t.sorted[f];if("string"==typeof e&&(e=e.split(","),a(n.id,e)))break}o=acf.newPostbox(n),acf.doAction("append",c),acf.doAction("append_postbox",o)}return o.showEnable(),t.visible.push(n.id),n}),acf.getPostboxes().map(function(e){-1===t.visible.indexOf(e.get("id"))&&(e.hideDisable(),t.hidden.push(e.get("id")))}),e("#acf-style").html(t.style),acf.doAction("refresh_post_screen",t)},renderUserScreen:function(e){}}),new acf.Model({postEdits:{},wait:"prepare",initialize:function(){acf.isGutenbergPostEditor()&&(wp.data.subscribe(acf.debounce(this.onChange).bind(this)),acf.screen.getPageTemplate=this.getPageTemplate,acf.screen.getPageParent=this.getPageParent,acf.screen.getPostType=this.getPostType,acf.screen.getPostFormat=this.getPostFormat,acf.screen.getPostCoreTerms=this.getPostCoreTerms,acf.unload.disable(),this.addAction("refresh_post_screen",this.onRefreshPostScreen),wp.domReady(acf.refresh))},onChange:function(){var e=["template","parent","format"];(wp.data.select("core").getTaxonomies()||[]).map(function(t){e.push(t.rest_base)});var t=wp.data.select("core/editor").getPostEdits(),i={};e.map(function(e){void 0!==t[e]&&(i[e]=t[e])}),JSON.stringify(i)!==JSON.stringify(this.postEdits)&&(this.postEdits=i,acf.screen.check())},getPageTemplate:function(){return wp.data.select("core/editor").getEditedPostAttribute("template")},getPageParent:function(e,t){return wp.data.select("core/editor").getEditedPostAttribute("parent")},getPostType:function(){return wp.data.select("core/editor").getEditedPostAttribute("type")},getPostFormat:function(e,t){return wp.data.select("core/editor").getEditedPostAttribute("format")},getPostCoreTerms:function(){var e={};return(wp.data.select("core").getTaxonomies()||[]).map(function(t){var i=wp.data.select("core/editor").getEditedPostAttribute(t.rest_base);i&&(e[t.slug]=i)}),e},onRefreshPostScreen:function(e){var t=wp.data.select("core/edit-post"),i=wp.data.dispatch("core/edit-post"),a={};t.getActiveMetaBoxLocations().map(function(e){a[e]=t.getMetaBoxesPerLocation(e)});var n=[];for(var s in a)a[s].map(function(e){n.push(e.id)});for(var s in e.results.filter(function(e){return-1===n.indexOf(e.id)}).map(function(e,t){var i=e.position;a[i]=a[i]||[],a[i].push({id:e.id,title:e.title})}),a)a[s]=a[s].filter(function(t){return-1===e.hidden.indexOf(t.id)});i.setAvailableMetaBoxesPerLocation(a)}})},9732:()=>{var e;jQuery,e=acf.models.SelectField.extend({type:"user"}),acf.registerFieldType(e)},9938:()=>{var e;jQuery,e=acf.Field.extend({type:"wysiwyg",wait:"load",events:{"mousedown .acf-editor-wrap.delay":"onMousedown",unmountField:"disableEditor",remountField:"enableEditor",removeField:"disableEditor"},$control:function(){return this.$(".acf-editor-wrap")},$input:function(){return this.$("textarea")},getMode:function(){return this.$control().hasClass("tmce-active")?"visual":"text"},initialize:function(){this.$control().hasClass("delay")||this.initializeEditor()},initializeEditor:function(){var e=this.$control(),t=this.$input(),i={tinymce:!0,quicktags:!0,toolbar:this.get("toolbar"),mode:this.getMode(),field:this},a=t.attr("id"),n=acf.uniqueId("acf-editor-"),s=t.data(),o=t.val();if(acf.rename({target:e,search:a,replace:n,destructive:!0}),this.set("id",n,!0),this.$input().data(s).val(o),acf.tinymce.initialize(n,i),!acf.isGutenbergPostEditor())return;let r=!0;wp.data.subscribe(()=>{wp.data.select("core/edit-post").isFeatureActive("distractionFree")?r=!1:r||(r=!0,setTimeout(()=>{tinymce.get(n).destroy(),acf.tinymce.initialize(n,i)},10))})},onMousedown:function(e){e.preventDefault();var t=this.$control();t.removeClass("delay"),t.find(".acf-editor-toolbar").remove(),this.initializeEditor()},enableEditor:function(){"visual"==this.getMode()&&acf.tinymce.enable(this.get("id"))},disableEditor:function(){acf.tinymce.destroy(this.get("id"))}}),acf.registerFieldType(e)},9982:()=>{var e,t;e=jQuery,t=acf.Field.extend({type:"date_picker",events:{'blur input[type="text"]':"onBlur",duplicateField:"onDuplicate"},$control:function(){return this.$(".acf-date-picker")},$input:function(){return this.$('input[type="hidden"]')},$inputText:function(){return this.$('input[type="text"]')},initialize:function(){if(this.has("save_format"))return this.initializeCompatibility();var t=this.$input(),i=this.$inputText(),a={dateFormat:this.get("date_format"),altField:t,altFormat:"yymmdd",changeYear:!0,yearRange:"-100:+100",changeMonth:!0,showButtonPanel:!0,firstDay:this.get("first_day")};if(a=acf.applyFilters("date_picker_args",a,this),acf.newDatePicker(i,a),1===i.data("default-to-today")&&!t.val()){const n=new Date,s=e.datepicker.formatDate(a.dateFormat,n);i.val(`${s}`);const o=e.datepicker.formatDate("yymmdd",n);t.val(`${o}`)}acf.doAction("date_picker_init",i,a,this)},initializeCompatibility:function(){var e=this.$input(),t=this.$inputText();t.val(e.val());var i={dateFormat:this.get("date_format"),altField:e,altFormat:this.get("save_format"),changeYear:!0,yearRange:"-100:+100",changeMonth:!0,showButtonPanel:!0,firstDay:this.get("first_day")},a=(i=acf.applyFilters("date_picker_args",i,this)).dateFormat;i.dateFormat=this.get("save_format"),acf.newDatePicker(t,i),t.datepicker("option","dateFormat",a),acf.doAction("date_picker_init",t,i,this)},onBlur:function(){this.$inputText().val()||acf.val(this.$input(),"")},onDuplicate:function(e,t,i){i.find('input[type="text"]').removeClass("hasDatepicker").removeAttr("id")}}),acf.registerFieldType(t),new acf.Model({priority:5,wait:"ready",initialize:function(){var t=acf.get("locale"),i=acf.get("rtl"),a=acf.get("datePickerL10n");return!!a&&void 0!==e.datepicker&&(a.isRTL=i,e.datepicker.regional[t]=a,void e.datepicker.setDefaults(a))}}),acf.newDatePicker=function(t,i){if(void 0===e.datepicker)return!1;i=i||{},t.datepicker(i),e("body > #ui-datepicker-div").exists()&&e("body > #ui-datepicker-div").wrap('<div class="acf-ui-datepicker" />')}}},t={};function i(a){var n=t[a];if(void 0!==n)return n.exports;var s=t[a]={exports:{}};return e[a](s,s.exports,i),s.exports}i.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return i.d(t,{a:t}),t},i.d=(e,t)=>{for(var a in t)i.o(t,a)&&!i.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";i(5338),i(2457),i(5593),i(6289),i(774),i(3623),i(9982),i(960),i(1163),i(3045),i(2410),i(2093),i(5915),i(2237),i(9252),i(6290),i(7509),i(6403),i(5848),i(2553),i(7513),i(9732),i(3284),i(9213),i(1525),i(5942),i(9938),i(8903),i(3858),i(2747),i(963),i(993),i(1218),i(9400),i(2900),i(1087),i(2631),i(8223),i(4750)})()})();