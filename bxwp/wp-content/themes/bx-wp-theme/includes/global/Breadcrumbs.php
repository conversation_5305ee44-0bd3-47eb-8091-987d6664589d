<?php

namespace CanaisDigitais;

use <PERSON>aisD<PERSON>tais\CD_Post\CD_Post;

if (!defined('ABSPATH')) exit;

class Breadcrumbs
{
   public $trail              = [];
   public $current_term_level = 0;

   function __construct()
   {
      $this->add_home();

      if (is_category() || is_tax()) {
         $this->add_taxonomy();
      }

      if (is_single()) {
         $this->add_single_taxonomy();
      }
   }

   function add_home()
   {
      $this->trail[] = [
         'title' => esc_html__('Home', 'canais_digitais'),
         'url'   => get_site_url()
      ];
   }

   function add_taxonomy()
   {
      $term      = get_queried_object();
      $max_depth = $this->get_max_term_depth($term->term_id, $term->taxonomy);

      return $this->add_term($term, $term->taxonomy, $max_depth, false);
   }

   public function add_single_taxonomy()
   {
      global $post;

      $CD_Post  = new CD_Post($post);
      $taxonomy = 'category';
      $category = $CD_Post->get_category('', false);

      if (empty($category)) {
         return;
      }

      $term = get_term_by('term_id', $category->get_the_ID(), 'category');

      if (is_null($term)) {
         return;
      }

      $max_depth = $this->get_max_term_depth($term->term_id, $taxonomy);

      return $this->add_term($term, $taxonomy, $max_depth);
   }

   public function add_term($term, string $taxonomy, int $max_depth, bool $show_last = true)
   {
      $term = get_term($term, $taxonomy);

      if ($this->current_term_level < $max_depth && $term->parent > 0) {
         $this->add_term($term->parent, $taxonomy, $max_depth, $show_last);
      }

      $this->current_term_level++;

      $hide = $this->current_term_level === $max_depth && $show_last !== true;

      if (!$hide) {
         $this->trail[] = [
            'title' => esc_html($term->name),
            'url'   => get_term_link($term)
         ];
      }

      if ($this->current_term_level === $max_depth) {
         $this->current_term_level = 0;
      }
   }

   function get_max_term_depth(int $term_id, string $taxonomy)
   {
      $ancestors = !empty(get_ancestors($term_id, $taxonomy)) ? get_ancestors($term_id, $taxonomy) : [];
      return count($ancestors) + 1;
   }

   public function get_trail()
   {
      return $this->trail;
   }
}
