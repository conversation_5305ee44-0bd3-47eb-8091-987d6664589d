<?php

/**
 * Name: Homepage_Tabs
 * Custom Fields: títu<PERSON> da seção de eventos, descrição da seção de eventos, evento destacado, outros eventos, mostrar eventos mais recentes, post de alto impacto, mostrar todos os especialistas, escolher especialistas, artigos de especialistas, mostrar posts mais recentes de especialista, escolher artigos mais lidos, mostrar artigos mais lidos, título da seção de newsletter, título da seção de newsletter, tipo de background da seção de newsletter, cor de background da seção de newsletter, imagem destacada seção de newsletter, imagem de fundo da seção de newsletter, Escolher categoria, escolher posts da categoria, mostrar posts mais recentes da categoria, título da seção de vídeos, descrição da seção de vídeos, vídeos, material de download em destaca, seletores de ordenação de seções
 * Description: Campos personalizados da homepage.
 */

namespace CanaisDigitais\Theme_Settings;

use CanaisDigitais\Category\Utils as CategoryUtils;

if (!defined('ABSPATH')) {
   exit;
}

class Register_Homepage_Tabs
{
   function __construct()
   {
      add_action('init', [$this, 'register_pages']);

      add_action('init', [$this, 'register_homepage_fields']);
      add_filter('bx_cd_options_home_tabs', [$this, 'tab_high_impact_post'], 5);
      add_filter('bx_cd_options_home_tabs', [$this, 'tab_specialists'], 10);
      add_filter('bx_cd_options_home_tabs', [$this, 'tab_categories'], 15);
      add_filter('bx_cd_options_home_tabs', [$this, 'tab_events'], 20);
      add_filter('bx_cd_options_home_tabs', [$this, 'tab_home_videos'], 30);
      add_filter('bx_cd_options_home_tabs', [$this, 'tab_home_newsletter'], 35);
      add_filter('bx_cd_options_home_tabs', [$this, 'tab_featured_materials'], 40);
      add_filter('bx_cd_options_home_tabs', [$this, 'tab_ordering'], 45);

      add_filter('acf/fields/post_object/query/name=homepage_featured_materials_download', [$this, 'get_featured_materials_download'], 20, 1);
   }

   public function register_pages()
   {
      if (!function_exists('acf_add_options_page') || !function_exists('acf_add_options_sub_page')) {
         return;
      }

      acf_add_options_sub_page([
         'page_title'  => 'Opções de Homepage',
         'menu_title'  => 'Opções de Homepage',
         'menu_slug'   => 'homepage-options',
         'capability'  => 'bx_manage_theme_options',
         'post_id'     => 'homepage-options',
         'parent_slug' => 'theme-settings',
      ]);
   }

   public function register_homepage_fields()
   {
      if (!function_exists('acf_add_local_field_group')) {
         return;
      }

      acf_add_local_field_group(array(
         'key'    => 'group_6537fddddc1f3',
         'title'  => 'Campos de Homepage',
         'fields' => array_merge(...apply_filters('bx_cd_options_home_tabs', [])),
         'location' => array(
            array(
               array(
                  'param'    => 'options_page',
                  'operator' => '==',
                  'value'    => 'homepage-options',
               ),
            ),
         ),
         'menu_order'            => 0,
         'position'              => 'normal',
         'style'                 => 'default',
         'label_placement'       => 'top',
         'instruction_placement' => 'label',
         'hide_on_screen'        => '',
         'active'                => true,
         'description'           => '',
         'show_in_rest'          => 0,
      ));
   }

   public function tab_ordering($fields)
   {
      $fields[] = [
         array(
            'key'               => 'field_9537fdae8be42',
            'label'             => 'Ordenação',
            'name'              => '',
            'aria-label'        => '',
            'type'              => 'tab',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => 0,
            'wrapper'           => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'placement' => 'top',
            'endpoint'  => 0,
         ),
         array(
            'key' => 'field_659a7c21884f8',
            'label' => 'Atenção!',
            'name' => 'ordering_message',
            'aria-label' => '',
            'type' => 'message',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id' => '',
            ),
            'message' => 'As categorias selecionadas seguirão a ordem em que foram escolhidas na aba \'Categorias\'.',
            'new_lines' => 'wpautop',
            'esc_html' => 0,
         ),
         array(
            'key'               => 'field_654919c09e5f7',
            'label'             => 'Seções',
            'name'              => 'ordering_sections',
            'aria-label'        => '',
            'type'              => 'repeater',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => 0,
            'wrapper'           => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'layout'        => 'table',
            'pagination'    => 0,
            'min'           => 0,
            'max'           => 25,
            'collapsed'     => '',
            'button_label'  => 'Adicionar novo',
            'rows_per_page' => 20,
            'sub_fields'    => array(
               array(
                  'key'               => 'field_65491a9588d9a',
                  'label'             => 'Seção',
                  'name'              => 'single_section',
                  'aria-label'        => '',
                  'type'              => 'select',
                  'instructions'      => '',
                  'required'          => 0,
                  'conditional_logic' => 0,
                  'wrapper'           => array(
                     'width' => '',
                     'class' => '',
                     'id'    => '',
                  ),
                  'choices' => array(
                     'category'           => 'Categoria',
                     'specialists'        => 'Especialistas',
                     'events'             => 'Eventos',
                     'newsletter'         => 'Newsletter',
                     'videos'             => 'Vídeos',
                     'featured-materials' => 'Destaques',
                  ),
                  'default_value'   => false,
                  'return_format'   => 'value',
                  'multiple'        => 0,
                  'allow_null'      => 0,
                  'ui'              => 0,
                  'ajax'            => 0,
                  'placeholder'     => '',
                  'parent_repeater' => 'field_654919c09e5f7',
               ),
            ),
         )
      ];

      return $fields;
   }

   public function tab_events($fields)
   {
      $fields[] = [
         array(
            'key'               => 'field_6537fdeeebe4d',
            'label'             => 'Eventos',
            'name'              => '',
            'aria-label'        => '',
            'type'              => 'tab',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => 0,
            'wrapper'           => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'placement' => 'top',
            'endpoint'  => 0,
         ),
         array(
            'key'               => 'field_6537fe7231e58',
            'label'             => 'Nome da Seção',
            'name'              => 'homepage_events_title',
            'aria-label'        => '',
            'type'              => 'text',
            'instructions'      => '',
            'required'          => 1,
            'conditional_logic' => 0,
            'wrapper'           => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'default_value' => '',
            'maxlength'     => '',
            'placeholder'   => '',
            'prepend'       => '',
            'append'        => '',
         ),
         array(
            'key'               => 'field_6537fea431e59',
            'label'             => 'Descrição da Seção',
            'name'              => 'homepage_events_description',
            'aria-label'        => '',
            'type'              => 'text',
            'instructions'      => '',
            'required'          => 1,
            'conditional_logic' => 0,
            'wrapper'           => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'default_value' => '',
            'maxlength'     => '',
            'placeholder'   => '',
            'prepend'       => '',
            'append'        => '',
         ),
         array(
            'key'               => 'field_653a8bf287d9a',
            'label'             => 'Mostrar Eventos Mais Recentes no Slider',
            'name'              => 'homepage_show_recent_events_for_slider',
            'aria-label'        => '',
            'type'              => 'true_false',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => 0,
            'wrapper'           => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'message'       => '',
            'default_value' => 0,
            'ui'            => 0,
            'ui_on_text'    => '',
            'ui_off_text'   => '',
         ),
         array(
            'key'               => 'field_653957d41927d',
            'label'             => 'Eventos do Slider',
            'name'              => 'homepage_slider_events_posts',
            'aria-label'        => '',
            'type'              => 'repeater',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => array(
               array(
                  array(
                     'field'    => 'field_653a8bf287d9a',
                     'operator' => '!=',
                     'value'    => '1',
                  ),
               ),
            ),
            'wrapper'           => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'layout'        => 'table',
            'pagination'    => 0,
            'min'           => 0,
            'max'           => 0,
            'collapsed'     => '',
            'button_label'  => 'Adicionar novo',
            'rows_per_page' => 20,
            'sub_fields'    => array(
               array(
                  'key'               => 'field_653957fe1927e',
                  'label'             => 'Evento',
                  'name'              => 'single_event_post',
                  'aria-label'        => '',
                  'type'              => 'post_object',
                  'instructions'      => '',
                  'required'          => 1,
                  'conditional_logic' => 0,
                  'wrapper'           => array(
                     'width' => '',
                     'class' => '',
                     'id'    => '',
                  ),
                  'post_type' => array(
                     0 => 'post',
                  ),
                  'post_status' => array(
                     0 => 'publish',
                  ),
                  'taxonomy' => array(
                     0 => 'category:' . CategoryUtils::get_default_category_slug('event'),
                  ),
                  'return_format'        => 'id',
                  'multiple'             => 0,
                  'allow_null'           => 0,
                  'bidirectional'        => 0,
                  'ui'                   => 1,
                  'bidirectional_target' => array(),
                  'parent_repeater'      => 'field_653957d41927d',
               ),
            ),
         ),
         array(
            'key'               => 'field_653a7bf237d9a',
            'label'             => 'Mostrar Outros Eventos Mais Recentes',
            'name'              => 'homepage_show_recent_events_for_other_posts',
            'aria-label'        => '',
            'type'              => 'true_false',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => 0,
            'wrapper'           => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'message'       => '',
            'default_value' => 0,
            'ui'            => 0,
            'ui_on_text'    => '',
            'ui_off_text'   => '',
         ),
         array(
            'key'               => 'field_652908d41927d',
            'label'             => 'Outros Eventos',
            'name'              => 'homepage_events_other_posts',
            'aria-label'        => '',
            'type'              => 'repeater',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => array(
               array(
                  array(
                     'field'    => 'field_653a7bf237d9a',
                     'operator' => '!=',
                     'value'    => '1',
                  ),
               ),
            ),
            'wrapper'           => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'layout'        => 'table',
            'pagination'    => 0,
            'min'           => 0,
            'max'           => 4,
            'collapsed'     => '',
            'button_label'  => 'Adicionar novo',
            'rows_per_page' => 20,
            'sub_fields'    => array(
               array(
                  'key'               => 'field_65229e089927e',
                  'label'             => 'Evento',
                  'name'              => 'single_event_post',
                  'aria-label'        => '',
                  'type'              => 'post_object',
                  'instructions'      => '',
                  'required'          => 1,
                  'conditional_logic' => 0,
                  'wrapper'           => array(
                     'width' => '',
                     'class' => '',
                     'id'    => '',
                  ),
                  'post_type' => array(
                     0 => 'post',
                  ),
                  'post_status' => array(
                     0 => 'publish',
                  ),
                  'taxonomy' => array(
                     0 => 'category:' . CategoryUtils::get_default_category_slug('event'),
                  ),
                  'return_format'        => 'id',
                  'multiple'             => 0,
                  'allow_null'           => 0,
                  'bidirectional'        => 0,
                  'ui'                   => 1,
                  'bidirectional_target' => array(),
                  'parent_repeater'      => 'field_652908d41927d',
               ),
            ),
         ),
      ];

      return $fields;
   }

   public function tab_specialists($fields)
   {
      $fields[] = [
         array(
            'key'               => 'field_6537fdefebe48',
            'label'             => 'Especialistas e Mais Lidas',
            'name'              => '',
            'aria-label'        => '',
            'type'              => 'tab',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => 0,
            'wrapper'           => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'placement' => 'top',
            'endpoint'  => 0,
         ),
         array(
            'key'               => 'field_653be3119200b',
            'label'             => 'Título da Seção',
            'name'              => 'homepage_specialists_title',
            'aria-label'        => '',
            'type'              => 'text',
            'instructions'      => '',
            'required'          => 1,
            'conditional_logic' => 0,
            'wrapper'           => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'default_value' => '',
            'maxlength'     => '',
            'placeholder'   => '',
            'prepend'       => '',
            'append'        => '',
         ),
         array(
            'key'               => 'field_653fc01fecb7c',
            'label'             => 'Mostrar Todos os Especialistas',
            'name'              => 'homepage_show_all_specialists',
            'aria-label'        => '',
            'type'              => 'true_false',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => 0,
            'wrapper'           => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'message'       => '',
            'default_value' => 0,
            'ui'            => 0,
            'ui_on_text'    => '',
            'ui_off_text'   => '',
         ),
         array(
            'key'               => 'field_653be35fdeb4e',
            'label'             => 'Especialistas',
            'name'              => 'homepage_specialists',
            'aria-label'        => '',
            'type'              => 'repeater',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => array(
               array(
                  array(
                     'field'    => 'field_653fc01fecb7c',
                     'operator' => '!=',
                     'value'    => '1',
                  ),
               ),
            ),
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'layout'        => 'table',
            'pagination'    => 0,
            'min'           => 0,
            'max'           => 0,
            'collapsed'     => '',
            'button_label'  => 'Adicionar novo',
            'rows_per_page' => 20,
            'sub_fields'    => array(
               array(
                  'key'               => 'field_653be38adeb4f',
                  'label'             => 'Especialista',
                  'name'              => 'author_id',
                  'aria-label'        => '',
                  'type'              => 'taxonomy',
                  'instructions'      => '',
                  'required'          => 1,
                  'conditional_logic' => 0,
                  'wrapper'           => array(
                     'width' => '',
                     'class' => '',
                     'id'    => '',
                  ),
                  'taxonomy'             => 'author_tax',
                  'add_term'             => 0,
                  'save_terms'           => 0,
                  'load_terms'           => 0,
                  'return_format'        => 'id',
                  'field_type'           => 'select',
                  'allow_null'           => 0,
                  'bidirectional'        => 1,
                  'bidirectional_target' => '',
                  'multiple'             => 0,
                  'parent_repeater'      => 'field_653be35fdeb4e',
               ),
               array(
                  'key'               => 'field_653fb611e61ed',
                  'label'             => 'Mostrar Artigos Mais Recentes',
                  'name'              => 'show_latest_ones',
                  'aria-label'        => '',
                  'type'              => 'true_false',
                  'instructions'      => '',
                  'required'          => 0,
                  'conditional_logic' => 0,
                  'wrapper'           => array(
                     'width' => '',
                     'class' => '',
                     'id'    => '',
                  ),
                  'message'         => '',
                  'default_value'   => 0,
                  'ui'              => 0,
                  'ui_on_text'      => '',
                  'ui_off_text'     => '',
                  'parent_repeater' => 'field_653be35fdeb4e',
               ),
               array(
                  'key'               => 'field_653be9f62d513',
                  'label'             => 'Artigos',
                  'name'              => 'specialist_articles',
                  'aria-label'        => '',
                  'type'              => 'repeater',
                  'instructions'      => '',
                  'required'          => 0,
                  'conditional_logic' => array(
                     array(
                        array(
                           'field'    => 'field_653fb611e61ed',
                           'operator' => '!=',
                           'value'    => '1',
                        ),
                     ),
                  ),
                  'wrapper' => array(
                     'width' => '',
                     'class' => '',
                     'id'    => '',
                  ),
                  'layout'        => 'table',
                  'min'           => 0,
                  'max'           => 3,
                  'collapsed'     => '',
                  'button_label'  => 'Adicionar novo',
                  'rows_per_page' => 20,
                  'sub_fields'    => array(
                     array(
                        'key'               => 'field_653be417deb51',
                        'label'             => 'Artigo',
                        'name'              => 'single_article',
                        'aria-label'        => '',
                        'type'              => 'post_object',
                        'instructions'      => '',
                        'required'          => 1,
                        'conditional_logic' => 0,
                        'wrapper'           => array(
                           'width' => '',
                           'class' => '',
                           'id'    => '',
                        ),
                        'post_type' => array(
                           0 => 'post',
                        ),
                        'post_status' => array(
                           0 => 'publish',
                        ),
                        'return_format'        => 'id',
                        'multiple'             => 0,
                        'allow_null'           => 0,
                        'bidirectional'        => 0,
                        'ui'                   => 1,
                        'bidirectional_target' => array(),
                        'parent_repeater'      => 'field_653be9f62d513',
                     ),
                  ),
                  'parent_repeater' => 'field_653be35fdeb4e',
               ),
            ),
         ),
         array(
            'key'               => 'field_653fe26f49eab',
            'label'             => 'Exibir Artigos Mais Lidos',
            'name'              => 'show_homepage_most_read_articles',
            'aria-label'        => '',
            'type'              => 'true_false',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => 0,
            'wrapper'           => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'message'       => '',
            'default_value' => 1,
            'ui'            => 0,
            'ui_on_text'    => '',
            'ui_off_text'   => '',
         ),
         array(
            'key'               => 'field_653fe290aa50b',
            'label'             => 'Artigos Mais Lidos',
            'name'              => 'homepage_most_read_articles',
            'aria-label'        => '',
            'type'              => 'repeater',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => array(
               array(
                  array(
                     'field'    => 'field_653fe26f49eab',
                     'operator' => '!=',
                     'value'    => '1',
                  ),
               ),
            ),
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'layout'        => 'table',
            'pagination'    => 0,
            'min'           => 1,
            'max'           => 4,
            'collapsed'     => '',
            'button_label'  => 'Adicionar novo',
            'rows_per_page' => 20,
            'sub_fields'    => array(
               array(
                  'key'               => 'field_653fe2a4aa50c',
                  'label'             => 'Artigo',
                  'name'              => 'most_read_article',
                  'aria-label'        => '',
                  'type'              => 'post_object',
                  'instructions'      => '',
                  'required'          => 1,
                  'conditional_logic' => 0,
                  'wrapper'           => array(
                     'width' => '',
                     'class' => '',
                     'id'    => '',
                  ),
                  'post_type' => array(
                     0 => 'post',
                  ),
                  'post_status' => array(
                     0 => 'publish',
                  ),
                  'return_format'        => 'id',
                  'multiple'             => 0,
                  'allow_null'           => 0,
                  'bidirectional'        => 0,
                  'ui'                   => 1,
                  'bidirectional_target' => array(),
                  'parent_repeater'      => 'field_653fe290aa50b',
               ),
            ),
         ),
         array(
            'key' => 'field_67e2eef54b081',
            'label' => 'Período dos Artigos Mais Lidos',
            'name' => 'period_homepage_most_read_articles',
            'aria-label' => '',
            'type' => 'select',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => array(
               array(
                  array(
                     'field' => 'field_653fe26f49eab',
                     'operator' => '==',
                     'value' => 1,
                  ),
               ),
            ),
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id' => '',
            ),
            'choices' => array(
               7 => '7 dias',
               15 => '15 dias',
            ),
            'default_value' => 7,
            'return_format' => 'value',
            'multiple' => 0,
            'allow_null' => 0,
            'allow_in_bindings' => 0,
            'ui' => 0,
            'ajax' => 0,
            'placeholder' => '',
         ),
      ];

      return $fields;
   }

   public function tab_high_impact_post($fields)
   {
      $fields[] = [
         array(
            'key' => 'field_653ab571693a2',
            'label' => 'Alto Impacto',
            'name' => '',
            'aria-label' => '',
            'type' => 'tab',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id' => '',
            ),
            'placement' => 'top',
            'endpoint' => 0,
         ),
         array(
            'key' => 'field_653ab5bb0d20b',
            'label' => 'Alto Impacto',
            'name' => 'homepage_high_impact_post',
            'aria-label' => '',
            'type' => 'post_object',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id' => '',
            ),
            'post_type' => array(
               0 => 'post',
            ),
            'post_status' => array(
               0 => 'publish',
            ),
            'taxonomy' => '',
            'return_format' => 'id',
            'multiple' => 0,
            'allow_null' => 1,
            'bidirectional' => 0,
            'ui' => 1,
            'bidirectional_target' => array(),
         ),
      ];

      return $fields;
   }

   public function tab_home_newsletter($fields)
   {
      $fields[] = [
         array(
            'key'               => 'field_653bf59321732d',
            'label'             => 'Newsletter',
            'name'              => '',
            'aria-label'        => '',
            'type'              => 'tab',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => 0,
            'wrapper'           => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'placement' => 'top',
            'endpoint'  => 0,
         ),
         array(
            'key'               => 'field_653bf622417330',
            'label'             => 'Tipo de layout',
            'name'              => 'homepage_newsletter_layout',
            'aria-label'        => '',
            'type'              => 'radio',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => 0,
            'wrapper'           => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'choices' => array(
               'text'       => 'Somente texto',
               'background' => 'Imagem como background',
               'image_text' => 'Imagem com texto',
            ),
            'default_value'     => 'text',
            'return_format'     => 'value',
            'allow_null'        => 0,
            'other_choice'      => 0,
            'layout'            => 'vertical',
            'save_other_choice' => 0,
         ),
         array(
            'key'               => 'field_653bf60c17432f',
            'label'             => 'Titulo',
            'name'              => 'homepage_newsletter_title',
            'aria-label'        => '',
            'type'              => 'text',
            'instructions'      => '',
            'required'          => 1,
            'conditional_logic' => 0,
            'wrapper'           => array(
               'width' => '50',
               'class' => '',
               'id'    => '',
            ),
            'default_value' => '',
            'maxlength'     => '',
            'placeholder'   => '',
            'prepend'       => '',
            'append'        => '',
         ),
         array(
            'key'               => 'field_653bf5d191732e',
            'label'             => 'Texto',
            'name'              => 'homepage_newsletter_text',
            'aria-label'        => '',
            'type'              => 'text',
            'instructions'      => '',
            'required'          => 1,
            'conditional_logic' => 0,
            'wrapper'           => array(
               'width' => '50',
               'class' => '',
               'id'    => '',
            ),
            'default_value' => '',
            'maxlength'     => '',
            'placeholder'   => '',
            'prepend'       => '',
            'append'        => '',
         ),
         array(
            'key'               => 'field_653b2f6867bf24',
            'label'             => 'Imagem',
            'name'              => 'homepage_newsletter_image',
            'aria-label'        => '',
            'type'              => 'image',
            'instructions'      => '',
            'required'          => 1,
            'conditional_logic' => array(
               array(
                  array(
                     'field'    => 'field_653bf622417330',
                     'operator' => '==',
                     'value'    => 'image_text',
                  ),
               ),
               array(
                  array(
                     'field'    => 'field_653bf622417330',
                     'operator' => '==',
                     'value'    => 'background',
                  ),
               ),
            ),
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'return_format' => 'url',
            'library'       => 'all',
            'min_width'     => '',
            'min_height'    => '',
            'min_size'      => '',
            'max_width'     => '',
            'max_height'    => '',
            'max_size'      => '',
            'mime_types'    => '',
            'preview_size'  => 'medium',
         ),
      ];

      return $fields;
   }

   public function tab_categories($fields)
   {
      $fields[] = [
         array(
            'key'               => 'field_6537fdde3be49',
            'label'             => 'Categorias',
            'name'              => '',
            'aria-label'        => '',
            'type'              => 'tab',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => 0,
            'wrapper'           => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'placement' => 'top',
            'endpoint'  => 0,
         ),
         array(
            'key' => 'field_654131cce3305',
            'label' => 'Categorias',
            'name' => 'homepage_categories',
            'aria-label' => '',
            'type' => 'repeater',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id' => '',
            ),
            'layout' => 'table',
            'pagination' => 0,
            'min' => 0,
            'max' => 20,
            'collapsed' => '',
            'button_label' => 'Adicionar novo',
            'rows_per_page' => 20,
            'sub_fields' => array(
               array(
                  'key' => 'field_654138793ad22',
                  'label' => 'Categoria',
                  'name' => 'category_id',
                  'aria-label' => '',
                  'type' => 'taxonomy',
                  'instructions' => '',
                  'required' => 1,
                  'conditional_logic' => 0,
                  'wrapper' => array(
                     'width' => '',
                     'class' => '',
                     'id' => '',
                  ),
                  'taxonomy' => 'category',
                  'add_term' => 1,
                  'save_terms' => 0,
                  'load_terms' => 0,
                  'return_format' => 'id',
                  'field_type' => 'select',
                  'allow_null' => 0,
                  'bidirectional' => 0,
                  'multiple' => 0,
                  'bidirectional_target' => array(),
                  'parent_repeater' => 'field_654131cce3305',
               ),
               array(
                  'key' => 'field_654139783ad25',
                  'label' => 'Mostrar Posts Mais Recente',
                  'name' => 'show_latest_articles',
                  'aria-label' => '',
                  'type' => 'true_false',
                  'instructions' => '',
                  'required' => 0,
                  'conditional_logic' => 0,
                  'wrapper' => array(
                     'width' => '',
                     'class' => '',
                     'id' => '',
                  ),
                  'message' => '',
                  'default_value' => 0,
                  'ui' => 0,
                  'ui_on_text' => '',
                  'ui_off_text' => '',
                  'parent_repeater' => 'field_654131cce3305',
               ),
               array(
                  'key' => 'field_654138f03ad23',
                  'label' => 'Posts',
                  'name' => 'category_articles',
                  'aria-label' => '',
                  'type' => 'repeater',
                  'instructions' => '',
                  'required' => 0,
                  'conditional_logic' => array(
                     array(
                        array(
                           'field' => 'field_654139783ad25',
                           'operator' => '!=',
                           'value' => '1',
                        ),
                     ),
                  ),
                  'wrapper' => array(
                     'width' => '',
                     'class' => '',
                     'id' => '',
                  ),
                  'layout' => 'table',
                  'min' => 0,
                  'max' => 6,
                  'collapsed' => '',
                  'button_label' => 'Adicionar novo',
                  'rows_per_page' => 20,
                  'sub_fields' => array(
                     array(
                        'key' => 'field_654139073ad24',
                        'label' => 'Post',
                        'name' => 'category_single_article',
                        'aria-label' => '',
                        'type' => 'post_object',
                        'instructions' => '',
                        'required' => 0,
                        'conditional_logic' => 0,
                        'wrapper' => array(
                           'width' => '',
                           'class' => '',
                           'id' => '',
                        ),
                        'post_type' => array(
                           0 => 'post',
                        ),
                        'post_status' => array(
                           0 => 'publish',
                        ),
                        'return_format' => 'id',
                        'multiple' => 0,
                        'allow_null' => 0,
                        'bidirectional' => 0,
                        'ui' => 1,
                        'bidirectional_target' => array(),
                        'parent_repeater' => 'field_654138f03ad23',
                     ),
                  ),
                  'parent_repeater' => 'field_654131cce3305',
               ),
            ),
         ),
      ];

      return $fields;
   }

   public function tab_home_videos($fields)
   {
      $fields[] = [
         array(
            'key' => 'field_6541t487efefec',
            'label' => 'Vídeos',
            'name' => '',
            'aria-label' => '',
            'type' => 'tab',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id' => '',
            ),
            'placement' => 'top',
            'endpoint' => 0,
         ),
         array(
            'key' => 'field_654t14891fefed',
            'label' => 'Vídeos',
            'name' => 'home_videos',
            'aria-label' => '',
            'type' => 'post_object',
            'instructions' => 'Selecione até 5 vídeos.',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id' => '',
            ),
            'post_type' => array(
               0 => 'post',
            ),
            'post_status' => array(
               0 => 'publish',
            ),
            'taxonomy' => array(
               0 => 'category:' . CategoryUtils::get_default_category_slug('video'),
            ),
            'return_format' => 'id',
            'multiple' => 1,
            'allow_null' => 1,
            'ui' => 1,
         ),
         array(
            'key' => 'field_654t14915fefee',
            'label' => 'Título',
            'name' => 'home_videos_title',
            'aria-label' => '',
            'type' => 'text',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id' => '',
            ),
            'default_value' => '',
            'maxlength' => '',
            'placeholder' => '',
            'prepend' => '',
            'append' => '',
         ),
         array(
            'key' => 'field_6541492efeftef',
            'label' => 'Texto',
            'name' => 'home_videos_text',
            'aria-label' => '',
            'type' => 'textarea',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id' => '',
            ),
            'default_value' => '',
            'maxlength' => '',
            'rows' => '',
            'placeholder' => '',
            'new_lines' => '',
         ),
      ];

      return $fields;
   }

   public function tab_featured_materials($fields)
   {
      $fields[] = [
         array(
            'key' => 'field_654d2f64c196f',
            'label' => 'Destaque',
            'name' => '',
            'aria-label' => '',
            'type' => 'tab',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id' => '',
            ),
            'placement' => 'top',
            'endpoint' => 0,
         ),
         array(
            'key' => 'field_554149aefeft11',
            'label' => 'Título',
            'name' => 'homepage_featured_materials_title',
            'aria-label' => '',
            'type' => 'text',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id' => '',
            ),
            'default_value' => Utils::$homepage_featured_materials_default_title,
            'maxlength' => '',
            'rows' => '',
            'placeholder' => '',
            'new_lines' => '',
         ),
         array(
            'key' => 'field_67d2ed72eb6f5',
            'label' => 'Mostrar matérias mais recentes',
            'name' => 'homepage_featured_materials_show_recent_posts',
            'aria-label' => '',
            'type' => 'true_false',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id' => '',
            ),
            'message' => '',
            'default_value' => 0,
            'ui' => 0,
            'ui_on_text' => '',
            'ui_off_text' => '',
         ),
         array(
            'key'               => 'field_654d2f70c1970',
            'label'             => 'Destaque',
            'name'              => 'homepage_featured_materials',
            'type'              => 'relationship',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => array(
               array(
                  array(
                     'field'    => 'field_67d2ed72eb6f5',
                     'operator' => '==',
                     'value'    => '0',
                  ),
               ),
            ),
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id'    => '',
            ),
            'post_type'     => [
               0 => 'post',
            ],
            'post_status' => [
               0 => 'publish',
            ],
            'filters'       => array(),
            'elements'      => '',
            'return_format' => 'id',
            'max'           => 7,
         ),
         array(
            'key' => 'field_655221b23f472831',
            'label' => 'Download',
            'name' => 'homepage_featured_materials_download',
            'aria-label' => '',
            'type' => 'post_object',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id' => '',
            ),
            'post_type' => array(
               0 => 'post',
            ),
            'post_status' => array(
               0 => 'publish',
            ),
            'return_format' => 'id',
            'multiple' => 0,
            'allow_null' => 1,
            'ui' => 1,
         ),
      ];

      return $fields;
   }

   public function get_featured_materials_download($args)
   {
      $args['meta_query'] = [
         'relation' => 'AND',
         [
            'key'     => 'download_material_file_upload',
            'value'   => '',
            'compare' => '!=',
         ],
      ];

      return $args;
   }
}
