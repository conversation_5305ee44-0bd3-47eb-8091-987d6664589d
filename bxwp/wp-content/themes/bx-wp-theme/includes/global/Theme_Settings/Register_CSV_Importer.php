<?php

namespace CanaisDigitais\Theme_Settings;

if (!defined('ABSPATH')) {
   exit;
}

class Register_CSV_Importer
{
   protected $used_rows    = 0;
   protected $failed_rows  = 0;
   protected $total_rows   = 0;

   public function __construct()
   {
      \add_action('admin_menu', [$this, 'register_subpage'], 99);
   }

   public function register_subpage()
   {
      \add_submenu_page(
         'general-options',
         __('Importador Meta Tags SEO', 'canais-digitais'),
         __('Importador Meta Tags SEO', 'canais-digitais'),
         'manage_options',
         'csv-importer-metatags-seo',
         [$this, 'csv_importer_metatags_seo_render_page']
      );
   }

   public function csv_importer_metatags_seo_render_page()
   {
      if (!current_user_can('manage_options')) {
         \wp_die(esc_html__('Sem permissões suficientes.', 'canais-digitais'));
      }

      if (!is_plugin_active('wordpress-seo/wp-seo.php')) {
         echo '<div class="notice notice-error"><p>' . esc_html__('O plugin Yoast SEO precisa estar ativo para utilizar o importador de NoIndex.', 'canais-digitais') . '</p></div>';

         return;
      }

      if ($_SERVER['REQUEST_METHOD'] === 'POST' && !empty($_FILES['csv_file']['tmp_name'])) {
         $this->csv_importer();

         $type_action = (intval($_POST['type_action'])) ? esc_html__("Adicionado", 'canais-digitais') : esc_html__("Retirado", 'canais-digitais');

         if ($this->total_rows > 0) {
            $message = sprintf(
               esc_html__('Linhas processadas: %d', 'canais-digitais'),
               $this->total_rows
            );

            if ($this->used_rows > 0) {
               $message .= '<br>' . sprintf(
                  esc_html(_n(
                     '%d post alterado. %s "noindex" e "nofollow"',
                     '%d posts alterados. %s "noindex" e "nofollow"',
                     $this->used_rows,
                     'canais-digitais'
                  )),
                  $this->used_rows,
                  $type_action
               );
            }

            printf('<div class="updated notice"><p>%s</p></div>', $message);
         }

         if ($this->failed_rows > 0) {
            printf(
               '<div class="notice notice-warning"><p>%s</p></div>',
               sprintf(
                  esc_html(_n(
                     '%d post não foi encontrado ou apresentou erro',
                     '%d posts não foram encontrados ou apresentaram erro',
                     $this->failed_rows,
                     'canais-digitais'
                  )),
                  $this->failed_rows
               )
            );
         }
      }

?>

      <div class="wrap">
         <h1 style="font-size:2rem;"><?php esc_html_e('Importador Meta Tags SEO', 'canais-digitais') ?></h1>

         <p><?php esc_html_e('Permite adicionar ou remover as meta tags "noindex" e "nofollow" de posts específicos através de um arquivo CSV. As URLs devem ser URLs válidas do próprio site.', 'canais-digitais') ?></p>

         <h4><?php esc_html_e('O arquivo CSV deve conter as seguintes colunas obrigatórias:', 'canais-digitais') ?></h4>

         <table class="widefat striped" style="margin-bottom: 1rem;">
            <thead>
               <tr>
                  <th><?php esc_html_e('Nome da coluna', 'canais-digitais'); ?></th>
                  <th><?php esc_html_e('Descrição', 'canais-digitais'); ?></th>
               </tr>
            </thead>
            <tbody>
               <tr>
                  <td><code>url</code></td>
                  <td><?php esc_html_e('URL completa do post.', 'canais-digitais'); ?></td>
               </tr>
            </tbody>
         </table>

         <?php \settings_errors(); ?>

         <form action="" method="post" enctype="multipart/form-data">
            <table class="form-table">
               <tr>
                  <th scope="row"><label for="type_action"><?php esc_html_e('Ação', 'canais-digitais') ?></label></th>
                  <td>
                     <select id="type_action" name="type_action">
                        <option value="1"><?php esc_html_e('Adicionar "noindex"', 'canais-digitais') ?></option>
                        <option value="0"><?php esc_html_e('Retirar "noindex"', 'canais-digitais') ?></option>
                     </select>
                  </td>
               </tr>
               <tr>
                  <th scope="row"><label for="csv_file"><?php esc_html_e('Arquivo CSV', 'canais-digitais') ?></label>
                  </th>
                  <td>
                     <input type="file" name="csv_file" id="csv_file" class="regular-text" required>
                  </td>
               </tr>
               <tr>
                  <th colspan="2" scope="row">
                     <?php \submit_button(esc_attr__('Importar', 'canais-digitais')); ?>
                  </th>
               </tr>
            </table>
         </form>

      </div>

<?php

   }

   public function csv_importer()
   {
      if (!isset($_FILES['csv_file']) || empty($_FILES['csv_file']['tmp_name'])) {
         return;
      }

      $type_action       = isset($_POST['type_action']) ? intval($_POST['type_action']) : 1;
      $csv_data          = $this->get_csv_data('csv_file');
      $this->used_rows   = 0;
      $this->failed_rows = 0;
      $this->total_rows  = count($csv_data);

      foreach ($csv_data as $row) {
         if (!isset($row['url'])) {
            $this->failed_rows++;
            continue;
         }

         $url = trim($row['url']);
         if (empty($url)) {
            $this->failed_rows++;
            continue;
         }

         $post_id = url_to_postid($url);

         if (!$post_id) {
            $this->failed_rows++;
            continue;
         }

         if ($type_action === 0) {
            delete_post_meta($post_id, '_yoast_wpseo_meta-robots-noindex');
            delete_post_meta($post_id, '_yoast_wpseo_meta-robots-nofollow');
         } else {
            update_post_meta($post_id, '_yoast_wpseo_meta-robots-noindex', 1);
            update_post_meta($post_id, '_yoast_wpseo_meta-robots-nofollow', 1);
         }

         $this->used_rows++;
      }
   }

   public function get_csv_data($file_input_name)
   {
      $csv_data = [];

      if (!isset($_FILES[$file_input_name]) || !isset($_FILES[$file_input_name]['tmp_name'])) {
         return $csv_data;
      }

      $filename = $_FILES[$file_input_name]['tmp_name'];

      if (!file_exists($filename)) {
         return $csv_data;
      }

      if (false !== ($csv_file = fopen($filename, 'r'))) {
         if (fgets($csv_file, 4) !== "\xef\xbb\xbf") { // Remove BOM character
            rewind($csv_file);
         }

         $csv_column_header = [];

         while (false !== ($row = fgetcsv($csv_file, 0, ','))) {
            $row = str_replace("\xc2\xa0", ' ', $row);

            if (empty($csv_column_header)) {

               if (count($row) === 1 && \filter_var($row[0], \FILTER_VALIDATE_URL)) {
                  $csv_column_header = ['url'];
                  $csv_data[] = ['url' => $row[0]];
               } else {
                  $csv_column_header = $row;
               }
            } else {
               $csv_data[] = array_combine($csv_column_header, $row);
            }
         }

         fclose($csv_file);
      }
      return $csv_data;
   }
}
