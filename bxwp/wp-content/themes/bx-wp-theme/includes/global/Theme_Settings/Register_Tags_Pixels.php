<?php

/**
 * Name: Tags_Pixels
 * Custom Fields: scripts do header, scripts de open body, script do Iris, data domain do OneTrust
 * Description: Opções para tags e pixels.
 */

namespace CanaisDigitais\Theme_Settings;

if (!defined('ABSPATH')) {
   exit;
}

class Register_Tags_Pixels
{
   function __construct()
   {
      add_action('init', [$this, 'register_pages']);

      add_action('init', [$this, 'register_tags_and_pixels_fields']);
      add_filter('bx_cd_tags_and_pixels_tabs', [$this, 'tab_tags_and_pixels'], 5);
   }

   public function register_pages()
   {
      if (!function_exists('acf_add_options_page') || !function_exists('acf_add_options_sub_page')) {
         return;
      }

      acf_add_options_sub_page([
         'page_title'  => 'Tags e Pixels',
         'menu_title'  => 'Tags e Pixels',
         'menu_slug'   => 'tags-and-pixels',
         'capability'  => 'bx_manage_theme_options',
         'post_id'     => 'tags-and-pixels',
         'parent_slug' => 'theme-settings',
      ]);
   }

   public function register_tags_and_pixels_fields()
   {
      if (!function_exists('acf_add_local_field_group')) {
         return;
      }

      acf_add_local_field_group(array(
         'key'    => 'group_65fdd4324ddc1f3',
         'title'  => 'Tags e Pixels',
         'fields' => array_merge(...apply_filters('bx_cd_tags_and_pixels_tabs', [])),
         'location' => array(
            array(
               array(
                  'param'    => 'options_page',
                  'operator' => '==',
                  'value'    => 'tags-and-pixels',
               ),
            ),
         ),
         'menu_order'            => 0,
         'position'              => 'normal',
         'style'                 => 'default',
         'label_placement'       => 'top',
         'instruction_placement' => 'label',
         'hide_on_screen'        => '',
         'active'                => true,
         'description'           => '',
         'show_in_rest'          => 0,
      ));
   }

   public function register_mega_menu_fields()
   {
      if (!function_exists('acf_add_local_field_group')) {
         return;
      }

      acf_add_local_field_group(array(
         'key'    => 'group_6d5dgfdf48ddc1f3',
         'title'  => 'Mega Menu',
         'fields' => apply_filters('bx_cd_mega_menu_tabs', []),
         'location' => array(
            array(
               array(
                  'param'    => 'options_page',
                  'operator' => '==',
                  'value'    => 'mega-menu',
               ),
            ),
         ),
         'menu_order'            => 0,
         'position'              => 'normal',
         'style'                 => 'default',
         'label_placement'       => 'top',
         'instruction_placement' => 'label',
         'hide_on_screen'        => '',
         'active'                => true,
         'description'           => '',
         'show_in_rest'          => 0,
      ));
   }

   public function tab_tags_and_pixels($fields)
   {
      $fields[] = [
         array(
            'key' => 'field_65aa7c756fe5e',
            'label' => 'Scripts do Head',
            'name' => 'bx_head_scripts',
            'aria-label' => '',
            'type' => 'textarea',
            'instructions' => 'Inclua neste campo as tags, pixels ou scripts que devem ir dentro da tag <strong>head</strong> do site.',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id' => '',
            ),
            'default_value' => '',
            'maxlength' => '',
            'rows' => '',
            'placeholder' => '',
            'new_lines' => '',
         ),
         array(
            'key' => 'field_65aa7cc16fe5f',
            'label' => 'Scripts do Body Open',
            'name' => 'bx_body_open_scripts',
            'aria-label' => '',
            'type' => 'textarea',
            'instructions' => 'Inclua neste campo as tags, pixels ou scripts que devem ir após a abertura da tag <strong>body</strong> do site.',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id' => '',
            ),
            'default_value' => '',
            'maxlength' => '',
            'rows' => '',
            'placeholder' => '',
            'new_lines' => '',
         ),
         array(
            'key' => 'field_657ccsds4716fe5f',
            'label' => 'Script do IRIS',
            'name' => 'bx_iris_script',
            'aria-label' => '',
            'type' => 'textarea',
            'instructions' => 'Inclua neste campo o script do IRIS, responsável pelo login',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id' => '',
            ),
            'default_value' => '',
            'maxlength' => '',
            'rows' => '',
            'placeholder' => '',
            'new_lines' => '',
         ),
         // array(
         //    'key' => 'field_65f1e3e391369',
         //    'label' => 'One Trust Data Domain',
         //    'name' => 'bx_onetrust_data_domain',
         //    'aria-label' => '',
         //    'type' => 'text',
         //    'instructions' => '',
         //    'required' => 0,
         //    'conditional_logic' => 0,
         //    'wrapper' => array(
         //       'width' => '',
         //       'class' => '',
         //       'id' => '',
         //    ),
         //    'default_value' => '',
         //    'maxlength' => '',
         //    'placeholder' => '',
         //    'prepend' => '',
         //    'append' => '',
         // ),
      ];

      return $fields;
   }
}
