<?php

/**
 * Name: Blocks
 * Custom Fields: id do autor, artigos do autor, slides, título do slide, descrição do slider, imagem do slider
 * Description: <PERSON>os G<PERSON>mberg Leia Mais e Slideshow.
 */

namespace CanaisDigitais\Blocks;

use CanaisDigitais\Category\Utils as CategoryUtils;

if (!defined('ABSPATH')) exit;

class Register
{
   function __construct()
   {
      add_filter('block_categories_all', [$this, 'register_blocks_categories']);

      add_action('acf/init', [$this, 'register_block_read_more_init']);
      add_action('acf/include_fields', [$this, 'register_block_read_more_fields']);

      add_action('acf/init', [$this, 'register_block_slideshow_init']);
      add_action('acf/include_fields', [$this, 'register_block_slideshow_fields']);

      add_action('acf/init', [$this, 'register_block_read_more_category_init']);
      add_action('acf/include_fields', [$this, 'register_block_read_more_category_fields']);
      add_filter('acf/fields/relationship/query/name=block_read_more_category_posts', [$this, 'register_block_read_more_filter_posts_by_date'], 10, 1);
   }

   function register_blocks_categories($categories)
   {
      $category_slugs = wp_list_pluck($categories, 'slug');

      return in_array('cd', $category_slugs, true) ? $categories : array_merge(
         $categories,
         [
            [
               'slug'  => 'cd',
               'title' => __('Blocos Canais Digitais', 'canais_digitais'),
               'icon'  => null,
            ],
         ]
      );
   }
   public function register_block_read_more_init()
   {
      if (function_exists('acf_register_block_type')) {
         acf_register_block_type([
            'name'            => 'cd-read-more',
            'title'           => __('CD Leia mais', 'canais_digitais'),
            'description'     => __('Permite escolher até 2 posts.', 'canais_digitais'),
            'render_template' => 'components/blocks/read-more-author.php',
            'category'        => 'cd',
            'icon'            => 'grid-view',
            'mode'            => 'auto',
            'keywords'        => ['read-more'],
         ]);
      }
   }

   public function register_block_read_more_fields()
   {
      if (!function_exists('acf_add_local_field_group')) {
         return;
      }

      acf_add_local_field_group(array(
         'key'    => 'group_65672fc8a344f',
         'title'  => 'Campos de CD Leia Mais',
         'fields' => array(
            array(
               'key'               => 'field_65672fd98f9cd',
               'label'             => 'Autor',
               'name'              => 'block_author_id',
               'aria-label'        => '',
               'type'              => 'taxonomy',
               'instructions'      => '',
               'required'          => 1,
               'conditional_logic' => 0,
               'wrapper'           => array(
                  'width' => '',
                  'class' => '',
                  'id'    => '',
               ),
               'taxonomy'             => 'author_tax',
               'add_term'             => 0,
               'save_terms'           => 0,
               'load_terms'           => 0,
               'return_format'        => 'id',
               'field_type'           => 'select',
               'allow_null'           => 0,
               'bidirectional'        => 0,
               'multiple'             => 0,
               'bidirectional_target' => array(),
            ),
            array(
               'key'               => 'field_6567306e23a96',
               'label'             => 'Exibir Mais Recentes',
               'name'              => 'block_show_most_recent',
               'aria-label'        => '',
               'type'              => 'true_false',
               'instructions'      => '',
               'required'          => 0,
               'conditional_logic' => 0,
               'wrapper'           => array(
                  'width' => '',
                  'class' => '',
                  'id'    => '',
               ),
               'message'       => '',
               'default_value' => 0,
               'ui'            => 0,
               'ui_on_text'    => '',
               'ui_off_text'   => '',
            ),
            array(
               'key'               => 'field_6567300e23a94',
               'label'             => 'Artigos',
               'name'              => 'block_articles',
               'aria-label'        => '',
               'type'              => 'repeater',
               'instructions'      => '',
               'required'          => 0,
               'conditional_logic' => array(
                  array(
                     array(
                        'field'    => 'field_6567306e23a96',
                        'operator' => '!=',
                        'value'    => '1',
                     ),
                  ),
               ),
               'wrapper' => array(
                  'width' => '',
                  'class' => '',
                  'id'    => '',
               ),
               'layout'        => 'table',
               'pagination'    => 0,
               'min'           => 0,
               'max'           => 2,
               'collapsed'     => '',
               'button_label'  => 'Adicionar artigo',
               'rows_per_page' => 20,
               'sub_fields'    => array(
                  array(
                     'key'               => 'field_6567304223a95',
                     'label'             => 'Artigo',
                     'name'              => 'block_single_article',
                     'aria-label'        => '',
                     'type'              => 'post_object',
                     'instructions'      => '',
                     'required'          => 1,
                     'conditional_logic' => 0,
                     'wrapper'           => array(
                        'width' => '',
                        'class' => '',
                        'id'    => '',
                     ),
                     'post_type' => array(
                        0 => 'post',
                     ),
                     'post_status' => array(
                        0 => 'publish',
                     ),
                     'return_format'        => 'id',
                     'multiple'             => 0,
                     'allow_null'           => 0,
                     'bidirectional'        => 0,
                     'ui'                   => 1,
                     'bidirectional_target' => array(),
                     'parent_repeater'      => 'field_6567300e23a94',
                  ),
               ),
            ),
         ),
         'location' => array(
            array(
               array(
                  'param'    => 'block',
                  'operator' => '==',
                  'value'    => 'acf/cd-read-more',
               ),
            ),
         ),
         'menu_order'            => 0,
         'position'              => 'normal',
         'style'                 => 'default',
         'label_placement'       => 'top',
         'instruction_placement' => 'label',
         'hide_on_screen'        => '',
         'active'                => true,
         'description'           => '',
         'show_in_rest'          => 0,
      ));
   }

   public function register_block_slideshow_init()
   {
      if (function_exists('acf_register_block_type')) {
         acf_register_block_type([
            'name'            => 'cd-slideshow',
            'title'           => __('CD Slideshow', 'canais_digitais'),
            'description'     => __('Permite escolher imagens e adicionar títulos e legendas.', 'canais_digitais'),
            'render_template' => 'components/blocks/slideshow.php',
            'category'        => 'cd',
            'mode'            => 'auto',
            'icon'            => 'slides',
            'keywords'        => ['slideshow'],
         ]);
      }
   }

   public function register_block_slideshow_fields()
   {
      if (!function_exists('acf_add_local_field_group')) {
         return;
      }

      acf_add_local_field_group(array(
         'key' => 'group_6569e741ba4f9',
         'title' => 'Campos de Slideshow',
         'fields' => array(
            array(
               'key' => 'field_6568c12d0172a',
               'label' => 'Slides',
               'name' => 'block_slides',
               'aria-label' => '',
               'type' => 'repeater',
               'instructions' => '',
               'required' => 0,
               'conditional_logic' => 0,
               'wrapper' => array(
                  'width' => '',
                  'class' => '',
                  'id' => '',
               ),
               'layout' => 'table',
               'pagination' => 0,
               'collapsed' => '',
               'button_label' => 'Adicionar slide',
               'rows_per_page' => 20,
               'sub_fields' => array(
                  array(
                     'key' => 'field_6568c1490172b',
                     'label' => 'Título',
                     'name' => 'block_single_slide_title',
                     'aria-label' => '',
                     'type' => 'text',
                     'instructions' => '',
                     'required' => 1,
                     'conditional_logic' => 0,
                     'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                     ),
                     'default_value' => '',
                     'maxlength' => '',
                     'placeholder' => '',
                     'prepend' => '',
                     'append' => '',
                     'parent_repeater' => 'field_6568c12d0172a',
                  ),
                  array(
                     'key' => 'field_6568c1610172c',
                     'label' => 'Descrição',
                     'name' => 'block_single_slide_description',
                     'aria-label' => '',
                     'type' => 'wysiwyg',
                     'instructions' => '',
                     'required' => 0,
                     'conditional_logic' => 0,
                     'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                     ),
                     'default_value' => '',
                     'maxlength' => '',
                     'placeholder' => '',
                     'new_lines' => 'br',
                     'prepend' => '',
                     'append' => '',
                     'parent_repeater' => 'field_6568c12d0172a',
                  ),
                  array(
                     'key' => 'field_6568c17b0172d',
                     'label' => 'Imagem',
                     'name' => 'block_single_slide_image',
                     'aria-label' => '',
                     'type' => 'image',
                     'instructions' => '',
                     'required' => 1,
                     'conditional_logic' => 0,
                     'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                     ),
                     'return_format' => 'id',
                     'library' => 'all',
                     'min_width' => '',
                     'min_height' => '',
                     'min_size' => '',
                     'max_width' => '',
                     'max_height' => '',
                     'max_size' => '',
                     'mime_types' => '',
                     'preview_size' => 'medium',
                     'parent_repeater' => 'field_6568c12d0172a',
                  ),
               ),
            ),

         ),
         'location' => array(
            array(
               array(
                  'param' => 'block',
                  'operator' => '==',
                  'value' => 'acf/cd-slideshow',
               ),
            ),
         ),
         'menu_order' => 0,
         'position' => 'normal',
         'style' => 'default',
         'label_placement' => 'top',
         'instruction_placement' => 'label',
         'hide_on_screen' => '',
         'active' => true,
         'description' => '',
         'show_in_rest' => 0,
      ));
   }

   public function register_block_read_more_category_init()
   {
      if (function_exists('acf_register_block_type')) {
         acf_register_block_type([
            'name'            => 'cd-read-more-category',
            'title'           => __('CD Leia mais (Categoria)', 'canais_digitais'),
            'description'     => __('Permite escolher até 2 posts de uma categoria.', 'canais_digitais'),
            'render_template' => 'components/blocks/read-more-category.php',
            'category'        => 'cd',
            'icon'            => 'grid-view',
            'mode'            => 'auto',
            'keywords'        => ['read-more-category'],
         ]);
      }
   }

   public function register_block_read_more_category_fields()
   {
      if (!function_exists('acf_add_local_field_group')) {
         return;
      }

      $categories = CategoryUtils::get_categories();

      $category_lists = [];

      if (!empty($categories)) {
         foreach ($categories as $category) {
            $category_lists[$category->name] = "category:{$category->name}";
         }
      }

      acf_add_local_field_group(array(
         'key' => 'group_689ca571174a4',
         'title' => 'Campos de CD Leia mais (Categoria)',
         'fields' => array(
            array(
               'key' => 'field_689ca7a45a5cb',
               'label' => 'Tipo de seleção',
               'name' => 'block_read_more_category_select_type',
               'aria-label' => '',
               'type' => 'select',
               'instructions' => '',
               'required' => 1,
               'conditional_logic' => 0,
               'wrapper' => array(
                  'width' => '',
                  'class' => '',
                  'id' => '',
               ),
               'choices' => array(
                  'category' => 'Selecionar categoria',
                  'posts' => 'Selecionar posts',
               ),
               'default_value' => 'category',
               'return_format' => '',
               'multiple' => 0,
               'allow_null' => 0,
               'allow_in_bindings' => 0,
               'ui' => 0,
               'ajax' => 0,
               'placeholder' => '',
               'create_options' => 0,
               'save_options' => 0,
            ),
            array(
               'key' => 'field_689ca8005a5cc',
               'label' => 'Categoria',
               'name' => 'block_read_more_category_taxonomy',
               'aria-label' => '',
               'type' => 'taxonomy',
               'instructions' => 'Selecione uma categoria que irá listar os dois posts mais recentes dela.',
               'required' => 0,
               'conditional_logic' => array(
                  array(
                     array(
                        'field' => 'field_689ca7a45a5cb',
                        'operator' => '==',
                        'value' => 'category',
                     ),
                  ),
               ),
               'wrapper' => array(
                  'width' => '',
                  'class' => '',
                  'id' => '',
               ),
               'taxonomy' => 'category',
               'add_term' => 0,
               'save_terms' => 0,
               'load_terms' => 0,
               'return_format' => 'id',
               'field_type' => 'select',
               'allow_null' => 0,
               'allow_in_bindings' => 0,
               'bidirectional' => 0,
               'multiple' => 0,
               'bidirectional_target' => array(),
            ),
            array(
               'key' => 'field_689ca90f5a5cd',
               'label' => 'Posts',
               'name' => 'block_read_more_category_posts',
               'aria-label' => '',
               'type' => 'relationship',
               'instructions' => 'Selecione manualmente até dois posts de qualquer categoria',
               'required' => 0,
               'conditional_logic' => array(
                  array(
                     array(
                        'field' => 'field_689ca7a45a5cb',
                        'operator' => '==',
                        'value' => 'posts',
                     ),
                  ),
               ),
               'wrapper' => array(
                  'width' => '',
                  'class' => '',
                  'id' => '',
               ),
               'post_type' => array(
                  0 => 'post',
               ),
               'post_status' => array(
                  0 => 'publish',
               ),
               'taxonomy' => $category_lists,
               'filters' => array(
                  0 => 'search',
                  1 => 'taxonomy',
               ),
               'return_format' => 'id',
               'min' => '',
               'max' => 2,
               'allow_in_bindings' => 0,
               'elements' => '',
               'bidirectional' => 0,
               'bidirectional_target' => array(),
            ),
         ),
         'location' => array(
            array(
               array(
                  'param' => 'block',
                  'operator' => '==',
                  'value' => 'acf/cd-read-more-category',
               ),
            ),
         ),
         'menu_order' => 0,
         'position' => 'normal',
         'style' => 'default',
         'label_placement' => 'top',
         'instruction_placement' => 'label',
         'hide_on_screen' => '',
         'active' => true,
         'description' => '',
         'show_in_rest' => 0,
      ));
   }

   public function register_block_read_more_filter_posts_by_date($args)
   {
      $args['orderby'] = 'date';
      $args['order'] = 'DESC';

      return $args;
   }
}
