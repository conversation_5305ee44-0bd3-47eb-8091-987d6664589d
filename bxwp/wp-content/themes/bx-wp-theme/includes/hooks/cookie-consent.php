<?php

if (!defined('ABSPATH')) {
   exit;
}

add_action('bx_head_scripts', 'bx_canais_digitais_cookie_consent');
function bx_canais_digitais_cookie_consent()
{
   if (wp_get_environment_type() === 'development' || wp_get_environment_type() === 'local') {
      return;
   }

?>
   <script type="text/javascript">"use strict";function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}!function(){self.airgap={readyQueue:[],ready(e){this.readyQueue.push(e)},...self.airgap};const e=()=>{const e="__tcfapiLocator",t=[],a=window;let n,s,o=a;for(;o;){try{if(o.frames[e]){n=o;break}}catch(e){}if(o===a.top)break;o=o.parent}n||(!function t(){const n=a.document,s=!!a.frames[e];if(!s)if(n.body){const t=n.createElement("iframe");t.style.cssText="display:none",t.name=e,n.body.appendChild(t)}else setTimeout(t,5);return!s}(),a.__tcfapi=function(...e){if(!e.length)return t;"setGdprApplies"===e[0]?e.length>3&&2===parseInt(e[1],10)&&"boolean"==typeof e[3]&&(s=e[3],"function"==typeof e[2]&&e[2]("set",!0)):"ping"===e[0]?"function"==typeof e[2]&&e[2]({gdprApplies:s,cmpLoaded:!1,cmpStatus:"stub"}):t.push(e)},a.addEventListener("message",(function(e){const t="string"==typeof e.data;let a={};if(t)try{a=JSON.parse(e.data)}catch(e){}else a=e.data;const n="object"==typeof a&&null!==a?a.__tcfapiCall:null;n&&window.__tcfapi(n.command,n.version,(function(a,s){let o={__tcfapiReturn:{returnValue:a,success:s,callId:n.callId}};e&&e.source&&e.source.postMessage&&e.source.postMessage(t?JSON.stringify(o):o,"*")}),n.parameter)}),!1))};airgap.ready((t=>{"TCF_EU"===t.loadOptions.consentManagerConfig.initialViewStateByPrivacyRegime[t.getRegimes()[0]]&&("undefined"!=typeof module?module.exports=e:e())}))}();</script>

   <?php

   if (wp_get_environment_type() === 'production') {

   ?>
      <script data-cfasync="false" data-local-sync="allow-network-observable" data-prompt="1" data-report-only="off"
         data-dismissed-view-state="Collapsed" data-tcf-ui-dismissed-view-state="Collapsed" src=https://transcend-cdn.com/cm/ef49a3f1-d8c1-47d6-88fc-50e41130631f/airgap.js></script>
   <?php

   } else {

   ?>
      <script data-cfasync="false" data-local-sync="allow-network-observable" data-prompt="1" data-report-only="off"
         data-dismissed-view-state="Collapsed" data-tcf-ui-dismissed-view-state="Collapsed" src=https://transcend-cdn.com/cm-test/ef49a3f1-d8c1-47d6-88fc-50e41130631f/airgap.js></script>
<?php

   }
}
