<?php

namespace CanaisDigitais\Form;

if (!defined('ABSPATH')) {
   exit;
}

class Utils
{
   public static $has_newsletter_signup_cookie_name = 'bx_cd_has_newsletter_signup';
   static $general_options_page                     = 'general-options';

   static function get_form_by_title(string $title)
   {
      $form = get_posts([
         'post_type' => 'wpcf7_contact_form',
         'posts_per_page' => 1,
         'title' => $title,
         'post_status' => 'publish',
         'fields' => 'ids'
      ]);

      if (empty($form)) {
         return false;
      }

      return do_shortcode("[contact-form-7 id='{$form[0]}']");
   }

   static function is_lead_form_enabled()
   {
      return !empty(get_field('general_options_form_is_active', self::$general_options_page));
   }

   static function get_eloqua_customer_guid()
   {

      $url      = get_field('bx_cd_eloqua_endpoint_customer_guid', self::$general_options_page);
      $url      = add_query_arg('_nocache', time(), $url);
      $response = wp_remote_get($url);

      if (is_wp_error($response) || wp_remote_retrieve_response_code($response) !== 200) {
         return false;
      }

      $body = wp_remote_retrieve_body($response);

      if (preg_match("/return\s+'([^']+)'/", $body, $matches)) {

         return $matches[1];
      }

      return false;
   }

   static function get_lead_form_config_data()
   {
      return  [
         'bx_cd_eloqua_endpoint'    => get_field('bx_cd_eloqua_endpoint', self::$general_options_page),
         'bx_cd_eloqua_customer_id' => self::get_eloqua_customer_guid(),
         'elqSiteId'                => get_field('bx_cd_elqSiteId', self::$general_options_page),
         'elqFormName'              => get_field('bx_cd_elqFormName', self::$general_options_page),
         'bx_cd_form_element_id'    => 'formulario-lead',
         'bx_cd_brand'              => get_field('bx_cd_brand', self::$general_options_page),
      ];
   }

   static function get_header_background()
   {
      return get_field('form_header_background_image', 'general-options');
   }

   static function get_form_title_field()
   {
      return get_field('bx_cd_form_title_newsletter', 'general-options');
   }

   static function get_form_title(string|null $download_content)
   {
      $form_title = self::get_form_title_field();

      if (!empty($download_content)) {
         return esc_html($download_content);
      }

      if (!empty($form_title)) {
         return esc_html($form_title);
      }

      return null;
   }

   static function get_form_description(bool $is_download)
   {
      $download_description = get_field('bx_cd_form_description_download', 'general-options');
      $newsletter_description = get_field('bx_cd_form_description_newsletter', 'general-options');

      if ($is_download && $download_description) {
         return esc_html($download_description);
      } elseif (!$is_download && $newsletter_description) {
         return esc_html($newsletter_description);
      } else {
         return null;
      }
   }

   static function get_form_icon(bool $is_download)
   {
      if ($is_download) {
         return render_svg('download-template-form-download', 'text-primary-500', false);
      }

      if (!empty(self::get_form_title_field())) {
         return render_svg('download-template-form-newsletter', 'text-primary-500', false);
      }
   }
}
