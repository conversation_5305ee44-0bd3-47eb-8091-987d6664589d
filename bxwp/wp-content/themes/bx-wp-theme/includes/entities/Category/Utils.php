<?php

namespace CanaisDigitais\Category;

if (!defined('ABSPATH')) {
   exit;
}

use <PERSON><PERSON><PERSON><PERSON>tais\Author\Utils as AuthorUtils;
use CanaisDigitais\Category\Category;
use CanaisDigitais\CD_Post\Utils as PostUtils;

class Utils
{

   public static $default_categories = [
      'event'   => [
         'slug' => 'eventos',
         'name' => 'Eventos'
      ],
      'podcast' => [
         'slug' => 'podcasts',
         'name' => 'Podcasts'
      ],
      'video'   => [
         'slug' => 'videos',
         'name' => 'Vídeos'
      ]
   ];

   public static function get_default_categories()
   {
      return self::$default_categories;
   }

   public static function get_default_categories_ids()
   {
      $default_categories_names = array_column(self::get_default_categories(), 'name');
      $default_category_ids     = [];

      foreach ($default_categories_names as $category_name) {
         $term = get_term_by('name', $category_name, 'category');
         if (!empty($term)) {
            $default_category_ids[] = $term->term_id;
         }
      }

      return $default_category_ids;
   }

   public static function get_default_category_slug($category_name)
   {
      $default_categories = self::get_default_categories();

      if (!isset($default_categories[$category_name])) {
         return;
      }

      return $default_categories[$category_name]['slug'];
   }


   public static function get_default_category_url($post_format)
   {
      $default_categories = self::get_default_categories();

      if (!isset($default_categories[$post_format])) {
         return null;
      }

      $Category = new Category(get_term_by(
         'name',
         $default_categories[$post_format]['name'],
         'category'
      ));

      return $Category->get_link();
   }

   public static function get_child_categories($parent_category_ID, bool $only_ID = false)
   {
      $categories = get_terms([
         'taxonomy'   => 'category',
         'parent'     => $parent_category_ID,
         'hide_empty' => false,
         'fields'     => empty($only_ID) ? 'all' : 'ids',
      ]);

      if (empty($categories)) {
         return [];
      }

      return $categories;
   }

   public static function filter_special_page_posts($chosen_posts = [], $latest_posts = [], $max_posts = 5)
   {
      global $excluded_posts_ID;

      $chosen_posts = array_filter($chosen_posts);
      $latest_posts = array_filter($latest_posts);

      if (!empty($chosen_posts)) {
         $chosen_posts = array_diff($chosen_posts, $excluded_posts_ID);
      }

      if (count($chosen_posts) < $max_posts) {
         $chosen_posts = array_merge($chosen_posts, array_slice($latest_posts, 0, $max_posts - count($chosen_posts)));
      }

      if (!empty($chosen_posts)) {
         $excluded_posts_ID = array_merge($excluded_posts_ID, $chosen_posts);
      }

      return $chosen_posts;
   }

   public static function get_special_page_specialists_with_articles($category_id)
   {
      global $excluded_posts_ID;

      $filtered_specialists = [];

      $Category = new Category($category_id);
      $specialists = $Category->get_special_page_specialists();

      if (empty($specialists)) {
         $specialists = AuthorUtils::get_all_authors('ids', 'specialist');
      }

      if (empty($specialists)) {
         return [];
      }

      foreach ($specialists as $specialist) {
         $specialist_id = $specialist['author_id'] ?? $specialist;
         $articles = [];
         $featured_article = [];
         $posts_ID = [];

         $latest_articles = PostUtils::get_latest_articles_by_author_term_id($specialist_id, 4, $excluded_posts_ID, 0, 1, $category_id);

         if (!empty($latest_articles)) {
            $latest_articles = array_column($latest_articles, 'ID');
         }

         if (!empty($specialist['specialist_articles'])) {
            $posts_ID = array_column($specialist['specialist_articles'], 'single_article');
            $posts_ID = array_diff($posts_ID, $excluded_posts_ID);
            $posts_ID = array_filter($posts_ID);
         }

         if (!empty($latest_articles) && count($posts_ID) < 4) {
            $posts_ID = array_merge($posts_ID, array_slice($latest_articles, 0, 4 - count($posts_ID)));
         }

         $featured_article = reset($posts_ID);
         $articles = array_slice($posts_ID, 1, 4);

         if (!empty($featured_article)) {
            $excluded_posts_ID[] = $featured_article;
         }

         if (!empty($articles)) {
            $excluded_posts_ID = array_merge($excluded_posts_ID, $articles);
         }

         $filtered_specialists[] = [
            'author_id'        => $specialist_id,
            'articles'         => $articles,
            'featured_article' => $featured_article
         ];
      }

      return $filtered_specialists;
   }

   public static function get_special_page_most_read_section_articles($category_id)
   {
      global $excluded_posts_ID;

      $Category = new Category($category_id);

      $most_read_articles = (array) $Category->get_special_page_most_read();
      $most_read_latest = (array) PostUtils::get_most_read_articles(4, $most_read_period ?? 7, $excluded_posts_ID, null, $category_id);
      $most_read_period = (int) $Category->get_special_page_most_read_period();

      return self::filter_special_page_posts($most_read_articles, $most_read_latest, 4);
   }

   public static function get_special_page_events_posts($category_id, $max_posts = 5, $is_slider = false)
   {
      global $excluded_posts_ID;

      $Category = new Category($category_id);

      if ($is_slider) {
         $chosen_events = (array) $Category->get_special_page_slider_events_posts();
      } else {
         $chosen_events = (array) $Category->get_special_page_other_events_posts();
      }

      $latest_events = (array) PostUtils::get_events_posts($max_posts, $excluded_posts_ID, $category_id);

      return self::filter_special_page_posts($chosen_events, $latest_events, $max_posts);
   }

   public static function get_videos($category_id)
   {
      global $excluded_posts_ID;

      $Category = new Category($category_id);

      $chosen_videos = (array) $Category->get_special_page_videos();
      $latest_videos = (array) PostUtils::get_videos(5, $excluded_posts_ID, $category_id);

      return self::filter_special_page_posts($chosen_videos, $latest_videos, 5);
   }

   public static function get_special_page_featured_materials($category_id)
   {
      global $excluded_posts_ID;

      $Category = new Category($category_id);

      $chosen_materials = (array) $Category->get_special_page_featured_materials();
      $latest_materials = (array) PostUtils::get_latest_articles_ids(7, $excluded_posts_ID, $category_id);

      $all_posts = self::filter_special_page_posts($chosen_materials, $latest_materials, 7);

      $download = (array) $Category->get_special_page_featured_materials_download();
      $download = array_filter($download);
      $download = array_diff($download, $excluded_posts_ID, $all_posts);

      if (!empty($download)) {
         $download = reset($download);
         $excluded_posts_ID[] = $download;
      }

      return [
         'materials' => $all_posts,
         'download'  => $download,
         'title'     => $Category->get_special_page_featured_materials_title(),
      ];
   }

   public static function get_special_page_categories_list($category_id)
   {
      $Category = new Category($category_id);

      $categories_list = $Category->get_special_page_categories();

      if (empty($categories_list)) {
         return [];
      }

      return $categories_list;
   }

   public static function get_special_page_category_sections_with_articles($category_id, $categories_sections)
   {
      global $excluded_posts_ID;

      $categories_sections = array_filter($categories_sections, function ($item) use ($category_id) {
         return $item['category_id'] === $category_id;
      });

      if (empty($categories_sections)) {
         return [];
      }

      $category_section = reset($categories_sections);

      $chosen_articles = [];

      if (!empty($category_section['category_articles'])) {
         $chosen_articles = (array) array_column($category_section['category_articles'], 'category_single_article');
      }

      $latest_articles  = PostUtils::get_latest_posts_by_category_id($category_id, 6, array_merge($excluded_posts_ID, $chosen_articles));
      $latest_articles = array_column($latest_articles, 'ID');

      $all_articles = self::filter_special_page_posts($chosen_articles, $latest_articles, 6);

      return [
         'category_id' => $category_id,
         'news'        => $all_articles,
      ];
   }

   public static function get_special_page_videos_basic_info($category_id)
   {
      $Category = new Category($category_id);

      return [
         'title'       => $Category->get_special_page_category_videos_title(),
         'description' => $Category->get_special_page_category_videos_text(),
      ];
   }

   public static function get_special_page_events_basic_info($category_id)
   {
      $Category = new Category($category_id);

      return [
         'title'       => $Category->get_special_page_category_events_title(),
         'description' => $Category->get_special_page_category_events_description(),
      ];
   }

   public static function get_categories()
   {
      return get_categories([
         'taxonomy' => 'category'
      ]);
   }
}
