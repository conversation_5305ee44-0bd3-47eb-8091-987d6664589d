<?php

namespace CanaisDigitais\CD_Post;

use <PERSON>ais<PERSON><PERSON>tais\Category\Utils;

if (!defined('ABSPATH')) exit;

class Register
{
   public function __construct()
   {
      add_action('wp', [$this, 'register_new_post_view']);
      add_action('init', [$this, 'register_post_fields'], 10);
      add_action('init', [$this, 'custom_permalink_structure'], 20);
      add_filter('bx_cd_post_fields', [$this, 'all_format_fields'], 5);
      add_filter('bx_cd_post_fields', [$this, 'video_format_fields'], 10);
      add_filter('bx_cd_post_fields', [$this, 'download_material_format_fields'], 20);
      add_filter('bx_cd_post_fields', [$this, 'podcast_format_fields'], 30);
      add_filter('bx_cd_post_fields', [$this, 'related_posts_fields'], 40);

      add_filter('acf/fields/relationship/query/name=related_posts', [$this, 'modify_related_posts_query'], 10, 1);
   }
   public function custom_permalink_structure()
   {
      $new_permalink_structure     = '/%category%/%postname%/';
      $current_permalink_structure = get_option('permalink_structure');

      if ($current_permalink_structure === $new_permalink_structure) {
         return;
      }

      update_option('permalink_structure', $new_permalink_structure);
   }

   public function register_new_post_view()
   {
      if (function_exists('is_bot_user_agent')) {
         if (\is_bot_user_agent())
            return;
      }

      if (\wp_doing_ajax() || \is_admin())
         return;

      /**
       * Verificação para evitar que o hook seja executado duas vezes seguidas.
       * Ref: https://www.benmarshall.me/wordpress-hook-fire-once/
       */
      if (
         !\is_singular() &&
         !\is_page() &&
         !\is_single() &&
         !\is_archive() &&
         !\is_home() &&
         !\is_front_page()
      )
         return;

      if (!\is_single() || \is_page())
         return;

      $this->update_post_views(\get_the_ID());
   }

   private function update_post_views($post_ID)
   {
      $this->update_post_views_metas($post_ID, 7);
      $this->update_post_views_metas($post_ID, 15);
   }

   private function update_post_views_metas($post_ID, $period_in_days)
   {
      if (empty($post_ID) || empty($period_in_days))
         return;

      $meta_key_count = "count_views_by_{$period_in_days}_days";
      $meta_key_sum = "count_views_sum_{$period_in_days}_days";

      $now            = new \DateTime();
      $today          = $now->format('Y-m-d');
      $views_per_date = \get_post_meta($post_ID, $meta_key_count, true);
      $count          = 0;

      if (empty($views_per_date)) {
         $views_per_date = [$today => 0];
      }

      if (!isset($views_per_date[$today])) {
         $views_per_date[$today] = 0;
      }

      $ago_days = strtotime('-' . $period_in_days . ' days');

      foreach ($views_per_date as $date => $views) {
         if (strtotime($date) < $ago_days) {
            unset($views_per_date[$date]);
            continue;
         }

         $views = (int) $views;

         if ($date === $today) {
            $views += 1;
            $views_per_date[$date] = $views;
         }

         $count += $views;
      }

      \update_post_meta($post_ID, $meta_key_count, $views_per_date);
      \update_post_meta($post_ID, $meta_key_sum, $count);
   }

   public function all_format_fields($fields)
   {
      $fields[] = [
         array(
            'key' => 'field_65ba935977ae5',
            'label' => 'Byline',
            'name' => 'post_byline',
            'aria-label' => '',
            'type' => 'text',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id' => '',
            ),
            'default_value' => '',
            'maxlength' => '',
            'placeholder' => '',
            'prepend' => '',
            'append' => '',
         ),
      ];

      return $fields;
   }
   public function video_format_fields($fields)
   {
      $fields[] = [
         array(
            'key' => 'field_65aec16f075b1',
            'label' => 'Opções de Vídeo',
            'name' => '',
            'aria-label' => '',
            'type' => 'tab',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id' => '',
            ),
            'placement' => 'top',
            'endpoint' => 0,
         ),
         array(
            'key' => 'field_6564ce8258278',
            'label' => 'Embed de Vídeo',
            'name' => 'video_single_embed',
            'aria-label' => '',
            'type' => 'oembed',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id' => '',
            ),
            'width' => '',
            'height' => '',
         ),
      ];

      return $fields;
   }

   public function podcast_format_fields($fields)
   {
      $fields[] = [
         array(
            'key' => 'field_954ec16f075c1',
            'label' => 'Opções de Podcast',
            'name' => '',
            'aria-label' => '',
            'type' => 'tab',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id' => '',
            ),
            'placement' => 'top',
            'endpoint' => 0,
         ),
         array(
            'key' => 'field_9564ce8258289',
            'label' => 'Embed de Podcast',
            'name' => 'podcast_single_embed',
            'aria-label' => '',
            'type' => 'oembed',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id' => '',
            ),
            'width' => '',
            'height' => '',
         ),
      ];

      return $fields;
   }

   public function download_material_format_fields($fields)
   {
      $fields[] = [
         array(
            'key' => 'field_65aec16f085b2',
            'label' => 'Opções de Materiais de Download',
            'name' => '',
            'aria-label' => '',
            'type' => 'tab',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id' => '',
            ),
            'placement' => 'top',
            'endpoint' => 0,
         ),
         array(
            'key'           => 'field_6553045174b01',
            'label'         => 'Upload de Arquivo',
            'name'          => 'download_material_file_upload',
            'type'          => 'file',
            'required'      => 0,
            'return_format' => 'id',
         ),
      ];

      return $fields;
   }

   public function related_posts_fields($fields)
   {
      $fields[] = [
         array(
            'key' => 'field_66aed16f075b4',
            'label' => 'Opções de Posts Relacionados',
            'name' => '',
            'aria-label' => '',
            'type' => 'tab',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id' => '',
            ),
            'placement' => 'top',
            'endpoint' => 0,
         ),
         array(
            'key' => 'field_6565efd4351a0',
            'label' => 'Posts relacionados',
            'name' => 'related_posts',
            'aria-label' => '',
            'type' => 'relationship',
            'instructions' => '',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
               'width' => '',
               'class' => '',
               'id' => '',
            ),
            'post_type' => array(
               0 => 'post',
            ),
            'post_status' => array(
               0 => 'publish',
            ),
            'filters' => array(
               0 => 'search',
            ),
            'return_format' => 'id',
            'multiple' => 1,
            'allow_null' => 1,
            'ui' => 1,
         ),
      ];

      return $fields;
   }

   public function modify_related_posts_query($args)
   {
      if (empty($args['s'])) {
         $args['orderby'] = 'date';
         $args['order']   = 'DESC';
      }

      return $args;
   }

   public function register_post_fields()
   {
      if (!function_exists('acf_add_local_field_group')) {
         return;
      }

      acf_add_local_field_group(array(
         'key'    => 'group_6377fda3dc1f3',
         'title'  => 'Opções',
         'fields' => array_merge(...apply_filters('bx_cd_post_fields', [])),
         'location' => array(
            array(
               array(
                  'param'    => 'post_type',
                  'operator' => '==',
                  'value'    => 'post',
               ),
            ),
         ),
         'menu_order'            => 0,
         'position'              => 'normal',
         'style'                 => 'default',
         'label_placement'       => 'top',
         'instruction_placement' => 'label',
         'hide_on_screen'        => '',
         'active'                => true,
         'description'           => '',
         'show_in_rest'          => 0,
      ));
   }
}
