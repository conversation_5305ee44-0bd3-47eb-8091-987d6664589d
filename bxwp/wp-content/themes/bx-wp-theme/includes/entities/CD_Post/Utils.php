<?php

namespace CanaisDigitais\CD_Post;

use <PERSON>ais<PERSON><PERSON>tais\Category\Category;
use Canais<PERSON><PERSON>tais\CD_Post\Utils as CD_PostUtils;
use CanaisDigitais\Category\Utils as CategoryUtils;
use <PERSON>ais<PERSON><PERSON>tais\Page\Utils as PageUtils;

if (!defined('ABSPATH')) {
   exit;
}

class Utils
{
   static $homepage_option_page = 'homepage-options';

   static function category_archive_get_child_section_posts($parent_category)
   {
      $child_categories = CategoryUtils::get_child_categories($parent_category);

      if (empty($child_categories)) {
         return [];
      }

      $filtered_sections = [];

      foreach ($child_categories as $child_category) {
         $filtered_sections[$child_category->slug] = [
            'category_id' => $child_category->term_id,
            'news'        => array_column(
               self::get_latest_posts_by_category_id($child_category->term_id, 6),
               'ID'
            ),
         ];
      }

      return $filtered_sections;
   }

   static function get_latest_posts_by_category_id($category_id, $numberposts = 4, $posts_not_in = [])
   {
      if (empty($category_id)) {
         return null;
      }

      $args = [
         'post_type'   => 'post',
         'numberposts' => $numberposts,
         'order'       => 'DESC',
         'tax_query'   => [
            [
               'taxonomy' => 'category',
               'field'    => 'term_id',
               'terms'    => [$category_id],
               'operator' => 'IN',
            ],
         ],
         'post__not_in' => $posts_not_in,
      ];

      return get_posts($args);
   }

   static function get_homepage_category_sections_with_articles(int $category_id = null)
   {
      global $excluded_posts_ID;

      $sections = get_field('homepage_categories', self::$homepage_option_page);

      if (is_int($category_id)) {
         $sections = array_filter($sections, function ($item) use ($category_id) {
            return $item['category_id'] === $category_id;
         });
      }

      if (!is_array($sections)) {
         return;
      }

      $filtered_sections = [];

      foreach ($sections as $section) {
         $articles = [];

         $category_id = $section['category_id'];

         $show_latest_ones    = isset($section['show_latest_articles']) ? $section['show_latest_articles'] : false;
         $has_chosen_articles = !empty($section['category_articles']);
         $chosen_articles     = $has_chosen_articles ? array_column($section['category_articles'], 'category_single_article') : [];

         $latest_articles = array_column(CD_PostUtils::get_latest_posts_by_category_id($category_id, 6, array_merge($excluded_posts_ID, $chosen_articles)), 'ID');

         if ($has_chosen_articles == true && $show_latest_ones === false) {
            $chosen_articles = array_diff($chosen_articles, $excluded_posts_ID);

            $count_articles = count($chosen_articles);
            $articles       = array_merge($chosen_articles, array_slice($latest_articles, 0, 6 - $count_articles));
         } else {
            $articles = $latest_articles;
         }

         if (empty($articles)) {
            continue;
         }

         $filtered_sections[] = [
            'category_id' => $category_id,
            'news'        => $articles,
         ];

         $excluded_posts_ID = array_merge($excluded_posts_ID, $articles);
      }

      return $filtered_sections;
   }

   public static function get_latest_articles_by_author_term_id($author_id, $numberposts = 4, $posts_not_in = [], $posts_per_page = 0, $paged = 1, int|null $category_id = null)
   {
      if (empty($author_id)) {
         return null;
      }

      $args = [
         'post_type'     => 'post',
         'numberposts'   => $numberposts,
         'order'         => 'DESC',
         'tax_query'     => [
            [
               'taxonomy' => 'author_tax',
               'field'    => 'term_id',
               'terms'    => [$author_id],
            ],
         ],
         'post__not_in' => $posts_not_in,
      ];

      if (!empty($category_id)) {
         $Category = new Category($category_id);
         $args['category_name'] = $Category->get_slug();
      }

      if ($posts_per_page !== 0 && $paged > 0) {
         $args['posts_per_page'] = $posts_per_page;
         $args['paged']          = $paged;

         return new \WP_Query($args);
      }

      return get_posts($args);
   }

   public static function get_latest_articles_ids(int $numberposts = 4, array $posts_not_in = [], int|null $category_id = null)
   {
      $args = [
         'post_type'     => 'post',
         'numberposts'   => $numberposts,
         'post__not_in'  => $posts_not_in,
         'fields'        => 'ids',
      ];

      if (!empty($category_id)) {
         $Category = new Category($category_id);
         $args['category_name'] = $Category->get_slug();
      }

      return get_posts($args);
   }

   public static function get_latest_download_material_ids(int $numberposts = 1, array $posts_not_in = [], $category_id = null)
   {
      $args = [
         'post_type'    => 'post',
         'numberposts'  => $numberposts,
         'post__not_in' => $posts_not_in,
         'fields'       => 'ids',
         'meta_query'   => [
            'relation' => 'AND',
            [
               'key'     => 'download_material_file_upload',
               'value'   => '',
               'compare' => '!=',
            ],
         ],
         'no_found_rows' => true,
      ];

      if (!empty($category_id)) {
         $args['tax_query'] = [
            [
               'taxonomy' => 'category',
               'field'    => 'term_id',
               'terms'    => [$category_id],
            ],
         ];
      }

      return get_posts($args);
   }

   public static function get_most_read_articles(int $numberposts = 4, int $period_in_days = 15, array $posts_not_in = [], int|null $paged = null, int|null $category_id = null)
   {
      $args = [
         'post_type'      => 'post',
         'posts_per_page' => $numberposts,
         'meta_key'       => "count_views_sum_{$period_in_days}_days",
         'order'          => 'DESC',
         'orderby'        => 'meta_value_num',
         'post__not_in'   => $posts_not_in,
         'fields'         => 'ids',
      ];

      if (!empty($category_id)) {
         $Category = new Category($category_id);
         $args['category_name'] = $Category->get_slug();
      }

      if (!empty($paged)) {
         $args['paged'] = $paged;

         return new \WP_Query($args);
      }

      return get_posts($args);
   }

   public static function get_videos(int $numberposts = 5, array $posts_not_in = [], int|null $category_id = null)
   {
      $args = [
         'post_type'      => 'post',
         'posts_per_page' => $numberposts,
         'fields'         => 'ids',
         'post__not_in'   => $posts_not_in,
         'category_name'  => CategoryUtils::get_default_category_slug('video')
      ];

      if (!empty($category_id)) {
         $Category = new Category($category_id);
         $args['category_name'] = $Category->get_slug();

         unset($args['category_name']);

         $args['category__and'] = [
            $Category->get_the_ID(),
            get_category_by_slug(CategoryUtils::get_default_category_slug('video'))->term_id
         ];
      }

      return get_posts($args);
   }

   public static function get_homepage_most_read_section_articles()
   {
      global $excluded_posts_ID;

      $chosen_articles = get_field('homepage_most_read_articles', self::$homepage_option_page) ?: [];
      $show_most_read = get_field('show_homepage_most_read_articles', self::$homepage_option_page);
      $period_most_read_articles = ($show_most_read) ? (int)get_field('period_homepage_most_read_articles', self::$homepage_option_page) : 7;
      $chosen_articles = !empty($chosen_articles)
         ? array_diff(array_column($chosen_articles, 'most_read_article'), $excluded_posts_ID)
         : [];

      if ($show_most_read) {
         $most_read_articles = self::get_most_read_articles(4, $period_most_read_articles, $excluded_posts_ID);
      } else {
         $most_read_articles = self::get_most_read_articles(4, $period_most_read_articles, array_merge($excluded_posts_ID, $chosen_articles));

         if (!empty($chosen_articles)) {
            $most_read_articles = array_merge($chosen_articles, array_slice($most_read_articles, 0, 4 - count($chosen_articles)));
            $excluded_posts_ID = array_merge($excluded_posts_ID, $chosen_articles);
         }
      }

      $excluded_posts_ID = array_merge($excluded_posts_ID, $most_read_articles);

      return $most_read_articles;
   }


   static function get_events_posts($number = 6, $not_in = [], $category_id = null)
   {
      $args = [
         'numberposts'   => $number,
         'post_type'     => 'post',
         'post_status'   => 'publish',
         'order'         => 'DESC',
         'post__not_in'  => $not_in,
         'fields'        => 'ids',
         'category_name' => CategoryUtils::get_default_category_slug('event')
      ];

      if (!empty($category_id)) {
         $Category = new Category($category_id);
         $args['category_name'] = $Category->get_slug();

         unset($args['category_name']);

         $args['category__and'] = [
            $Category->get_the_ID(),
            get_category_by_slug(CategoryUtils::get_default_category_slug('event'))->term_id
         ];
      }

      return get_posts($args);
   }

   static function get_homepage_events_basic_info()
   {
      return [
         'title'       => get_field('homepage_events_title', self::$homepage_option_page),
         'description' => get_field('homepage_events_description', self::$homepage_option_page)
      ];
   }

   static function get_homepage_events_slider_posts()
   {
      global $excluded_posts_ID;

      $chosen_events = get_field('homepage_slider_events_posts', self::$homepage_option_page) ?: [];
      $show_latest = get_field('homepage_show_recent_events_for_slider', self::$homepage_option_page);

      $chosen_events = !empty($chosen_events)
         ? array_diff(array_column($chosen_events, 'single_event_post'), $excluded_posts_ID)
         : [];

      $latest_events = self::get_events_posts(5, $show_latest ? $excluded_posts_ID : array_merge($excluded_posts_ID, $chosen_events));

      $events = (!$show_latest && !empty($chosen_events))
         ? array_slice(array_merge($chosen_events, $latest_events), 0, 5)
         : $latest_events;

      $excluded_posts_ID = array_merge($excluded_posts_ID, $events);

      return $events;
   }


   static function get_homepage_events_other_posts()
   {
      global $excluded_posts_ID;

      $chosen_events     = get_field('homepage_events_other_posts', self::$homepage_option_page);
      $show_latest       = get_field('homepage_show_recent_events_for_other_posts', self::$homepage_option_page);
      $has_chosen_events = !empty($chosen_events);
      $chosen_events     = $has_chosen_events ? array_column($chosen_events, 'single_event_post') : [];
      $chosen_events     = array_diff($chosen_events, $excluded_posts_ID);

      if ($has_chosen_events == true && $show_latest === false) {
         $count_events = count($chosen_events);

         $latest_filtered_events = self::get_events_posts(4, array_merge($chosen_events, $excluded_posts_ID));

         $events = array_merge($chosen_events, array_slice($latest_filtered_events, 0, 4 - $count_events));

         $excluded_posts_ID = array_merge($excluded_posts_ID, $events);
      } else {
         $events = self::get_events_posts(4, array_merge($excluded_posts_ID));

         $excluded_posts_ID = array_merge($excluded_posts_ID, $events);
      }

      return $events;
   }

   static function get_homepage_videos()
   {
      global $excluded_posts_ID;

      $selected_videos = get_field('home_videos', self::$homepage_option_page);

      if (empty($selected_videos)) {
         $selected_videos = [];
      }

      $selected_videos = array_diff($selected_videos, $excluded_posts_ID);

      if (count($selected_videos) < 5) {
         $additional_videos_count = 5 - count($selected_videos);

         $additional_videos = self::get_videos(
            $additional_videos_count,
            array_merge($excluded_posts_ID, $selected_videos)
         );

         $selected_videos = array_merge($selected_videos, $additional_videos);
      } else {
         $selected_videos = array_slice($selected_videos, 0, 5);
      }

      $excluded_posts_ID = array_merge($excluded_posts_ID, $selected_videos);

      return $selected_videos;
   }

   static function get_homepage_videos_basic_info()
   {
      return [
         'title'       => get_field('home_videos_title', self::$homepage_option_page),
         'description' => get_field('home_videos_text', self::$homepage_option_page)
      ];
   }

   static function get_download_material_signup_link()
   {
      $Post = new CD_Post();
      $download_material_ID = (int) $Post->get_download_material_file_id();

      if (empty($download_material_ID) || empty(PageUtils::get_form_newsletter_page())) {
         return;
      }

      $Newsletter_Page = new CD_Post(PageUtils::get_form_newsletter_page());

      return esc_url($Newsletter_Page->get_link() . '?ContentName=' . esc_attr($Post->get_title()) . '&download-id=' . $download_material_ID);
   }
}
