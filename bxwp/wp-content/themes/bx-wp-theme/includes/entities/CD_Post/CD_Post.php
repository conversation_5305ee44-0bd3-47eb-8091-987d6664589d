<?php

/**
 * Name: CD_Post
 * Type: Post
 * Taxonomies: category, tag, tax_author
 * Custom Fields: byline do autor, embed de vídeo, embed de podcast, arquivo de material de download, lista de posts relacionados
 * Description: Post padrão.
 */

namespace CanaisDigitais\CD_Post;

use CanaisDigitais\Category\Category;
use CanaisDigitais\Author\Author;
use CanaisDigitais\Category\Utils as CategoryUtils;

use CanaisDigitais\CD_Post\Utils as PostUtils;

if (!defined('ABSPATH')) {
   exit;
}

class CD_Post
{
   protected $post;
   protected $ID = 0;

   public function __construct($post = null)
   {
      if (is_null($post)) {
         $this->post = \get_post();
      } elseif (is_numeric($post)) {
         $this->post = \get_post($post);
      } elseif (is_a($post, 'WP_Post')) {
         $this->post = $post;
      }

      $this->ID = (int) $this->post->ID;
   }

   public function get_the_ID()
   {
      return $this->ID;
   }

   public function get_title()
   {
      return \apply_filters('the_title', $this->post->post_title, $this->ID);
   }

   public function has_thumbnail()
   {
      return \has_post_thumbnail($this->ID);
   }

   public function get_thumbnail($size = 'medium', $attrs = [])
   {
      return \get_the_post_thumbnail($this->post, $size, $attrs);
   }

   public function get_thumbnail_url($size = 'medium')
   {
      return \get_the_post_thumbnail_url($this->post, $size);
   }

   public function get_thumbnail_caption()
   {
      return \get_the_post_thumbnail_caption($this->ID);
   }

   public function get_link()
   {
      return \get_permalink($this->post);
   }

   public function get_terms($taxonomy)
   {
      return \get_the_terms($this->ID, $taxonomy);
   }

   public function get_post_terms($taxonomy = 'post_tag', $args = [])
   {
      return \wp_get_post_terms($this->ID, $taxonomy, $args);
   }

   public function has_term($term_slug, $taxonomy = 'post_tag')
   {
      $post_terms_list = $this->get_terms($taxonomy);

      if (empty($post_terms_list)) {
         return false;
      }

      foreach ($post_terms_list as $post_term) {
         if ($post_term->slug === $term_slug) {
            return true;
         }
      }

      return false;
   }

   public function get_date($format = 'd \d\e F, Y')
   {
      return \get_the_date($format, $this->ID);
   }

   public function get_category($category_type = '', $exclude_default_categories = true)
   {
      if ($exclude_default_categories === true) {
         $exclude_categories = CategoryUtils::get_default_categories_ids();
      } else {
         $exclude_categories = [];
      }

      $args = [
         'exclude' => $exclude_categories,
      ];

      if (!empty($category_type)) {
         $args['meta_query'] = [
            [
               'key'     => 'category_type',
               'value'   => $category_type,
               'compare' => '=',
            ],
         ];
      }

      $categories = $this->get_post_terms('category', $args);

      if (empty($categories) || is_wp_error($categories)) {
         return;
      }

      if (function_exists('yoast_get_primary_term_id') && !empty(yoast_get_primary_term_id('category', $this->ID))) {
         $first_category = get_category(yoast_get_primary_term_id('category', $this->ID));
      } else {
         $first_category = reset($categories);
      }

      return new Category($first_category);
   }

   public function get_format($output = 'key')
   {
      $default_categories = CategoryUtils::get_default_categories();
      $post_categories    = array_column((array)$this->get_terms('category'), 'name');

      foreach ($default_categories as $key => $category) {
         if (!in_array($category['name'], $post_categories)) {
            continue;
         }

         return $output === 'key' ? $key : $category['name'];
      }
   }

   public function has_gallery()
   {
      return \has_block('acf/cd-slideshow', $this->ID);
   }

   public function get_excerpt()
   {
      return \get_the_excerpt($this->ID);
   }

   public function has_excerpt()
   {
      return \has_excerpt($this->ID);
   }

   public function get_content()
   {
      return \apply_filters('the_content', $this->post->post_content);
   }

   public function get_download_material_file_id()
   {
      return get_field('download_material_file_upload', $this->ID);
   }

   public function get_reading_time()
   {
      $post_content = \get_the_content();

      if (empty($post_content)) {
         return;
      }

      $reading_time = ceil(str_word_count(strip_tags($post_content)) / 228);

      return \esc_html(sprintf(_n('%s minuto', '%s minutos', $reading_time, 'canais_digitais'), $reading_time));
   }

   public function get_authors($type = '', $strong = true)
   {
      $all_authors = $this->get_terms('author_tax');

      if (empty($all_authors) || \is_wp_error($all_authors)) {
         return;
      }

      $all_authors = array_map(function ($author) use ($type, $strong) {
         $author = new Author($author);

         if (!empty($type)) {
            if ($author->get_author_type() !== $type) {
               return;
            }
         }

         return $author->display($strong);
      }, $all_authors);

      if (count($all_authors) === 1) {
         return implode('', $all_authors);
      }

      if (count($all_authors) === 2) {
         return implode(' e ', $all_authors);
      }

      if (count($all_authors) >= 3) {
         $last_author = $all_authors[\array_key_last($all_authors)];

         unset($all_authors[\array_key_last($all_authors)]);

         return implode(', ', $all_authors) . ' e ' . $last_author;
      }
   }

   public function get_byline()
   {
      return esc_html(get_field('post_byline', $this->ID));
   }

   public function get_share_buttons($nav_class = '', $list_class = '', $link_class = '')
   {
      $title = rawurlencode($this->get_title());
      $url   = esc_url($this->get_link());

      $share_links = [
         [
            'title'     => esc_html__('Copiar url da notícia', 'canais-digitais'),
            'icon_name' => 'copy',
         ],
         [
            'title'     => esc_html__('Imprimir notícia', 'canais-digitais'),
            'icon_name' => 'print',
         ],
         [
            'url'       => 'https://www.linkedin.com/shareArticle?mini=true&url=' . $url . '&title=' . $title,
            'title'     => esc_html__('Compartilhar no LinkedIn', 'canais_digitais'),
            'icon_name' => 'linkedin',
         ],
         [
            'url'       => 'https://twitter.com/intent/tweet?text=' . $title . '&url=' . $url,
            'title'     => esc_html__('Compartilhar no Twitter', 'canais_digitais'),
            'icon_name' => 'twitter',
         ],
         [
            'url'       => 'https://wa.me/?text=' . $title . ' ' . $url,
            'title'     => esc_html__('Compartilhar no WhatsApp', 'canais_digitais'),
            'icon_name' => 'whatsapp',
         ],
         [
            'url'       => 'https://www.facebook.com/sharer/sharer.php?u=' . $url . '&quote=' . $title,
            'title'     => esc_html__('Compartilhar no Facebook', 'canais_digitais'),
            'icon_name' => 'facebook',
         ],
         [
            'title'     => esc_html__('Outros compartilhamentos', 'canais_digitais'),
            'icon_name' => 'ph_share-network',
         ],
      ];

      $has_download_material = (bool) $this->has_download_material();

      if (!empty($has_download_material)) {
         $download_material_signup_link = PostUtils::get_download_material_signup_link();
         $download_material_ID = (int) $this->get_download_material_file_id();

         $share_links[] = [
            'url'       => esc_url($download_material_signup_link),
            'title'     => esc_html__('Baixar material para download', 'canais-digitais'),
            'icon_name' => 'download-paywall',
         ];

         $share_links[] = [
            'icon_name' => 'download-button',
            'title'     => esc_html__('Baixar material para download', 'canais-digitais'),
         ];
      }

      $content = '<nav class="' . $nav_class . '" aria-label="' . esc_attr__('Compartilhar conteúdo', 'canais_digitais') . '">';

      $content .= '<ul class="' . $list_class . '">';

      foreach ($share_links as $link) {
         if ($link['icon_name'] === 'download-paywall') {
            $content .= '<li x-cloak x-data="newsletterSignUpCheck" x-show="!isCookieSet">';
         } else if ($link["icon_name"] === 'download-button') {
            $content .= '<li x-cloak x-data="newsletterSignUpCheck" x-show="isCookieSet">';
         } else {
            $content .= '<li>';
         }

         if ($link['icon_name'] === 'print') {
            $content .= '<a href="#" title="' . $link['title'] . '" class="' . $link_class . '" onclick="window.print()" target="_blank">';
         } else if ($link['icon_name'] === 'ph_share-network') {
            $content .= '<a href="#" title="' . $link['title'] . '" class="' . $link_class . '" js-share-network>';
         } else if ($link['icon_name'] === 'copy') {
            $content .= '<a href="#" title="' . $link['title'] . '" class="' . $link_class . '" js-copy-url>';
         } else if ($link['icon_name'] === 'download-button') {
            $content .= '<a id="download-button" data-file-id="' . $download_material_ID . '" class="download-button-link ' . $link_class . '" role="button">';
         } else {
            $content .= '<a href="' . $link['url'] . '" title="' . $link['title'] . '" class="' . $link_class . '" target="_blank">';
         }

         if ($link['icon_name'] === 'download-button') {
            $content .= "<span class='ph icon-[ph-download-simple] [mask-size:1.4rem] [mask-position:center]'></span>";
         } else {
            $content .= render_svg("social-media/{$link['icon_name']}", 'fill-neutral-700', false);
         }

         $content .= '</a>';
         $content .= '</li>';
      }

      $content .= '</ul>';
      $content .= '</nav>';

      return $content;
   }

   public function get_embed(array $attributes = [], $type = 'video')
   {
      if ($type == 'video') {
         $embed = get_field('video_single_embed', $this->ID);
      } else if ($type == 'podcast') {
         $embed = get_field('podcast_single_embed', $this->ID);
      }

      if (empty($embed) || empty($attributes)) {
         return $embed;
      }

      $_attributes = '';

      foreach ($attributes as $attribute_name => $attribute_value) {
         $_attributes .= $attribute_name . '=' . "'{$attribute_value}'" . ' ';
      }

      $embed_with_attributes = str_replace('></iframe>', ' ' . $_attributes . '></iframe>', $embed);

      return $embed_with_attributes;
   }

   public function get_event_start_date($date_format = 'd \d\e F, Y')
   {
      $Category = $this->get_category('event');

      if (empty($Category)) {
         return;
      }

      $event_start_date = $Category->get_event_start_date('', true);

      if (empty($event_start_date) || !strtotime($event_start_date)) {
         return;
      }

      $date = strtotime($event_start_date);

      return esc_html(date_i18n($date_format, $date));
   }

   public function get_related_posts_IDs()
   {
      $related_posts = get_field('related_posts', $this->ID);

      if (empty($related_posts)) {
         $related_posts = [];
      }

      if (count($related_posts) < 4) {
         $additional_posts_count = 4 - count($related_posts);

         $categories =  $this->get_post_terms('category');

         $category_ids = array_map(function ($term) {
            return $term->term_id;
         }, $categories);

         $posts_not_in = array_merge([$this->ID], $related_posts);

         $args = [
            'post_type'      => ['post'],
            'posts_per_page' => $additional_posts_count,
            'fields'         => 'ids',
            'post__not_in'   => $posts_not_in,
            'category__in'   => $category_ids
         ];

         $additional_posts = get_posts($args);
         $related_posts = array_merge($related_posts, $additional_posts);
      } else {
         $related_posts = array_slice($related_posts, 0, 4);
      }

      return $related_posts;
   }

   public function has_download_material()
   {
      return !empty(PostUtils::get_download_material_signup_link());
   }
}
