import Loading_Spinner from '../components/loading_spinner'
import handleFileDownload from '../utils/handle-download'
import {setCookie} from '../utils/manage-cookie'

document.addEventListener('DOMContentLoaded', () => {
   const formNewsletter = document.querySelector('.form-newsletter')

   if (!formNewsletter) {
      return
   }

   document.querySelectorAll('select').forEach((select) => {
      let firstOption = select.options[0]

      if (firstOption && !firstOption.hasAttribute('value')) {
         firstOption.setAttribute('value', '')
      }
   })

   const formElements = {
      wrapper: formNewsletter.querySelector('.form-wrapper'),
      content: formNewsletter.querySelector('.form-newsletter-content'),
      successMessage: formNewsletter.querySelector('.form-successful-message'),
      downloadMessage: formNewsletter.querySelector('.form-download-message'),
      loadingSpinnerSelector: '.loading-spinner',
      headerPicture: document.querySelector('.template-form-newsletter-header'),
   }

   const wpcf7Form = formElements.wrapper?.querySelector('.wpcf7-form')

   const directDownloadButton = formNewsletter.querySelector('.direct-download-button')

   if (!formElements.wrapper || !wpcf7Form) {
      return
   }

   if (!leadFormData || typeof leadFormData !== 'object') {
      return
   }

   if (!document.querySelector(formElements.loadingSpinnerSelector)) {
      return
   }

   if (leadFormData.hasOwnProperty('bx_cd_form_element_id') && leadFormData.bx_cd_form_element_id) {
      wpcf7Form.setAttribute('id', leadFormData.bx_cd_form_element_id)
   }

   let spinner
   const bx_cd_other_field_prefix = 'bx_cd_other_'
   const bx_cd_checkbox_group = 'bx_cd_checkbox_group_'

   spinner = new Loading_Spinner(formElements.loadingSpinnerSelector)

   const urlParams = new URLSearchParams(window.location.search)

   const contentName = urlParams.get('ContentName')
   const sanitizedContentName = contentName ? decodeURIComponent(contentName) : ''
   const contentType = contentName ? 'E-Book' : 'Newsletter'

   let downloadFileId = urlParams.get('download-id')

   if (downloadFileId && !isNaN(downloadFileId) && Number(downloadFileId) > 0) {
      const submitButton = wpcf7Form.querySelector('.wpcf7-submit')
      if (submitButton) {
         submitButton.value = messages.download_document
      }
   }

   wpcf7Form.addEventListener('reset', function (e) {
      e.preventDefault()
   })

   document.querySelectorAll('.wpcf7-validates-as-required').forEach((input) => {
      const label = input.closest('p').querySelector('label')
      if (label) {
         if (label.closest('.wpcf7-list-item')) return

         const asterisk = document.createElement('span')
         asterisk.textContent = '*'
         asterisk.classList.add('required-input')

         const inputWrapper = label.querySelector('.wpcf7-form-control-wrap')
         if (inputWrapper) {
            label.insertBefore(asterisk, inputWrapper)
         } else {
            label.appendChild(asterisk)
         }
      }
   })

   let hiddenTrackingFields = {
      elqCustomerGUID: leadFormData.bx_cd_eloqua_customer_id,
      elqCookieWrite: '0',
      Brand: leadFormData.bx_cd_brand,
      Content: sanitizedContentName,
      AssetType: contentType,
   }

   Object.entries(hiddenTrackingFields).forEach(([name, value]) => {
      const input = Object.assign(document.createElement('input'), {
         type: 'hidden',
         name,
         value,
      })
      wpcf7Form.appendChild(input)
   })

   if (directDownloadButton) {
      directDownloadButton.addEventListener('click', async (e) => {
         e.preventDefault()
         spinner.show()

         try {
            await handleFileDownload(downloadFileId)
         } catch {
            showToast(messages.download_error, false)
         }

         spinner.hide()
      })
   }

   const handleFormEvent = (eventName, callback) => {
      wpcf7Form.addEventListener(eventName, callback, false)
   }

   handleFormEvent('wpcf7mailfailed', () => {
      showToast(messages.newsletter_signup_failed, false)
   })

   handleFormEvent('wpcf7spam', () => {
      showToast(messages.newsletter_signup_spam, false)
   })

   handleFormEvent('wpcf7mailsent', () => {
      spinner.show()

      let formdata = new FormData()

      formdata.append('elqSiteId', leadFormData.elqSiteId)
      formdata.append('elqFormName', leadFormData.elqFormName)
      formdata.append('elqCampaignId ', '')

      Object.entries(hiddenTrackingFields).forEach(([name, value]) => {
         const inputElement = wpcf7Form.querySelector(`[name="${name}"]`)

         if (inputElement) {
            formdata.append(name, inputElement.value)
         } else {
            formdata.append(name, '')
         }
      })

      wpcf7Form
         ?.querySelectorAll('.wpcf7-form-control[name]:not([type="checkbox"]):not([type="radio"])')
         .forEach((input) => {
            formdata.append(input.name.replace(/\[\]$/, ''), input.value)
         })

      const checkboxes = {}
      const radios = {}

      wpcf7Form
         ?.querySelectorAll(
            '.wpcf7-checkbox input[type="checkbox"]:checked, .wpcf7-acceptance input[type="checkbox"]:checked',
         )
         .forEach((checkbox) => {
            let name = checkbox.name.replace(/\[\]$/, '')

            if (name.includes(bx_cd_checkbox_group) && checkbox.checked) {
               let checkboxValueParts = checkbox.value.split('_to_')

               if (checkboxValueParts.length === 2) {
                  formdata.append(checkboxValueParts[0], checkboxValueParts[1])
               }
            } else {
               if (!checkboxes[name]) {
                  checkboxes[name] = []
               }
               checkboxes[name].push(checkbox.value)
            }
         })

      wpcf7Form?.querySelectorAll('.wpcf7-radio input[type="radio"]:checked').forEach((radio) => {
         let name = radio.name
         radios[name] = radio.value
      })

      Object.keys(checkboxes).forEach((name) => {
         formdata.append(name, checkboxes[name].join(','))
      })

      Object.keys(radios).forEach((name) => {
         formdata.append(name, radios[name])
      })

      wpcf7Form
         ?.querySelectorAll('div[data-class="wpcf7cf_group"]:not(.wpcf7cf-hidden) .wpcf7-form-control[name]')
         .forEach((input) => {
            if (input.name.includes(bx_cd_other_field_prefix)) {
               const key = input.name.replace(bx_cd_other_field_prefix, '')
               const existingInput = wpcf7Form.querySelectorAll(`input[name="${key}[]"]`)[0]
               if (existingInput && existingInput instanceof HTMLInputElement) {
                  if (existingInput.getAttribute('type') === 'checkbox') {
                     const existingValue = formdata.get(key) || ''
                     formdata.delete(key)
                     formdata.append(key, existingValue ? existingValue + ',' + input.value : input.value)
                  }
               } else {
                  formdata.delete(key)
                  formdata.append(key, input.value)
               }
            }
         })

      const requestOptions = {
         method: 'POST',
         body: formdata,
         redirect: 'follow',
      }

      fetch(leadFormData.bx_cd_eloqua_endpoint, requestOptions)
         .then((response) => response.text())
         .then(async (result) => {
            if (result.includes('problem') || result.includes('error')) {
               spinner.hide()
               showToast(messages.newsletter_signup_failed, false)
            } else {
               setCookie('bx_cd_has_newsletter_signup', true, 7)

               gtag('event', 'preencheu_formulario', {
                  content_type: contentType,
               })

               if (downloadFileId) {
                  formElements.downloadMessage.classList.remove('hidden')
                  spinner.show()
                  try {
                     formElements.content.remove()
                     formElements.headerPicture.classList.add('hidden')
                     window.scrollTo({top: 0, behavior: 'smooth'})
                     await handleFileDownload(downloadFileId)
                  } catch {
                     showToast(messages.download_error, false)
                  }
                  spinner.hide()
               } else {
                  spinner.hide()
                  formElements.headerPicture.classList.add('hidden')
                  formElements.successMessage.classList.remove('hidden')
                  formElements.content.remove()
                  window.scrollTo({top: 0, behavior: 'smooth'})
               }
            }
         })
         .catch((error) => {
            spinner.hide()
            showToast(messages.newsletter_signup_failed, false)
         })
   })
})
