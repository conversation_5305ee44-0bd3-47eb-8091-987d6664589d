<?php

if (!defined('ABSPATH')) {
   echo 'Inicie WordPress';

   exit;
}

if (!class_exists('bx_sntls_Utils')) {
   echo 'Instale BX Essentials';

   return;
}

require implode(DIRECTORY_SEPARATOR, [__DIR__, 'includes', 'hooks', '_index.php']);
require implode(DIRECTORY_SEPARATOR, [__DIR__, 'includes', 'global', '_index.php']);

bx_sntls_Utils::load_class('AutoLoader');

$loader = new bx_sntls_AutoLoader();
$loader->add_namespace('CanaisDigitais', get_theme_file_path('includes/global'));
$loader->add_namespace('CanaisDigitais', get_theme_file_path('includes/entities'));
$loader->add_namespace('CanaisDigitais', get_theme_file_path('includes/generic'));

new \CanaisDigitais\CD_Post\Register();
new \CanaisDigitais\Theme_Settings\Register();
new \CanaisDigitais\Theme_Settings\Register_General_Options();
new \CanaisDigitais\Theme_Settings\Register_Homepage_Tabs();
new \CanaisDigitais\Theme_Settings\Register_Mega_Menu();
new \CanaisDigitais\Theme_Settings\Register_Tags_Pixels();
new \CanaisDigitais\Theme_Settings\Register_CSV_Importer();
new \CanaisDigitais\Blocks\Register();
new \CanaisDigitais\Category\Register();
new \CanaisDigitais\Tag\Register();
new \CanaisDigitais\Ad\Register();
new \CanaisDigitais\Author\Register();
new \CanaisDigitais\Head_Hooks();
new \CanaisDigitais\Form\Register();
new \CanaisDigitais\Page\Register();
new \CanaisDigitais\Story\Register();

if (function_exists('is_environment')) {
   if (\is_environment(['staging', 'production'])) {
      new \CanaisDigitais\Mail\Register();
   }
}
