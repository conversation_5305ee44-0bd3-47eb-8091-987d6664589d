<?php

if (!defined('ABSPATH')) {
   exit;
}

// TODO: update with essential resource

global $wp_query;

$custom_args['total']   = isset($args['total']) ? $args['total'] : $wp_query->max_num_pages;
$custom_args['current'] = isset($args['current']) ? $args['current'] : max(1, get_query_var('paged'));

$default_args = [
   'type'               => 'array',
   'mid_size'           => 2,
   'before_page_number' => '',
   'prev_next'          => true,
   'prev_text'          => '<span class="">' . esc_html__('Anterior', 'canais_digitais') . '</span>',
   'next_text'          => '<span class="">' . esc_html__('Próxima', 'canais_digitais') . '</span>',
];

$paginate_links = paginate_links(wp_parse_args($custom_args, $default_args));

if (!empty($paginate_links)) {

?>
   <nav class="pagination flex justify-center">
      <ul class="flex gap-x-2">
         <?php

         foreach ($paginate_links as $key => $link) {
            if (strpos($link, 'current') !== false) {
               $link = '<li class="current"><a href="' . get_pagenum_link($custom_args['current']) . '">' . $link . '</a></li>';
            }

         ?>
            <li><?php echo $link; ?></li>
         <?php

         }

         ?>
      </ul>
   </nav>
<?php

}
