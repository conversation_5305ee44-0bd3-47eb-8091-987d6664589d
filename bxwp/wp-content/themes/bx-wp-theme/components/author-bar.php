<?php

if (!defined('ABSPATH')) {
   exit;
}

use <PERSON>ais<PERSON><PERSON>tais\Author\Author;
use Canais<PERSON><PERSON>tais\CD_Post\CD_Post;
use Canais<PERSON><PERSON>tais\Page\Utils as PageUtils;

$is_archive = isset($args['is_archive']) ? $args['is_archive'] : false;

if ($is_archive === true) {

   $author_tax = [];
   $author_object = get_queried_object();
   $author_tax[0] = $author_object->term_id;

   $wrapper_classes     = 'p-12 flex-col gap-6 lg:flex-row lg:p-0 lg:gap-10';
   $container_classes   = '';
   $thumbnail_classes   = 'w-full aspect-square lg:w-[20rem] lg:h-[21.5rem]';
   $title_classes       = 'text-2xl leading-[3.5rem] lg:text-5xl';
   $description_classes = 'max-w-[53.875rem] text-sm leading-[1.375rem] line-clamp-3 lg:leading-7 lg:text-xl';
} else {

   $CD_Post    = new CD_Post();

   $author_tax = [];
   $author_list = wp_get_post_terms($CD_Post->get_the_ID(), 'author_tax');

   foreach ($author_list as $term) {
      $author_tax[] = $term->term_id;
   }

   $wrapper_classes     = 'gap-4 max-w-[55rem]';
   $container_classes   = 'justify-center';
   $thumbnail_classes   = 'w-28 h-28 lg:w-40 lg:h-40';
   $title_classes       = 'text-base lg:text-2xl';
   $description_classes = 'text-sm line-clamp-3 lg:text-base';
}


if (!empty($author_tax)) {

   foreach ($author_tax as $key => $author_id) {

      $author             = new Author($author_id);
      $author_name        = $author->get_name();
      $author_link        = $author->get_link();
      $author_description = $author->get_description();
      $author_type        = $author->get_author_type();

      if ($author_type === 'specialist') {

?>
         <section class="py-8 bg-neutral-200 <?php echo $key != 0 ? 'pt-0' : ''; ?>">
            <div class="container flex <?php echo esc_attr($container_classes); ?>">
               <div class="flex items-start <?php echo esc_attr($wrapper_classes); ?>">
                  <div class="shrink-0 h-[108px] aspect-[108/108] lg:h-[164px] lg:aspect-[164/164]">
                     <?php

                     if (!empty($author->get_avatar())) {

                        echo $author->get_avatar('thumbnail', [
                           'class' => 'w-full aspect-square object-cover object-center'
                        ]);
                     }

                     ?>
                  </div>
                  <div>
                     <?php

                     if ($is_archive === true) {

                        $specialist_template_page = PageUtils::get_specialist_archive_page();

                        if (!empty($specialist_template_page)) {
                           $Page                     = new CD_Post($specialist_template_page);
                        }

                        if (!empty($Page)) {

                     ?>
                           <div class="flex items-center gap-2">
                              <div class="text-neutral-700 text-xs font-medium uppercase leading-none">
                                 <a href="<?php echo esc_url($Page->get_link()); ?>">
                                    <?php

                                    esc_html_e('Especialistas', 'canais_digitais');

                                    ?>
                                 </a>
                              </div>
                              <span class="w-3 bg-neutral-400 icon-[ph-caret-right] [mask-size:0.7rem] [mask-position:center]"></span>
                              <div class="text-neutral-700 text-xs font-medium uppercase leading-none">
                                 <?php

                                 echo esc_html($author_name);

                                 ?>
                              </div>
                           </div>
                     <?php

                        }
                     }

                     ?>
                     <div class=" text-neutral-800 lg:mb-3">
                        <span class="<?php echo esc_attr($title_classes); ?>">
                           <?php

                           echo esc_html($author_name);

                           ?>
                        </span>
                     </div>
                     <?php

                     if (!empty($author_description)) {

                     ?>
                        <p class="mb-3 text-neutral-600" x-data>
                           <span class="author-description <?php echo esc_attr($description_classes); ?>" x-ref="authorBio">
                              <?php

                              echo $author_description;

                              ?>
                           </span>
                           <?php

                           if (strlen($author_description) >= 250) {

                           ?>
                              <a class="font-semibold" href="#" x-on:click.prevent="$refs.authorBio.classList.remove('line-clamp-3');$el.remove()">Ver completo</a>
                           <?php

                           }

                           ?>
                        </p>
                     <?php

                     }

                     if ($is_archive !== true) {

                     ?>
                        <a href="<?php echo esc_url($author_link); ?>" class="text-primary-500 text-xs uppercase font-medium hover:underline">
                           <?php

                           esc_html_e('Ver Artigos', 'canais_digitais');

                           ?>
                        </a>
                     <?php

                     }

                     ?>
                  </div>
               </div>
            </div>
         </section>
<?php
      }
   }
}
