<?php

if (!defined('ABSPATH')) {
   exit;
}

use Canais<PERSON><PERSON>tais\CD_Post\CD_Post;
use Canais<PERSON><PERSON>tais\Page\Utils as PageUtils;
use CanaisDigitais\Form\Utils as FormUtils;
use CanaisDigitais\Category\Category;

if (!FormUtils::is_lead_form_enabled()) {
   return;
}

bx_sntls_Utils::load_class('Form');

if (!class_exists('bx_sntls_Form')) {
   return;
}

$form_newsletter_page = PageUtils::get_form_newsletter_page();
$Page                 = new CD_Post($form_newsletter_page);

if (empty($Page)) {
   return;
}

$category_ID = $args['category_id'] ?? 0;

if (!empty($category_ID)) {
   $Category        = new Category($category_ID);
   $newsletter_data = $Category->get_special_page_newsletter();
} else {
   $newsletter_data = [
      'layout' => get_field('homepage_newsletter_layout', 'homepage-options'),
      'title'  => get_field('homepage_newsletter_title', 'homepage-options'),
      'text'   => get_field('homepage_newsletter_text', 'homepage-options'),
      'image'  => get_field('homepage_newsletter_image', 'homepage-options'),
   ];
}

$layout = $newsletter_data['layout'] ?? "text";
$title  = $newsletter_data['title'] ?? "";
$text   = $newsletter_data['text'] ?? "";
$image  = $newsletter_data['image'] ?? "";

$section_classes        = "py-8 bg-neutral-300";
$background_style       = "";
$content_classes        = "flex flex-col gap-4";
$text_container_classes = "flex flex-col gap-4 max-w-3xl";
$text_color             = "text-neutral-700";

if ($layout === "background" && !empty($image)) {
   $section_classes         = "pt-40 pb-8 lg:pt-8 bg-center bg-cover";
   $background_style        = 'style="background-image: url(' . esc_url($image) . ')"';
   $content_classes        .= " items-start";
   $text_container_classes .= " bg-[#616161]/80 backdrop-blur-sm p-8 text-left";
   $text_color              = "text-white";
} elseif ($layout === 'image_text' && !empty($image)) {
   $section_classes        .= " relative md:py-16";
   $text_container_classes .= " md:w-3/5 order-last md:order-none";
} else {
   $text_container_classes .= " items-center text-center mx-auto";
}

?>
<section x-cloak x-data="newsletterSignUpCheck" x-show="!isCookieSet" class="<?php echo esc_attr($section_classes); ?>" <?php echo $background_style; ?>>
   <div class="container <?php echo esc_attr($content_classes); ?>">
      <div class="<?php echo esc_attr($text_container_classes); ?>">
         <h2 class="text-[32px] lg:text-[40px] leading-[40px] lg:leading-[48px] tracking-tight <?php echo esc_attr($text_color); ?>">
            <?php echo esc_html($title); ?>
         </h2>

         <p class="<?php echo esc_attr($text_color); ?>">
            <?php echo esc_html($text); ?>
         </p>

         <a href="<?php echo esc_url($Page->get_link()); ?>" class="button-primary w-fit !px-4 !py-2 border-none rounded !text-white transition-all duration-500 ease-in-out hover:!bg-primary-300">
            <?php esc_html_e('ASSINE A NOSSA NEWSLETTER', 'canais-digitais'); ?>
         </a>
      </div>

      <?php

      if ($layout === 'image_text' && !empty($image)) {

      ?>
         <img src="<?php echo esc_url($image); ?>" alt="<?php echo esc_attr($title); ?>" class="relative md:absolute md:top-0 md:right-0 w-full aspect-[375/210] md:w-1/3 md:aspect-[476/268] md:h-full object-cover order-first md:order-none">
      <?php

      }

      ?>
   </div>
</section>
