<?php

if (!defined('ABSPATH')) {
   exit;
}

$articles = $args['articles'] ?? [];
$text = $args['text'] ?? '';
$label = $args['label'] ?? '';

?>

<div class="container font-primary my-10 py-4 max-w-[69rem] border-y border-neutral-200">
   <div class="flex flex-col gap-6 lg:gap-7">
      <div>
         <?php

         if (!empty($text) || !empty($label)) {
            echo sprintf(
               '<span class="text-neutral-500 text-xs font-medium uppercase leading-5">%s <b class="text-neutral-700">%s</b></span>',
               $text,
               $label,
            );
         }

         ?>
      </div>
      <div class="grid grid-cols-1 gap-y-8 gap-x-4 xl:grid-cols-2">
         <?php

         foreach ($articles as $article) {
            get_component('simple-horizontal-compact-card', [
               'cd_post'           => $article,
               'wrapper_classes'   => 'h-[6.5rem] flex',
               'thumbnail_classes' => "flex w-[6.5rem] h-full aspect-square max-w-none",
            ]);
         }

         ?>
      </div>
   </div>
</div>
