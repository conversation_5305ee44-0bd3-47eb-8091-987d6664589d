<?php

use CanaisDigitais\CD_Post\Utils as PostUtils;
use CanaisDigitais\Author\Author;

if (!defined('ABSPATH')) {
   exit;
}

$author_id = get_field('block_author_id');

if (empty($author_id)) {
   return;
}

$Author           = new Author($author_id);
$show_most_recent = get_field('block_show_most_recent');
$articles         = get_field('block_articles');
$current_post_id  = get_the_ID() ?  get_the_ID() : '';

if (!empty($articles) && empty($show_most_recent)) {
   $articles = array_column($articles, 'block_single_article');
} else {
   $articles = PostUtils::get_latest_articles_by_author_term_id($author_id, 2, [$current_post_id]);
}

if (empty($articles)) {
   return;
}

get_component('read-more-grid', [
   'articles' => $articles,
   'label'    => $Author->get_name(),
   'text'     => __('Leia mais de', 'canais_digitais'),
]);
