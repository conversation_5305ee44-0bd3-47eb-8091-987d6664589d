<?php

if (!defined('ABSPATH')) {
   exit;
}

use CanaisDigitais\Category\Category;
use CanaisDigitais\CD_Post\Utils as PostUtils;

global $post;

$select_type = get_field('block_read_more_category_select_type');

$posts_list = [];

if ($select_type === 'category') {
   $category_id = get_field('block_read_more_category_taxonomy');

   if (empty($category_id)) {
      return;
   }

   $Category = new Category($category_id);

   $posts_list = PostUtils::get_latest_posts_by_category_id($Category->get_the_ID(), 2, [$post->ID]);
} else {
   $posts_list = get_field('block_read_more_category_posts');
}

if (empty($posts_list)) {
   return;
}

get_component('read-more-grid', [
   'articles' => $posts_list,
   'text'     => $select_type === 'category' ? __('Leia mais sobre', 'canais_digitais') : __('<PERSON>ia mais:', 'canais_digitais'),
   'label'    => $select_type === 'category' ? $Category->get_name() : '',
]);
