<?php

if (!defined('ABSPATH')) {
   exit;
}

use Canais<PERSON><PERSON>tais\Ad\Ad;
use Canais<PERSON><PERSON>tais\Category\Utils as CategoryUtils;
use CanaisD<PERSON>tais\CD_Post\Utils;
use CanaisDigitais\CD_Post\CD_Post;

$category_ID = $args['category_id'] ?? 0;

if (!empty($category_ID)) {
   $slider_posts      = CategoryUtils::get_special_page_events_posts($category_ID, 5, true);
   $other_event_posts = CategoryUtils::get_special_page_events_posts($category_ID, 4);

   $basic_info = CategoryUtils::get_special_page_events_basic_info($category_ID);
} else {
   $slider_posts      = Utils::get_homepage_events_slider_posts();
   $other_event_posts = Utils::get_homepage_events_other_posts();

   $basic_info = Utils::get_homepage_events_basic_info();
}

$basic_info_title = !empty($basic_info['title']) ? esc_html($basic_info['title']) : esc_html__('Eventos', 'canais_digitais');
$basic_info_description = !empty($basic_info['description']) ? esc_html($basic_info['description']) : esc_html__('Confira os eventos mais recentes do site.', 'canais_digitais');

if (empty($slider_posts) && empty($other_event_posts)) {
   return;
}

?>
<div class="bg-neutral-200 py-6">
   <div class="container w-full">
      <div class="flex flex-col gap-4 border-b border-neutral-300 pb-12 lg:flex-row">
         <div class="w-full lg:w-1/2 lg:mt-16">
            <div class="flex flex-col max-w-[31.3125rem] gap-4">
               <h2 class="text-neutral-700 text-2xl font-normal leading-10 lg:text-6xl lg:leading-[4rem]">
                  <?php echo $basic_info_title; ?>
               </h2>

               <span class="text-neutral-500 text-base font-normal leading-8 lg:text-2xl">
                  <?php echo $basic_info_description; ?>
               </span>
            </div>
            <div class="mt-8">
               <a class="button button-primary" href="<?php echo esc_url(CategoryUtils::get_default_category_url('event')); ?>">
                  <?php

                  esc_html_e('Ver todas as notícias', 'canais_digitais');

                  ?>
               </a>
            </div>
         </div>
         <div class="w-full mt-6 lg:w-1/2 lg:mt-0">
            <?php

            if (!empty($slider_posts)) {

            ?>
               <div class="slider-home-events swiper">
                  <div class="swiper-wrapper">
                     <?php

                     foreach ($slider_posts as $slide) {
                        $Event         = new CD_Post($slide);
                        $has_thumbnail = $Event->has_thumbnail();
                        $category      = $Event->get_category();

                     ?>
                        <div class="group swiper-slide relative">
                           <a class="flex w-full aspect-[343/180] lg:aspect-[656/420]" href="<?php echo esc_url($Event->get_link()); ?>">
                              <div class="flex w-full h-full relative default-animation ">
                                 <div class="absolute w-full h-full overflow-hidden">
                                    <div class="z-[2] absolute w-full h-full bg-black/48"></div>
                                    <div class="w-full h-full group-hover:blur-md default-animation scale-110">
                                       <?php

                                       if ($has_thumbnail) {
                                          echo $Event->get_thumbnail('medium_large', [
                                             'class' => 'w-full h-full object-cover'
                                          ]);
                                       }

                                       ?>
                                    </div>
                                 </div>
                                 <div class="z-[3] flex flex-col justify-end w-full h-full pb-12 px-8 text-white">
                                    <?php

                                    if (!empty($category)) {

                                    ?>
                                       <span class="mb-2 text-white text-xs font-medium uppercase leading-5">
                                          <?php echo esc_html($category->get_name()); ?>
                                       </span>
                                    <?php

                                    }

                                    ?>
                                    <span class="mb-4 text-white text-base lg:text-3xl font-normal leading-6 lg:leading-10 line-clamp-3">
                                       <?php echo esc_html($Event->get_title()); ?>
                                    </span>
                                    <?php

                                    if ($Event->get_event_start_date()) {

                                    ?>
                                       <span class="text-white text-base font-normal leading-normal">
                                          <?php echo esc_html($Event->get_event_start_date()); ?>
                                       </span>
                                    <?php

                                    }

                                    ?>
                                 </div>
                              </div>
                           </a>
                        </div>
                     <?php

                     }

                     ?>
                  </div>
                  <div class="flex justify-between gap-4 items-center mt-6 lg:justify-end">
                     <div class="slider-pagination"></div>
                     <div class="flex gap-3">
                        <div class="slider-button slider-button-prev">
                           <span class="slider-button-arrow icon-[ph-caret-left] [mask-size:1.5rem] [mask-position:center]"></span>
                        </div>
                        <div class="slider-button slider-button-next">
                           <span class="slider-button-arrow icon-[ph-caret-right] [mask-size:1.5rem] [mask-position:center]"></span>
                        </div>
                     </div>
                  </div>
               </div>
            <?php

            }

            ?>
         </div>
      </div>
      <div class="block">
         <?php

         if (!empty($other_event_posts)) {

         ?>
            <div class="mt-4 mb-8">
               <span class="text-neutral-700 text-xl font-normal leading-7">
                  <?php esc_html_e('Acompanhe mais notícias sobre os eventos', 'canais_digitais'); ?>
               </span>
            </div>
            <div class="grid gap-6 grid-cols-1 w-full lg:grid-cols-4 lg:gap-4">
               <?php

               foreach ($other_event_posts as $event) {
                  $Event = new CD_Post($event);

                  get_component('simple-vertical-card', [
                     'article' => $event,
                     'date'    => $Event->get_event_start_date('d \d\e F, Y')
                  ]);
               }

               ?>
            </div>
         <?php

         }

         ?>
      </div>
   </div>
</div>
<?php

$ad_list = [
   'nativekey_main_ge4',
   'nativekey_main_ge5',
   'nativekey_main_ge6',
   'nativekey_main_ge7',
];

?>
<div class="container flex flex-col lg:flex-row lg:justify-center">
   <?php

   foreach ($ad_list as $ad) {
      new Ad($ad, 'my-4');
   }

   ?>
</div>
