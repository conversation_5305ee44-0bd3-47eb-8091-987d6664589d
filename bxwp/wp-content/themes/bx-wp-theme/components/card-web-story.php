<?php

if (!defined('ABSPATH')) {
   exit;
}

use CanaisDigitais\CD_Post\CD_Post;
use Canais<PERSON>igitais\Category\Category;

$Web_Story = new CD_Post($args['post_ID'] ?? get_the_ID());

$categories = $Web_Story->get_terms('web_story_category');
$category   = !empty($categories) && !is_wp_error($categories) ? $categories[0] : null;

?>

<article class="card-web-story flex relative overflow-hidden [transform:translateZ(0)] w-full rounded-lg mb-0">
   <a class="ga-webstory-interaction flex flex-col justify-between w-full py-[1.31rem] px-[1.81rem]" href="<?php echo esc_url($Web_Story->get_link()); ?>" target="_blank">
      <figure class="absolute top-0 left-0 w-full h-full overflow-hidden">
         <?php

         if ($Web_Story->has_thumbnail()) {

         ?>
            <?php

            echo $Web_Story->get_thumbnail('web-stories', [
               'class' => 'block w-full h-full object-cover transition-all duration-500',
            ]);

            ?>
         <?php

         }

         ?>
      </figure>

      <div class="relative z-10 flex flex-col items-start w-full">
         <?php

         if (!empty($category)) {
            $Category = new Category($category);

         ?>
            <span class="flex py-[0.375rem] px-4 bg-primary-500 items-center justify-center rounded-full text-white text-sm font-semibold uppercase">
               <?php echo $Category->get_name(); ?>
            </span>
         <?php

         }

         ?>
      </div>

      <div class="relative z-10 flex flex-col justify-center w-full">
         <h3 class="mb-4 text-xl font-medium leading-6 text-white">
            <?php echo esc_html($Web_Story->get_title()) ?>
         </h3>

         <?php

         if (!empty($Web_Story->has_excerpt())) {

         ?>
            <p class="excerpt transition-all duration-300 leading-5 max-h-0 overflow-hidden opacity-0 text-white">
               <span class="block pb-6">
                  <?php echo $Web_Story->get_excerpt(); ?>
               </span>
            </p>
         <?php

         }

         ?>
      </div>
   </a>
</article>
