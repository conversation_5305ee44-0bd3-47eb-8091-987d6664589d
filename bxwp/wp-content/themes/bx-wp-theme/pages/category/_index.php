<?php

if (!defined('ABSPATH')) {
   exit;
}

use CanaisDigitais\Category\Category;
use CanaisDigitais\Ad\Ad;

$Category = new Category(get_queried_object_id());

$category_type   = $Category->get_type();
$is_special_page = $Category->is_special_page();

get_component('header');

if ($is_special_page) {
   get_component('sponsors', [
      'category_id' => $Category->get_the_ID()
   ]);
}

get_component('page-header');

?>
<div>
   <?php

   new Ad('728_1_a');

   new Ad('sponsoredlogo');

   ?>
</div>
<?php

if ($is_special_page) {
   get_page_component('category', 'special-page');
}

if (!$is_special_page && $category_type === 'video') {
   get_page_component('category', 'video-archive');
}

if (!$is_special_page && $category_type !== 'video' && $Category->is_parent()) {
   get_page_component('category', 'parent-category-archive');
}

if (!$is_special_page && $category_type !== 'video' && !$Category->is_parent()) {
   get_page_component('category', 'subcategory-archive');
}

?>
<div>
   <?php

   new Ad('adhension');

   ?>
</div>
<?php

get_component('footer');
